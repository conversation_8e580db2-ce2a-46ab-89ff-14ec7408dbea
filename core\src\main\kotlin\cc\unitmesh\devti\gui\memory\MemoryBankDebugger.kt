package cc.unitmesh.devti.gui.memory

import cc.unitmesh.devti.memory.MemoryBankService
import cc.unitmesh.devti.memory.MemorySummary
import com.intellij.openapi.project.Project
import javax.swing.*
import java.awt.BorderLayout
import java.awt.Dimension

/**
 * 记忆银行调试器 - 用于调试 Markdown 渲染问题
 */
object MemoryBankDebugger {
    
    fun debugMarkdownRendering(project: Project) {
        println("=== 记忆银行 Markdown 渲染调试 ===")
        
        val memoryBankService = MemoryBankService.getInstance(project)
        
        // 创建测试记忆
        val testMemory = MemorySummary(
            id = "debug-test",
            title = "调试测试记忆",
            content = """
                # 调试测试
                
                这是一个用于调试的**测试记忆**。
                
                ## 代码示例
                ```kotlin
                fun debugTest() {
                    println("Debug test")
                }
                ```
                
                ## 列表
                - 项目 1
                - 项目 2
                - 项目 3
            """.trimIndent(),
            category = "debug",
            tags = listOf("调试", "测试"),
            importance = 3
        )
        
        println("创建测试记忆: ${testMemory.title}")
        println("内容长度: ${testMemory.content.length}")
        
        try {
            // 测试 MarkdownMemoryCard 创建
            println("开始创建 MarkdownMemoryCard...")
            
            SwingUtilities.invokeAndWait {
                try {
                    val memoryCard = MarkdownMemoryCard(project, testMemory) { updatedMemory ->
                        println("记忆更新回调: ${updatedMemory.title}")
                    }
                    
                    println("MarkdownMemoryCard 创建成功")
                    println("组件数量: ${memoryCard.componentCount}")
                    
                    // 检查组件结构
                    for (i in 0 until memoryCard.componentCount) {
                        val component = memoryCard.getComponent(i)
                        println("组件 $i: ${component.javaClass.simpleName}")
                    }
                    
                    // 创建测试窗口（可选）
                    if (System.getProperty("debug.show.window") == "true") {
                        showDebugWindow(memoryCard)
                    }
                    
                } catch (e: Exception) {
                    println("创建 MarkdownMemoryCard 失败: ${e.message}")
                    e.printStackTrace()
                }
            }
            
        } catch (e: Exception) {
            println("调试过程中发生错误: ${e.message}")
            e.printStackTrace()
        }
        
        println("=== 调试完成 ===")
    }
    
    private fun showDebugWindow(memoryCard: MarkdownMemoryCard) {
        SwingUtilities.invokeLater {
            val frame = JFrame("记忆银行调试窗口")
            frame.defaultCloseOperation = JFrame.DISPOSE_ON_CLOSE
            frame.layout = BorderLayout()
            
            // 添加记忆卡片
            frame.add(memoryCard, BorderLayout.CENTER)
            
            // 添加调试信息面板
            val debugPanel = JPanel()
            debugPanel.layout = BoxLayout(debugPanel, BoxLayout.Y_AXIS)
            debugPanel.add(JLabel("调试信息:"))
            debugPanel.add(JLabel("组件数量: ${memoryCard.componentCount}"))
            debugPanel.add(JLabel("首选大小: ${memoryCard.preferredSize}"))
            
            frame.add(debugPanel, BorderLayout.SOUTH)
            
            frame.size = Dimension(800, 600)
            frame.setLocationRelativeTo(null)
            frame.isVisible = true
            
            println("调试窗口已显示")
        }
    }
    
    fun testMemoryBankDialog(project: Project) {
        println("=== 测试记忆银行对话框 ===")
        
        try {
            SwingUtilities.invokeLater {
                val dialog = MemoryBankDialog(project)
                dialog.show()
                println("记忆银行对话框已显示")
            }
        } catch (e: Exception) {
            println("显示记忆银行对话框失败: ${e.message}")
            e.printStackTrace()
        }
    }
    
    fun checkMarkdownPreviewComponent(project: Project) {
        println("=== 检查 MarkdownPreviewHighlightSketch 组件 ===")
        
        val testContent = """
            # 测试标题
            
            这是一个**测试**内容。
            
            ```kotlin
            fun test() = "Hello"
            ```
        """.trimIndent()
        
        try {
            SwingUtilities.invokeAndWait {
                try {
                    val markdownPreview = cc.unitmesh.devti.sketch.ui.MarkdownPreviewHighlightSketch(project, testContent)
                    println("MarkdownPreviewHighlightSketch 创建成功")
                    
                    val component = markdownPreview.getComponent()
                    println("组件类型: ${component.javaClass.simpleName}")
                    println("组件大小: ${component.preferredSize}")
                    println("组件可见: ${component.isVisible}")
                    
                    // 测试在窗口中显示
                    if (System.getProperty("debug.show.markdown") == "true") {
                        val frame = JFrame("Markdown 预览测试")
                        frame.add(component)
                        frame.size = Dimension(600, 400)
                        frame.defaultCloseOperation = JFrame.DISPOSE_ON_CLOSE
                        frame.setLocationRelativeTo(null)
                        frame.isVisible = true
                        println("Markdown 预览窗口已显示")
                    }
                    
                } catch (e: Exception) {
                    println("创建 MarkdownPreviewHighlightSketch 失败: ${e.message}")
                    e.printStackTrace()
                }
            }
        } catch (e: Exception) {
            println("检查 Markdown 组件失败: ${e.message}")
            e.printStackTrace()
        }
    }
    
    fun runAllDebugTests(project: Project) {
        println("=== 运行所有调试测试 ===")
        
        checkMarkdownPreviewComponent(project)
        Thread.sleep(1000)
        
        debugMarkdownRendering(project)
        Thread.sleep(1000)
        
        // 可选：显示对话框
        if (System.getProperty("debug.show.dialog") == "true") {
            testMemoryBankDialog(project)
        }
        
        println("=== 所有调试测试完成 ===")
    }
}
