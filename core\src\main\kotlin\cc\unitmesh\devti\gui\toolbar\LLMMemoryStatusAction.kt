package cc.unitmesh.devti.gui.toolbar

import cc.unitmesh.devti.memory.intelligent.LLMMemoryIntegration
import cc.unitmesh.devti.memory.intelligent.MemoryProcessingMonitor
import com.intellij.icons.AllIcons
import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.ui.Messages

/**
 * LLM记忆状态显示Action
 */
class LLMMemoryStatusAction : AnAction(
    "LLM记忆状态",
    "查看LLM记忆增强和处理状态",
    AllIcons.General.InspectionsEye
) {

    override fun getActionUpdateThread(): ActionUpdateThread = ActionUpdateThread.BGT

    override fun update(e: AnActionEvent) {
        val project = e.project
        e.presentation.isEnabled = project != null
    }

    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return
        
        val integration = LLMMemoryIntegration.getInstance(project)
        val monitor = MemoryProcessingMonitor.getInstance(project)
        
        val config = integration.getConfiguration()
        val stats = monitor.getProcessingStatistics()
        val activeStates = monitor.getActiveProcessingStates()
        
        val statusMessage = buildString {
            append("# LLM记忆功能状态报告\n\n")
            
            // 配置状态
            append("## 🔧 当前配置\n")
            append("- **记忆存储**: ${if (config.memoryStorageEnabled) "✅ 已启用" else "❌ 已禁用"}\n")
            append("- **上下文增强**: ${if (config.contextEnrichmentEnabled) "✅ 已启用" else "❌ 已禁用"}\n")
            append("- **自动保存阈值**: ${config.autoSaveThreshold} 字符\n\n")
            
            // 处理统计
            append("## 📊 处理统计\n")
            append("- **总启动数**: ${stats.totalInitiated.get()}\n")
            append("- **总完成数**: ${stats.totalCompleted.get()}\n")
            append("- **总失败数**: ${stats.totalFailed.get()}\n")
            append("- **成功率**: ${"%.2f".format(stats.getSuccessRate())}%\n")
            append("- **平均处理时间**: ${stats.getAverageProcessingTime()}ms\n\n")
            
            // AI摘要统计
            append("## 🤖 AI摘要统计\n")
            append("- **成功生成**: ${stats.aiSummarySuccessCount.get()}\n")
            append("- **生成失败**: ${stats.aiSummaryFailureCount.get()}\n")
            append("- **成功率**: ${"%.2f".format(stats.getAISummarySuccessRate())}%\n\n")
            
            // 记忆银行统计
            append("## 🏦 记忆银行统计\n")
            append("- **保存成功**: ${stats.memoryBankSaveCount.get()}\n\n")
            
            // 活跃处理状态
            if (activeStates.isNotEmpty()) {
                append("## ⚡ 活跃处理状态\n")
                activeStates.forEach { state ->
                    val duration = System.currentTimeMillis() - state.startTime
                    append("- **${state.title}** (${state.memoryId})\n")
                    append("  - 当前阶段: ${state.currentStage}\n")
                    append("  - 处理时间: ${duration}ms\n")
                    append("  - AI摘要: ${if (state.aiSummaryGenerated) "✅" else "⏳"}\n")
                    append("  - 记忆银行: ${if (state.savedToMemoryBank) "✅" else "⏳"}\n\n")
                }
            } else {
                append("## ⚡ 活跃处理状态\n")
                append("当前没有活跃的记忆处理任务\n\n")
            }
            
            // 功能说明
            append("## 💡 功能说明\n")
            append("- **记忆存储**: 自动将重要的LLM对话保存为记忆\n")
            append("- **上下文增强**: 在LLM请求中自动添加相关记忆作为上下文\n")
            append("- **智能判断**: 基于内容长度、关键词、代码等判断对话重要性\n")
            append("- **AI摘要**: 为保存的对话生成智能摘要和标签\n")
            append("- **无缝集成**: 与现有记忆银行系统完全集成\n\n")
            
            // 使用建议
            append("## 🎯 使用建议\n")
            if (!config.memoryStorageEnabled && !config.contextEnrichmentEnabled) {
                append("- ⚠️ 建议启用记忆功能以获得更好的LLM体验\n")
            }
            if (config.contextEnrichmentEnabled && stats.totalInitiated.get() == 0L) {
                append("- 💡 开始使用LLM功能，系统会自动增强上下文\n")
            }
            if (config.memoryStorageEnabled && stats.memoryBankSaveCount.get() > 0) {
                append("- 📚 可以在记忆银行中查看保存的LLM对话\n")
            }
            if (stats.getAISummarySuccessRate() < 80.0 && stats.aiSummarySuccessCount.get() > 0) {
                append("- 🔧 AI摘要成功率较低，请检查大模型配置\n")
            }
        }
        
        Messages.showInfoMessage(project, statusMessage, "LLM记忆功能状态")
    }
}
