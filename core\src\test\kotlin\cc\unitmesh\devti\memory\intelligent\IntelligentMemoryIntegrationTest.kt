package cc.unitmesh.devti.memory.intelligent

import cc.unitmesh.devti.memory.MemoryBankService
import com.intellij.testFramework.LightPlatformTestCase
import kotlinx.coroutines.runBlocking

/**
 * 智能记忆系统集成测试
 * 
 * 该测试类验证智能记忆系统的核心功能，包括：
 * - 记忆的完整生命周期管理
 * - 上下文丰富和建议生成
 * - 记忆搜索和访问
 * - Markdown导出功能
 * - 批量记忆处理
 * - 异步操作支持
 * - 健康检查和配置管理
 */
class IntelligentMemoryIntegrationTest : LightPlatformTestCase() {
    // 初始化智能记忆门面和内存银行服务
    private lateinit var facade: IntelligentMemoryFacade
    private lateinit var memoryBankService: MemoryBankService

    // 测试前准备：获取单例实例并清空测试数据
    override fun setUp() {
        super.setUp()
        facade = IntelligentMemoryFacade.getInstance(project)
        memoryBankService = MemoryBankService.getInstance(project)
        
        // 清空测试数据
        memoryBankService.clearAllMemories()
    }

    // 测试记忆的完整生命周期：添加、丰富上下文、搜索、访问
    fun testCompleteMemoryLifecycle() = runBlocking {
        // 1. 添加新记忆
        val result = facade.addMemoryWithContext(
            title = "Kotlin 协程测试",
            content = """
                # Kotlin 协程基础
                
                协程是轻量级线程，可以暂停和恢复执行。
                
                ```kotlin
                suspend fun example() {
                    delay(1000)
                    println("Hello Coroutines!")
                }
                ```
            """.trimIndent(),
            source = "integration_test",
            context = mapOf(
                "test_case" to "lifecycle",
                "priority" to "high"
            )
        )
        
        assertTrue("记忆添加应该成功", result.success)
        assertNotNull("应该返回记忆ID", result.memoryId)
        
        // 2. 测试上下文丰富
        val enrichedContext = facade.enrichContext("Kotlin 协程 suspend")
        assertNotNull("丰富的上下文不应为空", enrichedContext)
        assertTrue("丰富的上下文应包含相关信息", enrichedContext.contains("协程"))
        
        // 3. 测试记忆搜索
        val searchResults = facade.searchMemories("Kotlin", 5)
        assertTrue("应该找到相关记忆", searchResults.isNotEmpty())
        assertEquals("应该找到刚添加的记忆", "Kotlin 协程测试", searchResults[0].title)
        
        // 4. 测试记忆访问
        val accessResult = facade.accessMemory(result.memoryId!!)
        assertNotNull("应该能访问记忆", accessResult)
        assertEquals("访问的记忆标题应该匹配", "Kotlin 协程测试", accessResult!!.title)
    }
    
    // 测试使用多个记忆进行上下文丰富
    fun testContextEnrichmentWithMultipleMemories() = runBlocking {
        // 添加多个相关记忆
        facade.addMemory("Java 线程", "Java 中的线程管理和同步机制")
        facade.addMemory("异步编程", "异步编程模式和最佳实践")
        facade.addMemory("并发控制", "并发控制的各种策略和工具")
        
        // 测试上下文丰富
        val request = ContextEnrichmentRequest(
            currentContext = "我需要了解并发编程的最佳实践",
            maxMemories = 3,
            relevanceThreshold = 0.2
        )
        
        val result = facade.enrichContextAdvanced(request)
        
        assertNotNull("丰富结果不应为空", result)
        assertTrue("应该使用了相关记忆", result.usedMemories.isNotEmpty())
        assertTrue("丰富的上下文应该比原始上下文更长", result.enrichedContext.length > request.currentContext.length)
        assertTrue("应该有建议", result.suggestions.isNotEmpty())
    }
    
    // 测试Markdown导出功能
    fun testMarkdownExport() = runBlocking {
        // 添加测试记忆
        facade.addMemory("测试记忆1", "这是第一个测试记忆的内容")
        facade.addMemory("测试记忆2", "这是第二个测试记忆的内容")
        
        // 等待记忆处理完成
        kotlinx.coroutines.delay(1000)
        
        // 测试导出
        val exportConfig = MemoryUtils.createExportConfig(
            groupByCategory = true,
            includeMetadata = true
        )
        
        val exportResult = facade.exportToMarkdown(exportConfig)
        
        assertTrue("导出应该成功", exportResult.success)
        assertTrue("应该有导出文件", exportResult.exportedFiles.isNotEmpty())
        assertNotNull("应该有输出目录", exportResult.outputDirectory)
        
        // 验证导出文件存在
        val outputDir = java.io.File(exportResult.outputDirectory)
        assertTrue("输出目录应该存在", outputDir.exists())
        assertTrue("应该有索引文件", java.io.File(outputDir, "INDEX.md").exists())
    }
    
    // 测试批量记忆处理
    fun testBatchMemoryProcessing() = runBlocking {
        // 批量添加记忆
        val memories = listOf(
            "Spring Boot" to "Spring Boot 是一个快速开发框架",
            "Docker" to "Docker 是容器化技术",
            "Kubernetes" to "Kubernetes 是容器编排平台",
            "微服务" to "微服务架构的设计原则",
            "API 设计" to "RESTful API 设计最佳实践"
        )
        
        val results = facade.addMemories(memories)
        
        assertEquals("应该处理所有记忆", memories.size, results.size)
        assertTrue("所有记忆都应该成功添加", results.all { it.success })
        
        // 测试相关记忆检索
        val relatedMemories = facade.getRelatedMemories("微服务架构", 10)
        assertTrue("应该找到相关记忆", relatedMemories.isNotEmpty())
        
        // 验证记忆类型分布
        val typeDistribution = relatedMemories.groupBy { it.type }
        assertTrue("应该有不同类型的记忆", typeDistribution.isNotEmpty())
    }
    
    // 测试智能建议生成功能
    fun testSmartSuggestions() = runBlocking {
        // 添加一些记忆
        facade.addMemory("Bug 修复", "修复了一个内存泄漏问题")
        facade.addMemory("性能优化", "优化了数据库查询性能")
        
        // 测试智能建议
        val suggestions1 = facade.getSmartSuggestions("遇到了性能问题")
        assertTrue("应该有建议", suggestions1.isNotEmpty())
        
        val suggestions2 = facade.getSmartSuggestions("这是一个全新的话题")
        assertTrue("对于新话题也应该有建议", suggestions2.isNotEmpty())
    }
    
    // 测试异步操作支持
    fun testAsyncOperations() {
        var addResult: ProcessingResult? = null
        var enrichResult: String? = null
        var exportResult: ExportResult? = null
        
        // 测试异步添加记忆
        facade.addMemoryAsync("异步测试记忆", "这是异步添加的记忆") { result ->
            addResult = result
        }
        
        // 测试异步上下文丰富
        facade.enrichContextAsync("异步上下文测试") { result ->
            enrichResult = result
        }
        
        // 测试异步导出
        facade.exportAsync() { result ->
            exportResult = result
        }
        
        // 等待异步操作完成
        Thread.sleep(3000)
        
        assertNotNull("异步添加应该完成", addResult)
        assertNotNull("异步丰富应该完成", enrichResult)
        assertNotNull("异步导出应该完成", exportResult)
        
        assertTrue("异步添加应该成功", addResult!!.success)
        assertNotNull("异步丰富结果不应为空", enrichResult)
        assertTrue("异步导出应该成功", exportResult!!.success)
    }
    
    // 测试健康检查功能
    fun testHealthCheck() {
        val healthStatus = facade.healthCheck()
        
        assertNotNull("健康检查结果不应为空", healthStatus)
        assertTrue("应该检查记忆引擎", healthStatus.containsKey("memoryEngine"))
        assertTrue("应该检查上下文服务", healthStatus.containsKey("contextService"))
        assertTrue("应该检查导出服务", healthStatus.containsKey("exportService"))
        
        // 所有服务都应该是健康的
        assertTrue("所有服务都应该健康", healthStatus.values.all { it })
    }
    
    // 测试字符串扩展函数
    fun testExtensionFunctions() = runBlocking {
        val testContent = "这是一个测试内容，包含 Kotlin 和协程相关信息"
        
        // 测试字符串扩展函数
        val saveResult = testContent.saveAsMemory(project, "扩展函数测试")
        assertTrue("保存应该成功", saveResult.success)
        
        val enrichedContent = testContent.enrichWithMemories(project)
        assertNotNull("丰富的内容不应为空", enrichedContent)
        assertTrue("丰富的内容应该包含原始内容", enrichedContent.contains(testContent))
    }
    
    // 测试配置管理功能
    fun testConfigurationManagement() {
        // 测试默认配置
        val defaultConfig = facade.getDefaultConfig()
        assertNotNull("默认配置不应为空", defaultConfig)
        assertTrue("应该启用AI评估", defaultConfig.enableAIEvaluation)
        assertTrue("应该启用自动导出", defaultConfig.autoExportEnabled)
        
        // 测试配置更新
        val customConfig = MemoryUtils.createConfig(
            enableAI = false,
            autoExport = false,
            minImportance = 3
        )
        
        assertNotNull("自定义配置不应为空", customConfig)
        assertFalse("AI评估应该被禁用", customConfig.enableAIEvaluation)
        assertFalse("自动导出应该被禁用", customConfig.autoExportEnabled)
        
        // 更新配置（这里只是测试接口，实际更新逻辑可能需要持久化）
        facade.updateConfig(customConfig)
    }
    
    // 测试内存统计信息获取
    fun testMemoryStatistics() {
        val stats = facade.getMemoryStatistics()
        
        assertNotNull("统计信息不应为空", stats)
        assertTrue("工作记忆数量应该非负", stats.workingMemoryCount >= 0)
        assertTrue("短期记忆数量应该非负", stats.shortTermMemoryCount >= 0)
        assertTrue("长期记忆数量应该非负", stats.longTermMemoryCount >= 0)
        assertTrue("总处理量应该非负", stats.totalProcessed >= 0)
        assertTrue("平均重要性应该在合理范围内", stats.averageImportance >= 0.0 && stats.averageImportance <= 5.0)
    }
    
    // 测试一键式处理和上下文丰富
    fun testProcessAndEnrich() = runBlocking {
        // 测试一键式处理
        val (processResult, enrichedContext) = facade.processAndEnrich(
            title = "一键测试",
            content = "这是一键式处理的测试内容",
            query = "测试查询"
        )
        
        assertTrue("处理应该成功", processResult.success)
        assertNotNull("丰富的上下文不应为空", enrichedContext)
        assertTrue("丰富的上下文应该包含查询内容", enrichedContext.contains("测试"))
    }
}
