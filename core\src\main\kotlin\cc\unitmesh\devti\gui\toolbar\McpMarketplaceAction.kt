package cc.unitmesh.devti.gui.toolbar


import cc.unitmesh.devti.mcp.marketplace.ui.McpMarketplaceDialog
import com.intellij.icons.AllIcons
import com.intellij.openapi.actionSystem.*
import com.intellij.openapi.actionSystem.ex.CustomComponentAction
import com.intellij.openapi.actionSystem.impl.ActionButton
import com.intellij.openapi.project.DumbAware
import com.intellij.ui.JBColor
import com.intellij.ui.components.JBLabel
import com.intellij.util.ui.JBUI
import com.intellij.util.ui.UIUtil
import java.awt.BorderLayout
import java.awt.Cursor
import java.awt.event.MouseAdapter
import java.awt.event.MouseEvent
import javax.swing.JButton
import javax.swing.JComponent
import javax.swing.JPanel

/**
 * MCP 市场按钮 Action
 * 在工具栏中显示一个按钮，点击后打开 MCP 市场对话框
 */
class McpMarketplaceAction : AnAction("MCP Marketplace", "Browse and install MCP packages", AllIcons.Actions.Download),
    CustomComponentAction, DumbAware {
    
    override fun getActionUpdateThread(): ActionUpdateThread = ActionUpdateThread.EDT
    
    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return
        showMarketplace(project)
    }
    
    override fun createCustomComponent(presentation: Presentation, place: String): JComponent {
        return when (place) {
            ActionPlaces.MAIN_TOOLBAR -> createMainToolbarButton(presentation)
            ActionPlaces.EDITOR_TOOLBAR -> createEditorToolbarButton(presentation)
            else -> createDefaultButton(presentation)
        }
    }
    
    /**
     * 创建主工具栏按钮
     */
    private fun createMainToolbarButton(presentation: Presentation): JComponent {
        val button = object : JButton() {
            init {
                putClientProperty("ActionToolbar.smallVariant", true)
                putClientProperty("customButtonInsets", JBUI.insets(1, 1, 1, 1).asUIResource())
                
                icon = AllIcons.Actions.Download
                toolTipText = "MCP Marketplace - Browse and install MCP packages"
                isOpaque = false
                isFocusable = false
                
                addActionListener {
                    val project = ActionToolbar.getDataContextFor(this).getData(CommonDataKeys.PROJECT)
                    if (project != null) {
                        showMarketplace(project)
                    }
                }
            }
        }
        
        return com.intellij.ui.components.panels.Wrapper(button).also {
            it.border = JBUI.Borders.empty(0, 10)
        }
    }
    
    /**
     * 创建编辑器工具栏按钮
     */
    private fun createEditorToolbarButton(presentation: Presentation): JComponent {
        val panel = JPanel(BorderLayout())
        panel.isOpaque = false
        
        val iconLabel = JBLabel(AllIcons.Actions.Download).apply {
            cursor = Cursor.getPredefinedCursor(Cursor.HAND_CURSOR)
            toolTipText = "MCP Marketplace"
        }
        
        val textLabel = JBLabel("Marketplace").apply {
            cursor = Cursor.getPredefinedCursor(Cursor.HAND_CURSOR)
            font = JBUI.Fonts.smallFont()
            foreground = UIUtil.getLabelForeground()
            border = JBUI.Borders.emptyLeft(4)
        }
        
        val clickListener = object : MouseAdapter() {
            override fun mouseClicked(e: MouseEvent) {
                val project = ActionToolbar.getDataContextFor(panel).getData(CommonDataKeys.PROJECT)
                if (project != null) {
                    showMarketplace(project)
                }
            }
            
            override fun mouseEntered(e: MouseEvent) {
                iconLabel.foreground = JBColor.BLUE
                textLabel.foreground = JBColor.BLUE
            }

            override fun mouseExited(e: MouseEvent) {
                iconLabel.foreground = UIUtil.getLabelForeground()
                textLabel.foreground = UIUtil.getLabelForeground()
            }
        }
        
        iconLabel.addMouseListener(clickListener)
        textLabel.addMouseListener(clickListener)
        panel.addMouseListener(clickListener)
        
        panel.add(iconLabel, BorderLayout.WEST)
        panel.add(textLabel, BorderLayout.CENTER)
        panel.border = JBUI.Borders.empty(4, 8)
        
        return panel
    }
    
    /**
     * 创建默认按钮
     */
    private fun createDefaultButton(presentation: Presentation): JComponent {
        val actionButton = ActionButton(
            this,
            presentation,
            ActionPlaces.UNKNOWN,
            JBUI.size(24, 24)
        )
        
        return com.intellij.ui.components.panels.Wrapper(actionButton).also {
            it.border = JBUI.Borders.empty(2)
        }
    }
    
    override fun update(e: AnActionEvent) {
        val project = e.project
        e.presentation.isEnabled = project != null
        
        // 根据上下文更新图标和文本
        when (e.place) {
            ActionPlaces.MAIN_TOOLBAR -> {
                e.presentation.icon = AllIcons.Actions.Download
                e.presentation.text = "MCP Marketplace"
            }
            ActionPlaces.EDITOR_TOOLBAR -> {
                e.presentation.icon = AllIcons.Actions.Download
                e.presentation.text = "Marketplace"
            }
            else -> {
                e.presentation.icon = AllIcons.Actions.Download
                e.presentation.text = "MCP Marketplace"
            }
        }
    }
    
    private fun showMarketplace(project: com.intellij.openapi.project.Project) {
        val dialog = McpMarketplaceDialog(project)
        dialog.show()
    }
}

/**
 * MCP 市场快速访问 Action
 * 可以放在更显眼的位置，比如 MCP 配置按钮旁边
 */
class McpMarketplaceQuickAction : AnAction("Browse MCP Marketplace", "Quick access to MCP marketplace", AllIcons.General.Web),
    DumbAware {
    
    override fun getActionUpdateThread(): ActionUpdateThread = ActionUpdateThread.EDT
    
    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return
        val dialog = McpMarketplaceDialog(project)
        dialog.show()
    }
    
    override fun update(e: AnActionEvent) {
        e.presentation.isEnabled = e.project != null
    }
}

/**
 * 在 MCP 配置弹窗中添加市场按钮的 Action
 */
class McpConfigMarketplaceAction : AnAction("Browse Marketplace", "Browse MCP marketplace for more packages", AllIcons.General.Web),
    DumbAware {
    
    override fun getActionUpdateThread(): ActionUpdateThread = ActionUpdateThread.EDT
    
    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return
        val dialog = McpMarketplaceDialog(project)
        dialog.show()
    }
    
    override fun update(e: AnActionEvent) {
        e.presentation.isEnabled = e.project != null
    }
}
