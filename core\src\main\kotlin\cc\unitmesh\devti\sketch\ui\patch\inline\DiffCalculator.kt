package cc.unitmesh.devti.sketch.ui.patch.inline

/**
 * 差异计算工具类，用于计算文本的行级和字符级差异
 */
object DiffCalculator {
    
    /**
     * 行差异类型
     */
    sealed class LineDiff {
        data class Added(val line: String, val lineNumber: Int) : LineDiff()
        data class Deleted(val line: String, val lineNumber: Int) : LineDiff()
        data class Modified(
            val oldLine: String, 
            val newLine: String, 
            val oldLineNumber: Int, 
            val newLineNumber: Int,
            val charDiffs: List<CharDiff>
        ) : LineDiff()
        data class Unchanged(val line: String, val lineNumber: Int) : LineDiff()
    }
    
    /**
     * 字符差异类型
     */
    sealed class CharDiff {
        data class Added(val text: String, val startIndex: Int, val endIndex: Int) : CharDiff()
        data class Deleted(val text: String, val startIndex: Int, val endIndex: Int) : CharDiff()
        data class Unchanged(val text: String, val startIndex: Int, val endIndex: Int) : CharDiff()
    }
    
    /**
     * 计算两个文本之间的行级差异
     */
    fun calculateLineDiffs(oldText: String, newText: String): List<LineDiff> {
        val oldLines = oldText.lines()
        val newLines = newText.lines()
        
        val lcs = computeLCS(oldLines, newLines)
        return buildLineDiffs(oldLines, newLines, lcs)
    }
    
    /**
     * 计算两个字符串之间的字符级差异
     */
    fun calculateCharDiffs(oldLine: String, newLine: String): List<CharDiff> {
        val oldChars = oldLine.toCharArray().map { it.toString() }
        val newChars = newLine.toCharArray().map { it.toString() }
        
        val lcs = computeLCS(oldChars, newChars)
        return buildCharDiffs(oldChars, newChars, lcs)
    }
    
    /**
     * 计算最长公共子序列 (LCS)
     */
    private fun <T> computeLCS(seq1: List<T>, seq2: List<T>): Array<IntArray> {
        val m = seq1.size
        val n = seq2.size
        val dp = Array(m + 1) { IntArray(n + 1) }
        
        for (i in 1..m) {
            for (j in 1..n) {
                if (seq1[i - 1] == seq2[j - 1]) {
                    dp[i][j] = dp[i - 1][j - 1] + 1
                } else {
                    dp[i][j] = maxOf(dp[i - 1][j], dp[i][j - 1])
                }
            }
        }
        
        return dp
    }
    
    /**
     * 根据LCS构建行级差异
     */
    private fun buildLineDiffs(oldLines: List<String>, newLines: List<String>, lcs: Array<IntArray>): List<LineDiff> {
        val diffs = mutableListOf<LineDiff>()
        var i = oldLines.size
        var j = newLines.size
        var oldLineNum = oldLines.size
        var newLineNum = newLines.size
        
        while (i > 0 || j > 0) {
            when {
                i > 0 && j > 0 && oldLines[i - 1] == newLines[j - 1] -> {
                    // 相同行
                    diffs.add(0, LineDiff.Unchanged(oldLines[i - 1], oldLineNum))
                    i--
                    j--
                    oldLineNum--
                    newLineNum--
                }
                i > 0 && j > 0 && lcs[i - 1][j] >= lcs[i][j - 1] -> {
                    // 删除行
                    diffs.add(0, LineDiff.Deleted(oldLines[i - 1], oldLineNum))
                    i--
                    oldLineNum--
                }
                j > 0 -> {
                    // 添加行
                    diffs.add(0, LineDiff.Added(newLines[j - 1], newLineNum))
                    j--
                    newLineNum--
                }
                else -> {
                    // 删除行
                    diffs.add(0, LineDiff.Deleted(oldLines[i - 1], oldLineNum))
                    i--
                    oldLineNum--
                }
            }
        }
        
        // 合并相邻的修改行
        return mergeModifiedLines(diffs)
    }
    
    /**
     * 合并相邻的添加和删除行为修改行
     */
    private fun mergeModifiedLines(diffs: List<LineDiff>): List<LineDiff> {
        val result = mutableListOf<LineDiff>()
        var i = 0
        
        while (i < diffs.size) {
            val current = diffs[i]
            
            when (current) {
                is LineDiff.Deleted -> {
                    // 查找紧接着的添加行
                    if (i + 1 < diffs.size && diffs[i + 1] is LineDiff.Added) {
                        val added = diffs[i + 1] as LineDiff.Added
                        val charDiffs = calculateCharDiffs(current.line, added.line)
                        result.add(LineDiff.Modified(
                            current.line, 
                            added.line, 
                            current.lineNumber, 
                            added.lineNumber,
                            charDiffs
                        ))
                        i += 2 // 跳过下一个添加行
                    } else {
                        result.add(current)
                        i++
                    }
                }
                else -> {
                    result.add(current)
                    i++
                }
            }
        }
        
        return result
    }
    
    /**
     * 根据LCS构建字符级差异
     */
    private fun buildCharDiffs(oldChars: List<String>, newChars: List<String>, lcs: Array<IntArray>): List<CharDiff> {
        val diffs = mutableListOf<CharDiff>()
        var i = oldChars.size
        var j = newChars.size
        
        while (i > 0 || j > 0) {
            when {
                i > 0 && j > 0 && oldChars[i - 1] == newChars[j - 1] -> {
                    // 相同字符
                    diffs.add(0, CharDiff.Unchanged(oldChars[i - 1], i - 1, i))
                    i--
                    j--
                }
                i > 0 && (j == 0 || lcs[i - 1][j] >= lcs[i][j - 1]) -> {
                    // 删除字符
                    diffs.add(0, CharDiff.Deleted(oldChars[i - 1], i - 1, i))
                    i--
                }
                j > 0 -> {
                    // 添加字符
                    diffs.add(0, CharDiff.Added(newChars[j - 1], j - 1, j))
                    j--
                }
            }
        }
        
        return mergeConsecutiveCharDiffs(diffs)
    }
    
    /**
     * 合并连续的字符差异
     */
    private fun mergeConsecutiveCharDiffs(diffs: List<CharDiff>): List<CharDiff> {
        if (diffs.isEmpty()) return diffs
        
        val result = mutableListOf<CharDiff>()
        var current = diffs[0]
        
        for (i in 1 until diffs.size) {
            val next = diffs[i]
            
            if (canMergeCharDiffs(current, next)) {
                current = mergeCharDiffs(current, next)
            } else {
                result.add(current)
                current = next
            }
        }
        
        result.add(current)
        return result
    }
    
    /**
     * 判断两个字符差异是否可以合并
     */
    private fun canMergeCharDiffs(diff1: CharDiff, diff2: CharDiff): Boolean {
        return when {
            diff1 is CharDiff.Added && diff2 is CharDiff.Added -> 
                diff1.endIndex == diff2.startIndex
            diff1 is CharDiff.Deleted && diff2 is CharDiff.Deleted -> 
                diff1.endIndex == diff2.startIndex
            diff1 is CharDiff.Unchanged && diff2 is CharDiff.Unchanged -> 
                diff1.endIndex == diff2.startIndex
            else -> false
        }
    }
    
    /**
     * 合并两个字符差异
     */
    private fun mergeCharDiffs(diff1: CharDiff, diff2: CharDiff): CharDiff {
        return when {
            diff1 is CharDiff.Added && diff2 is CharDiff.Added ->
                CharDiff.Added(diff1.text + diff2.text, diff1.startIndex, diff2.endIndex)
            diff1 is CharDiff.Deleted && diff2 is CharDiff.Deleted ->
                CharDiff.Deleted(diff1.text + diff2.text, diff1.startIndex, diff2.endIndex)
            diff1 is CharDiff.Unchanged && diff2 is CharDiff.Unchanged ->
                CharDiff.Unchanged(diff1.text + diff2.text, diff1.startIndex, diff2.endIndex)
            else -> diff1 // 不应该到达这里
        }
    }
}
