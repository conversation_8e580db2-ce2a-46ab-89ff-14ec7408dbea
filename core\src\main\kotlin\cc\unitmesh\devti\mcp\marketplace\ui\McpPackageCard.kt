package cc.unitmesh.devti.mcp.marketplace.ui


import cc.unitmesh.devti.mcp.marketplace.model.McpPackage
import cc.unitmesh.devti.mcp.marketplace.model.InstallType
import cc.unitmesh.devti.mcp.marketplace.service.McpPackageInstaller
import com.intellij.icons.AllIcons
import com.intellij.openapi.project.Project
import com.intellij.ui.JBColor
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBPanel
import com.intellij.util.ui.JBUI
import com.intellij.util.ui.UIUtil
import java.awt.*
import java.awt.event.MouseAdapter
import java.awt.event.MouseEvent
import javax.swing.*

/**
 * MCP 包卡片组件
 */
class McpPackageCard(
    private val project: Project,
    private val mcpPackage: McpPackage,
    private val packageInstaller: McpPackageInstaller,
    private val onStatusChanged: (String) -> Unit
) : JBPanel<McpPackageCard>(BorderLayout()) {
    
    private val isInstalled = packageInstaller.isPackageInstalled(mcpPackage.id)
    
    init {
        setupUI()
        setupInteractions()
    }
    
    private fun setupUI() {
        border = JBUI.Borders.compound(
            JBUI.Borders.customLine(JBColor.border()),
            JBUI.Borders.empty(12)
        )
        background = UIUtil.getPanelBackground()
        
        // 头部面板
        val headerPanel = createHeaderPanel()
        add(headerPanel, BorderLayout.NORTH)
        
        // 内容面板
        val contentPanel = createContentPanel()
        add(contentPanel, BorderLayout.CENTER)
        
        // 底部面板
        val footerPanel = createFooterPanel()
        add(footerPanel, BorderLayout.SOUTH)
    }
    
    private fun createHeaderPanel(): JComponent {
        val panel = JPanel(BorderLayout())
        panel.isOpaque = false
        
        // 左侧：包名和标签
        val leftPanel = JPanel(FlowLayout(FlowLayout.LEFT, 0, 0))
        leftPanel.isOpaque = false
        
        val nameLabel = JBLabel(mcpPackage.displayName).apply {
            font = JBUI.Fonts.label(14f).asBold()
        }
        leftPanel.add(nameLabel)
        
        // 官方标签
        if (mcpPackage.isOfficial) {
            leftPanel.add(Box.createHorizontalStrut(8))
            leftPanel.add(createBadge("Official", JBColor.BLUE))
        }
        
        // 验证标签
        if (mcpPackage.isVerified) {
            leftPanel.add(Box.createHorizontalStrut(4))
            leftPanel.add(createBadge("Verified", JBColor.GREEN))
        }
        
        // 右侧：版本和评分
        val rightPanel = JPanel(FlowLayout(FlowLayout.RIGHT, 0, 0))
        rightPanel.isOpaque = false
        
        // 评分
        if (mcpPackage.rating > 0) {
            val ratingPanel = JPanel(FlowLayout(FlowLayout.RIGHT, 2, 0))
            ratingPanel.isOpaque = false
            ratingPanel.add(JBLabel("★").apply { foreground = JBColor.ORANGE })
            ratingPanel.add(JBLabel(String.format("%.1f", mcpPackage.rating)).apply {
                foreground = UIUtil.getContextHelpForeground()
                font = JBUI.Fonts.smallFont()
            })
            rightPanel.add(ratingPanel)
            rightPanel.add(Box.createHorizontalStrut(8))
        }
        
        // 版本
        rightPanel.add(JBLabel("v${mcpPackage.version}").apply {
            foreground = UIUtil.getContextHelpForeground()
            font = JBUI.Fonts.smallFont()
        })
        
        panel.add(leftPanel, BorderLayout.WEST)
        panel.add(rightPanel, BorderLayout.EAST)
        
        return panel
    }
    
    private fun createContentPanel(): JComponent {
        val panel = JPanel(BorderLayout())
        panel.isOpaque = false
        panel.border = JBUI.Borders.emptyTop(8)
        
        // 描述
        val descriptionLabel = JBLabel("<html>${mcpPackage.description}</html>").apply {
            foreground = UIUtil.getLabelForeground()
        }
        panel.add(descriptionLabel, BorderLayout.NORTH)
        
        // 工具信息
        if (mcpPackage.tools.isNotEmpty()) {
            val toolsPanel = JPanel(FlowLayout(FlowLayout.LEFT, 0, 4))
            toolsPanel.isOpaque = false
            
            val toolsLabel = JBLabel("Tools: ").apply {
                font = JBUI.Fonts.smallFont()
                foreground = UIUtil.getContextHelpForeground()
            }
            toolsPanel.add(toolsLabel)
            
            mcpPackage.tools.take(3).forEach { tool ->
                toolsPanel.add(createToolBadge(tool.name))
                toolsPanel.add(Box.createHorizontalStrut(4))
            }
            
            if (mcpPackage.tools.size > 3) {
                toolsPanel.add(JBLabel("+${mcpPackage.tools.size - 3} more").apply {
                    font = JBUI.Fonts.smallFont()
                    foreground = UIUtil.getContextHelpForeground()
                })
            }
            
            panel.add(toolsPanel, BorderLayout.CENTER)
        }
        
        return panel
    }
    
    private fun createFooterPanel(): JComponent {
        val panel = JPanel(BorderLayout())
        panel.isOpaque = false
        panel.border = JBUI.Borders.emptyTop(8)
        
        // 左侧：安装类型和作者
        val leftPanel = JPanel(FlowLayout(FlowLayout.LEFT, 0, 0))
        leftPanel.isOpaque = false
        
        val typeIcon = getInstallTypeIcon(mcpPackage.installType)
        leftPanel.add(JBLabel(typeIcon))
        leftPanel.add(Box.createHorizontalStrut(4))
        leftPanel.add(JBLabel(mcpPackage.installType.displayName).apply {
            font = JBUI.Fonts.smallFont()
            foreground = UIUtil.getContextHelpForeground()
        })
        
        leftPanel.add(Box.createHorizontalStrut(12))
        leftPanel.add(JBLabel("by ${mcpPackage.author}").apply {
            font = JBUI.Fonts.smallFont()
            foreground = UIUtil.getContextHelpForeground()
        })
        
        // 右侧：操作按钮
        val rightPanel = JPanel(FlowLayout(FlowLayout.RIGHT, 4, 0))
        rightPanel.isOpaque = false
        
        // 详情按钮
        val detailsButton = JButton("Details").apply {
            font = JBUI.Fonts.smallFont()
            addActionListener { showPackageDetails() }
        }
        rightPanel.add(detailsButton)
        
        // 安装/卸载按钮
        val actionButton = if (isInstalled) {
            JButton("Uninstall").apply {
                font = JBUI.Fonts.smallFont()
                addActionListener { uninstallPackage() }
            }
        } else {
            JButton("Install").apply {
                font = JBUI.Fonts.smallFont()
                addActionListener { installPackage() }
            }
        }
        rightPanel.add(actionButton)
        
        panel.add(leftPanel, BorderLayout.WEST)
        panel.add(rightPanel, BorderLayout.EAST)
        
        return panel
    }
    
    private fun createBadge(text: String, color: JBColor): JComponent {
        val label = JBLabel(text).apply {
            font = JBUI.Fonts.smallFont()
            foreground = Color.WHITE
            isOpaque = true
            background = color
            border = JBUI.Borders.empty(2, 6)
        }
        return label
    }
    
    private fun createToolBadge(toolName: String): JComponent {
        val label = JBLabel(toolName).apply {
            font = JBUI.Fonts.smallFont()
            foreground = UIUtil.getContextHelpForeground()
            border = JBUI.Borders.compound(
                JBUI.Borders.customLine(JBColor.border()),
                JBUI.Borders.empty(2, 4)
            )
        }
        return label
    }
    
    private fun getInstallTypeIcon(installType: InstallType) = when (installType) {
        InstallType.NPX, InstallType.NPM -> AllIcons.FileTypes.JavaScript
        InstallType.PYTHON -> AllIcons.FileTypes.JavaScript
        InstallType.DOCKER -> AllIcons.Nodes.Services
        InstallType.GIT -> AllIcons.Vcs.Vendors.Github
        InstallType.BINARY -> AllIcons.FileTypes.Archive
        InstallType.CUSTOM -> AllIcons.Actions.Execute
    }
    
    private fun setupInteractions() {
        cursor = Cursor.getPredefinedCursor(Cursor.HAND_CURSOR)
        
        addMouseListener(object : MouseAdapter() {
            override fun mouseEntered(e: MouseEvent) {
                background = UIUtil.getListSelectionBackground(true)
                repaint()
            }
            
            override fun mouseExited(e: MouseEvent) {
                background = UIUtil.getPanelBackground()
                repaint()
            }
            
            override fun mouseClicked(e: MouseEvent) {
                if (e.clickCount == 2) {
                    showPackageDetails()
                }
            }
        })
    }
    
    private fun installPackage() {
        packageInstaller.installPackage(mcpPackage) { progress, message ->
            // 可以在这里更新进度显示
        }.thenAccept { success ->
            SwingUtilities.invokeLater {
                onStatusChanged(mcpPackage.id)
            }
        }
    }
    
    private fun uninstallPackage() {
        val result = JOptionPane.showConfirmDialog(
            this,
            "Are you sure you want to uninstall ${mcpPackage.displayName}?",
            "Confirm Uninstall",
            JOptionPane.YES_NO_OPTION
        )
        
        if (result == JOptionPane.YES_OPTION) {
            packageInstaller.uninstallPackage(mcpPackage.id).thenAccept { success ->
                SwingUtilities.invokeLater {
                    onStatusChanged(mcpPackage.id)
                }
            }
        }
    }
    
    private fun showPackageDetails() {
        val dialog = McpPackageDetailDialog(project, mcpPackage, packageInstaller)
        dialog.show()
    }
}
