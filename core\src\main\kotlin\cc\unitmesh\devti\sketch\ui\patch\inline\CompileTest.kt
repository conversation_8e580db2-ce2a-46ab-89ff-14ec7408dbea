package cc.unitmesh.devti.sketch.ui.patch.inline

import cc.unitmesh.devti.sketch.ui.patch.SingleFileDiffSketch
import com.intellij.lang.Language
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.patch.TextFilePatch

/**
 * 编译测试类，用于验证所有类和方法的语法正确性
 */
object CompileTest {
    
    /**
     * 测试DiffCalculator的编译
     */
    fun testDiffCalculator() {
        val oldCode = "line1\nline2"
        val newCode = "line1\nline3"
        
        // 测试行级差异计算
        val lineDiffs = DiffCalculator.calculateLineDiffs(oldCode, newCode)
        
        // 测试字符级差异计算
        val charDiffs = DiffCalculator.calculateCharDiffs("old", "new")
        
        // 测试差异类型
        lineDiffs.forEach { diff ->
            when (diff) {
                is DiffCalculator.LineDiff.Added -> println("Added: ${diff.line}")
                is DiffCalculator.LineDiff.Deleted -> println("Deleted: ${diff.line}")
                is DiffCalculator.LineDiff.Modified -> println("Modified: ${diff.oldLine} -> ${diff.newLine}")
                is DiffCalculator.LineDiff.Unchanged -> println("Unchanged: ${diff.line}")
            }
        }
        
        charDiffs.forEach { diff ->
            when (diff) {
                is DiffCalculator.CharDiff.Added -> println("Char Added: ${diff.text}")
                is DiffCalculator.CharDiff.Deleted -> println("Char Deleted: ${diff.text}")
                is DiffCalculator.CharDiff.Unchanged -> println("Char Unchanged: ${diff.text}")
            }
        }
    }
    
    /**
     * 测试InlineDiffViewer的编译（模拟）
     */
    fun testInlineDiffViewer(project: Project) {
        val oldCode = "function test() { return 'old'; }"
        val newCode = "function test() { return 'new'; }"
        
        // 测试创建查看器
        val viewer = InlineDiffViewer.create(project, oldCode, newCode)
        
        // 测试方法调用
        val component = viewer.getComponent()
        val stats = viewer.getDiffStats()
        val hasDiff = viewer.hasDifferences()
        
        // 测试静态方法
        val hasStaticDiff = InlineDiffViewer.hasDifferences(oldCode, newCode)
        
        // 测试资源清理
        viewer.dispose()
    }
    
    /**
     * 测试InlineDiffExtension的编译（模拟）
     */
    fun testInlineDiffExtension(
        project: Project,
        currentFile: VirtualFile,
        patch: TextFilePatch
    ) {
        val oldCode = "old content"
        val newCode = "new content"
        
        // 测试扩展创建
        val extension = InlineDiffExtension(project, currentFile, patch, oldCode, newCode)
        
        // 测试方法调用
        val viewer = extension.createInlineDiffViewer()
        val button = extension.createToggleButton()
        val isInline = extension.isInlineMode()
        val stats = extension.getDiffStats()
        val hasDiff = extension.hasDifferences()
        
        // 测试模式切换
        extension.setInlineMode(true)
        extension.refreshDiffs()
        
        // 测试静态方法
        val isSupported = InlineDiffExtension.isSupported(oldCode, newCode)
        
        // 测试资源清理
        extension.dispose()
    }
    
    /**
     * 测试InlineDiffIntegration的编译（模拟）
     */
    fun testInlineDiffIntegration(
        originalSketch: SingleFileDiffSketch,
        project: Project,
        currentFile: VirtualFile,
        patch: TextFilePatch
    ) {
        val oldCode = "old content"
        val newCode = "new content"
        
        // 测试增强功能
        val enhancedSketch = InlineDiffIntegration.enhance(
            originalSketch, project, currentFile, patch, oldCode, newCode
        )
        
        // 测试独立查看器创建
        val standaloneViewer = InlineDiffIntegration.createStandaloneViewer(
            project, oldCode, newCode
        )
        
        // 测试切换按钮创建
        val toggleButton = InlineDiffIntegration.createToggleButton { isInlineMode ->
            println("Toggle to inline mode: $isInlineMode")
        }
        
        // 测试支持性检查
        val isSupported = InlineDiffIntegration.isSupported(oldCode, newCode)
        
        // 测试统计计算
        val stats = InlineDiffIntegration.calculateDiffStats(oldCode, newCode)
        
        // 测试扩展方法
        val enhancedWithExtension = originalSketch.withInlineDiffSupport(
            project, currentFile, patch, oldCode, newCode
        )
    }
    
    /**
     * 测试配置类的编译
     */
    fun testConfiguration() {
        // 测试默认配置
        val defaultConfig = InlineDiffConfig.DEFAULT
        
        // 测试自定义配置
        val customConfig = InlineDiffConfig(
            maxLinesForInlineView = 500,
            showLineNumbers = true,
            showDiffStats = true,
            enableCharLevelDiff = true,
            defaultToInlineView = false
        )
        
        // 测试工厂类
        val factory = InlineDiffViewerFactory(customConfig)
    }
    
    /**
     * 测试容器类的编译（模拟）
     */
    fun testDiffViewContainer(
        traditionalView: javax.swing.JComponent,
        inlineView: javax.swing.JComponent
    ) {
        // 测试容器创建
        val container = DiffViewContainer(traditionalView, inlineView)
        
        // 测试方法调用
        container.toggleView()
        container.setShowInline(true)
        val isShowing = container.isShowingInline()
    }
    
    /**
     * 测试增强包装器的编译（模拟）
     */
    fun testEnhancedSingleFileDiffSketch(
        originalSketch: SingleFileDiffSketch,
        project: Project,
        currentFile: VirtualFile,
        patch: TextFilePatch
    ) {
        val oldCode = "old content"
        val newCode = "new content"
        
        // 测试增强包装器创建
        val enhanced = EnhancedSingleFileDiffSketch(
            originalSketch, project, currentFile, patch, oldCode, newCode
        )
        
        // 测试方法调用
        val component = enhanced.getEnhancedComponent()
        enhanced.switchToInlineView()
        enhanced.switchToTraditionalView()
        val isSupported = enhanced.isInlineViewSupported()
        val stats = enhanced.getDiffStats()
        
        // 测试资源清理
        enhanced.dispose()
    }
    
    /**
     * 主测试方法（仅用于编译验证）
     */
    @JvmStatic
    fun main(args: Array<String>) {
        println("编译测试通过 - 所有类和方法语法正确")
        
        // 注意：这些方法需要真实的IntelliJ环境才能运行
        // 这里只是验证编译时的语法正确性
        
        testDiffCalculator()
        println("DiffCalculator 编译测试通过")
        
        // 其他测试需要真实的项目环境，这里只验证语法
        println("所有类的语法验证完成")
    }
}
