package cc.unitmesh.devti.gui.example

import com.intellij.openapi.project.Project
import com.intellij.openapi.wm.ToolWindow
import com.intellij.openapi.wm.ToolWindowFactory
import com.intellij.ui.content.ContentFactory

/**
 * 在工具窗口中显示可折叠面板示例
 */
class CollapsiblePanelToolWindowFactory : ToolWindowFactory {
    override fun createToolWindowContent(project: Project, toolWindow: ToolWindow) {
        val example = CollapsiblePanelExample(project)
        val content = ContentFactory.getInstance().createContent(
            example.createCenterPanel(), "Collapsible Panel Example", false
        )
        toolWindow.contentManager.addContent(content)
    }
}