{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"mcpServers": {"type": "object", "additionalProperties": {"type": "object", "properties": {"command": {"type": "string"}, "args": {"type": "array", "items": {"type": "string"}}, "disabled": {"type": "boolean"}, "autoApprove": {"type": "array", "items": {"type": "string"}}, "env": {"type": "object", "additionalProperties": {"type": "string"}}, "requires_confirmation": {"type": "array", "items": {"type": "string"}}}, "required": ["command", "args"], "additionalProperties": false}}}, "required": ["mcpServers"]}