package cc.unitmesh.devti.gui.example

import cc.unitmesh.devti.gui.component.CollapsiblePanel
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBScrollPane
import com.intellij.util.ui.JBUI
import java.awt.BorderLayout
import java.awt.Dimension
import javax.swing.JComponent
import javax.swing.JPanel

class CollapsiblePanelExample(project: Project) : DialogWrapper(project) {
    init {
        title = "可折叠面板示例"
        init()
    }

    public override fun createCenterPanel(): JComponent {
        val mainPanel = JPanel(BorderLayout())
        mainPanel.preferredSize = Dimension(500, 400)
        
        val contentPanel = JPanel(BorderLayout())
        contentPanel.border = JBUI.Borders.empty(10)
        
        // 创建第一个可折叠面板
        val section1 = CollapsiblePanel("AutoDev for IntelliJ 项目介绍及架构信息", true)
        section1.addContent(JBLabel("这里是项目介绍内容..."))
        
        // 创建第二个可折叠面板
        val section2 = CollapsiblePanel("功能模块", false)
        section2.addContent(JBLabel("这里是功能模块内容..."))
        
        // 创建第三个可折叠面板
        val section3 = CollapsiblePanel("开发指南", false)
        section3.addContent(JBLabel("这里是开发指南内容..."))
        
        // 添加所有面板到内容区
        val sectionsPanel = JPanel(BorderLayout())
        sectionsPanel.add(section1, BorderLayout.NORTH)
        sectionsPanel.add(section2, BorderLayout.CENTER)
        sectionsPanel.add(section3, BorderLayout.SOUTH)
        
        // 添加滚动条
        contentPanel.add(JBScrollPane(sectionsPanel), BorderLayout.CENTER)
        mainPanel.add(contentPanel, BorderLayout.CENTER)
        
        return mainPanel
    }

}