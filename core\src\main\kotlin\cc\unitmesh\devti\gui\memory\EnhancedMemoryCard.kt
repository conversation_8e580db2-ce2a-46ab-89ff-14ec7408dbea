package cc.unitmesh.devti.gui.memory

import cc.unitmesh.devti.memory.MemoryBankService
import cc.unitmesh.devti.memory.MemorySummary
import cc.unitmesh.devti.memory.intelligent.GeneratedSummary
import cc.unitmesh.devti.memory.intelligent.MemoryProcessingEngine
import com.intellij.icons.AllIcons
import com.intellij.openapi.project.Project
import com.intellij.ui.JBColor
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBScrollPane
import com.intellij.util.ui.JBUI
import com.intellij.util.ui.UIUtil
import org.commonmark.parser.Parser
import org.commonmark.renderer.html.HtmlRenderer
import java.awt.*
import javax.swing.*
import javax.swing.text.html.HTMLEditorKit

/**
 * 增强的记忆卡片 - 支持AI摘要显示
 */
class EnhancedMemoryCard(
    private val project: Project,
    private val memory: MemorySummary,
    private val onUpdate: (MemorySummary) -> Unit
) : JPanel(BorderLayout()) {
    
    private val memoryBankService = MemoryBankService.getInstance(project)
    private val memoryEngine = MemoryProcessingEngine.getInstance(project)
    private var isExpanded = memory.isExpanded
    private var showingSummary = true // 默认显示摘要
    
    init {
        setupUI()
    }
    
    private fun setupUI() {
        border = JBUI.Borders.compound(
            JBUI.Borders.customLine(JBColor.border()),
            JBUI.Borders.empty(12)
        )
        background = UIUtil.getPanelBackground()
        
        // 头部面板
        val headerPanel = createHeaderPanel()
        add(headerPanel, BorderLayout.NORTH)
        
        // 内容面板
        if (isExpanded) {
            val contentPanel = createContentPanel()
            add(contentPanel, BorderLayout.CENTER)
        }
    }
    
    private fun createHeaderPanel(): JComponent {
        val panel = JPanel(BorderLayout())
        panel.isOpaque = false
        
        // 左侧：展开/折叠 + 标题
        val leftPanel = JPanel(FlowLayout(FlowLayout.LEFT, 0, 0))
        leftPanel.isOpaque = false
        
        val expandIcon = if (isExpanded) AllIcons.General.ArrowDown else AllIcons.General.ArrowRight
        val expandButton = JButton(expandIcon).apply {
            isOpaque = false
            isBorderPainted = false
            isContentAreaFilled = false
            toolTipText = if (isExpanded) "折叠" else "展开"
            addActionListener { toggleExpanded() }
        }
        
        val titleLabel = JBLabel(memory.title).apply {
            font = JBUI.Fonts.label(14f).asBold()
            foreground = UIUtil.getLabelForeground()
        }
        
        leftPanel.add(expandButton)
        leftPanel.add(Box.createHorizontalStrut(8))
        leftPanel.add(titleLabel)
        
        // 中间：标签和信息
        val middlePanel = JPanel(FlowLayout(FlowLayout.LEFT, 8, 0))
        middlePanel.isOpaque = false
        
        // 分类标签
        val categoryLabel = JBLabel(memory.category).apply {
            font = JBUI.Fonts.smallFont()
            foreground = Color.WHITE
            isOpaque = true
            background = getCategoryColor(memory.category)
            border = JBUI.Borders.empty(3, 8)
        }
        middlePanel.add(categoryLabel)
        
        // 重要性星级
        val starsPanel = JPanel(FlowLayout(FlowLayout.LEFT, 1, 0))
        starsPanel.isOpaque = false
        repeat(memory.importance) {
            starsPanel.add(JBLabel("★").apply { 
                foreground = Color.ORANGE
                font = JBUI.Fonts.smallFont()
            })
        }
        repeat(5 - memory.importance) {
            starsPanel.add(JBLabel("☆").apply { 
                foreground = UIUtil.getInactiveTextColor()
                font = JBUI.Fonts.smallFont()
            })
        }
        middlePanel.add(starsPanel)
        
        // AI摘要标识
        val aiSummary = memoryEngine.getGeneratedSummary(memory.id)
        if (aiSummary != null) {
            val aiLabel = JBLabel("AI").apply {
                font = JBUI.Fonts.miniFont()
                foreground = Color.WHITE
                isOpaque = true
                background = JBColor.GREEN
                border = JBUI.Borders.empty(2, 4)
                toolTipText = "包含AI生成摘要"
            }
            middlePanel.add(aiLabel)
        }
        
        // Markdown标识
        val markdownLabel = JBLabel("MD").apply {
            font = JBUI.Fonts.miniFont()
            foreground = Color.WHITE
            isOpaque = true
            background = JBColor.BLUE
            border = JBUI.Borders.empty(2, 4)
            toolTipText = "Markdown 格式"
        }
        middlePanel.add(markdownLabel)
        
        // 右侧：操作按钮
        val rightPanel = JPanel(FlowLayout(FlowLayout.RIGHT, 3, 0))
        rightPanel.isOpaque = false
        
        // 摘要/原文切换按钮
        if (aiSummary != null) {
            val toggleButton = JButton(if (showingSummary) "原文" else "摘要").apply {
                isOpaque = false
                isBorderPainted = false
                isContentAreaFilled = false
                font = JBUI.Fonts.smallFont()
                toolTipText = if (showingSummary) "显示原文" else "显示AI摘要"
                addActionListener { toggleSummaryView() }
            }
            rightPanel.add(toggleButton)
        }
        
        val editButton = JButton(AllIcons.Actions.Edit).apply {
            isOpaque = false
            isBorderPainted = false
            isContentAreaFilled = false
            toolTipText = "编辑记忆"
            addActionListener { editMemory() }
        }
        
        val copyButton = JButton(AllIcons.Actions.Copy).apply {
            isOpaque = false
            isBorderPainted = false
            isContentAreaFilled = false
            toolTipText = "复制内容"
            addActionListener { copyContent() }
        }
        
        val deleteButton = JButton(AllIcons.Actions.Cancel).apply {
            isOpaque = false
            isBorderPainted = false
            isContentAreaFilled = false
            toolTipText = "删除记忆"
            addActionListener { deleteMemory() }
        }
        
        rightPanel.add(copyButton)
        rightPanel.add(editButton)
        rightPanel.add(deleteButton)
        
        panel.add(leftPanel, BorderLayout.WEST)
        panel.add(middlePanel, BorderLayout.CENTER)
        panel.add(rightPanel, BorderLayout.EAST)
        
        return panel
    }
    
    private fun createContentPanel(): JComponent {
        val panel = JPanel(BorderLayout())
        panel.isOpaque = false
        panel.border = JBUI.Borders.emptyTop(12)
        
        val aiSummary = memoryEngine.getGeneratedSummary(memory.id)
        
        // 根据当前显示模式选择内容
        val contentToShow = when {
            showingSummary && aiSummary != null -> createSummaryContent(aiSummary)
            else -> memory.content
        }
        
        if (contentToShow.isBlank()) {
            val emptyLabel = JBLabel("内容为空").apply {
                horizontalAlignment = SwingConstants.CENTER
                foreground = UIUtil.getInactiveTextColor()
            }
            panel.add(emptyLabel, BorderLayout.CENTER)
            return panel
        }
        
        try {
            // 使用 CommonMark 解析 Markdown
            val parser = Parser.builder().build()
            val document = parser.parse(contentToShow)
            val renderer = HtmlRenderer.builder().build()
            val html = renderer.render(document)
            
            // 创建 HTML 显示组件
            val htmlPane = JEditorPane().apply {
                contentType = "text/html"
                editorKit = HTMLEditorKit()
                text = createStyledHtml(html)
                isEditable = false
                isOpaque = false
            }
            
            val scrollPane = JBScrollPane(htmlPane).apply {
                border = JBUI.Borders.compound(
                    JBUI.Borders.customLine(JBColor.border()),
                    JBUI.Borders.empty(8)
                )
                verticalScrollBarPolicy = JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED
                horizontalScrollBarPolicy = JScrollPane.HORIZONTAL_SCROLLBAR_NEVER
                preferredSize = Dimension(600, 300)
                minimumSize = Dimension(400, 100)
            }
            
            panel.add(scrollPane, BorderLayout.CENTER)
            
        } catch (e: Exception) {
            // 降级到纯文本显示
            val fallbackArea = JTextArea(contentToShow).apply {
                isEditable = false
                lineWrap = true
                wrapStyleWord = true
                font = JBUI.Fonts.label()
                background = UIUtil.getPanelBackground()
            }
            
            val scrollPane = JBScrollPane(fallbackArea).apply {
                border = JBUI.Borders.compound(
                    JBUI.Borders.customLine(JBColor.border()),
                    JBUI.Borders.empty(8)
                )
                preferredSize = Dimension(600, 200)
            }
            
            panel.add(scrollPane, BorderLayout.CENTER)
        }
        
        // 底部信息面板
        val infoPanel = createInfoPanel(aiSummary)
        panel.add(infoPanel, BorderLayout.SOUTH)
        
        return panel
    }
    
    private fun createSummaryContent(aiSummary: GeneratedSummary): String {
        return buildString {
            append("# ${memory.title}\n\n")
            
            append("## 🤖 AI生成摘要\n")
            append("${aiSummary.summary}\n\n")
            
            if (aiSummary.keywords.isNotEmpty()) {
                append("## 🔑 关键词\n")
                append(aiSummary.keywords.joinToString(" • ") { "`$it`" })
                append("\n\n")
            }
            
            if (aiSummary.relatedConcepts.isNotEmpty()) {
                append("## 🔗 相关概念\n")
                aiSummary.relatedConcepts.forEach { concept ->
                    append("- $concept\n")
                }
                append("\n")
            }
            
            if (aiSummary.importanceReason.isNotEmpty()) {
                append("## ⭐ 重要性分析\n")
                append("**评级**: ${"★".repeat(aiSummary.importance)}\n")
                append("**理由**: ${aiSummary.importanceReason}\n\n")
            }
        }
    }
    
    private fun createInfoPanel(aiSummary: GeneratedSummary?): JComponent {
        val panel = JPanel(FlowLayout(FlowLayout.LEFT, 0, 0))
        panel.isOpaque = false
        panel.border = JBUI.Borders.emptyTop(8)
        
        // 显示当前模式
        val modeLabel = JBLabel(if (showingSummary && aiSummary != null) "📋 摘要视图" else "📄 完整内容").apply {
            font = JBUI.Fonts.smallFont()
            foreground = UIUtil.getContextHelpForeground()
        }
        panel.add(modeLabel)
        panel.add(Box.createHorizontalStrut(10))
        
        // 标签显示
        if (memory.tags.isNotEmpty()) {
            memory.tags.forEach { tag ->
                val tagLabel = JBLabel("#$tag").apply {
                    font = JBUI.Fonts.smallFont()
                    foreground = JBColor.BLUE
                    border = JBUI.Borders.empty(2, 4)
                }
                panel.add(tagLabel)
                panel.add(Box.createHorizontalStrut(6))
            }
            
            panel.add(JBLabel("|").apply {
                foreground = UIUtil.getInactiveTextColor()
                font = JBUI.Fonts.smallFont()
            })
            panel.add(Box.createHorizontalStrut(6))
        }
        
        // 时间信息
        val timeLabel = JBLabel("创建: ${formatDateTime(memory.createdAt)}").apply {
            font = JBUI.Fonts.smallFont()
            foreground = UIUtil.getInactiveTextColor()
        }
        panel.add(timeLabel)
        
        return panel
    }
    
    private fun createStyledHtml(html: String): String {
        return """
            <html>
            <head>
                <style>
                    body { 
                        font-family: ${UIUtil.getLabelFont().family}; 
                        font-size: ${UIUtil.getLabelFont().size}px;
                        color: ${colorToHex(UIUtil.getLabelForeground())};
                        background-color: ${colorToHex(UIUtil.getPanelBackground())};
                        margin: 8px;
                        line-height: 1.5;
                    }
                    h1, h2, h3 { color: ${colorToHex(UIUtil.getLabelForeground())}; margin-top: 16px; }
                    h1 { font-size: 18px; }
                    h2 { font-size: 16px; }
                    h3 { font-size: 14px; }
                    pre { 
                        background-color: ${colorToHex(UIUtil.getTextFieldBackground())};
                        border: 1px solid ${colorToHex(UIUtil.getBoundsColor())};
                        padding: 8px;
                        border-radius: 4px;
                        overflow-x: auto;
                        font-family: monospace;
                    }
                    code {
                        background-color: ${colorToHex(UIUtil.getTextFieldBackground())};
                        padding: 2px 4px;
                        border-radius: 3px;
                        font-family: monospace;
                        font-size: ${UIUtil.getLabelFont().size - 1}px;
                    }
                    blockquote {
                        border-left: 4px solid ${colorToHex(JBColor.BLUE)};
                        margin: 8px 0;
                        padding-left: 12px;
                        color: ${colorToHex(UIUtil.getContextHelpForeground())};
                    }
                    table {
                        border-collapse: collapse;
                        width: 100%;
                        margin: 8px 0;
                    }
                    th, td {
                        border: 1px solid ${colorToHex(UIUtil.getBoundsColor())};
                        padding: 6px 8px;
                        text-align: left;
                    }
                    th {
                        background-color: ${colorToHex(UIUtil.getTextFieldBackground())};
                        font-weight: bold;
                    }
                    ul, ol { margin: 8px 0; padding-left: 20px; }
                    li { margin: 2px 0; }
                </style>
            </head>
            <body>
                $html
            </body>
            </html>
        """.trimIndent()
    }
    
    private fun toggleExpanded() {
        isExpanded = !isExpanded
        removeAll()
        setupUI()
        revalidate()
        repaint()
    }
    
    private fun toggleSummaryView() {
        showingSummary = !showingSummary
        removeAll()
        setupUI()
        revalidate()
        repaint()
    }
    
    private fun editMemory() {
        val dialog = EditMemoryDialog(project, memory) { updatedMemory ->
            onUpdate(updatedMemory)
        }
        dialog.show()
    }
    
    private fun copyContent() {
        val aiSummary = memoryEngine.getGeneratedSummary(memory.id)
        val contentToCopy = when {
            showingSummary && aiSummary != null -> createSummaryContent(aiSummary)
            else -> memory.content
        }
        
        val clipboard = Toolkit.getDefaultToolkit().systemClipboard
        val stringSelection = java.awt.datatransfer.StringSelection(contentToCopy)
        clipboard.setContents(stringSelection, null)
        
        JOptionPane.showMessageDialog(
            this,
            "内容已复制到剪贴板",
            "复制成功",
            JOptionPane.INFORMATION_MESSAGE
        )
    }
    
    private fun deleteMemory() {
        val result = JOptionPane.showConfirmDialog(
            this,
            "确定要删除这条记忆摘要吗？\n\n标题: ${memory.title}",
            "确认删除",
            JOptionPane.YES_NO_OPTION,
            JOptionPane.WARNING_MESSAGE
        )
        
        if (result == JOptionPane.YES_OPTION) {
            memoryBankService.deleteMemory(memory.id)
            onUpdate(memory)
        }
    }
    
    // 辅助方法
    private fun getCategoryColor(category: String): Color {
        return when (category.lowercase()) {
            "code" -> JBColor.GREEN
            "design" -> JBColor.MAGENTA
            "discussion" -> JBColor.ORANGE
            "important" -> JBColor.RED
            "idea" -> JBColor.CYAN
            "issue" -> JBColor.RED
            else -> UIUtil.getContextHelpForeground()
        }
    }
    
    private fun colorToHex(color: Color): String {
        return String.format("#%02x%02x%02x", color.red, color.green, color.blue)
    }
    
    private fun formatDateTime(dateTimeString: String): String {
        return try {
            dateTimeString.substring(0, 16).replace("T", " ")
        } catch (e: Exception) {
            dateTimeString
        }
    }
}
