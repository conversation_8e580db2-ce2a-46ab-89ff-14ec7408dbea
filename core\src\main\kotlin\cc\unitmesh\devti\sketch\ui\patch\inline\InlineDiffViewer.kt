package cc.unitmesh.devti.sketch.ui.patch.inline

import cc.unitmesh.devti.sketch.ui.code.EditorUtil
import com.intellij.lang.Language
import com.intellij.openapi.Disposable
import com.intellij.openapi.editor.EditorFactory
import com.intellij.openapi.editor.ex.EditorEx
import com.intellij.openapi.fileTypes.PlainTextLanguage
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Disposer
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBPanel
import com.intellij.util.ui.JBUI
import java.awt.BorderLayout
import java.awt.Dimension
import javax.swing.JComponent
import javax.swing.JPanel
import javax.swing.border.EmptyBorder

/**
 * 行内差异视图组件，实现类似图片中显示的行内差异高亮效果
 */
class InlineDiffViewer(
    private val project: Project,
    private val oldCode: String,
    private val newCode: String,
    private val language: Language? = null,
    private val fileName: String? = null
) : Disposable {
    
    private var editor: EditorEx? = null
    private var diffRenderer: DiffHighlightRenderer? = null
    private val mainPanel = JBPanel<InlineDiffViewer>(BorderLayout())
    private var statsLabel: JBLabel? = null
    
    init {
        initializeComponent()
    }
    
    /**
     * 初始化组件
     */
    private fun initializeComponent() {
        // 创建编辑器
        createEditor()
        
        // 计算并渲染差异
        calculateAndRenderDiffs()
        
        // 设置布局
        setupLayout()
    }
    
    /**
     * 创建编辑器
     */
    private fun createEditor() {
        val editorLanguage = language ?: PlainTextLanguage.INSTANCE
        
        // 使用新代码创建编辑器，因为我们要在其中显示差异
        editor = EditorUtil.createCodeViewerEditor(
            project, 
            newCode, 
            editorLanguage, 
            fileName, 
            this
        )
        
        // 配置编辑器
        editor?.let { ed ->
            ed.settings.apply {
                isLineNumbersShown = true
                isLineMarkerAreaShown = false
                isFoldingOutlineShown = false
                isRightMarginShown = false
                isWhitespacesShown = false
                isIndentGuidesShown = false
            }
            
            // 创建差异渲染器
            diffRenderer = DiffHighlightRenderer(ed)
        }
    }
    
    /**
     * 计算并渲染差异
     */
    private fun calculateAndRenderDiffs() {
        editor?.let { ed ->
            diffRenderer?.let { renderer ->
                // 计算行级差异
                val lineDiffs = DiffCalculator.calculateLineDiffs(oldCode, newCode)
                
                // 渲染差异
                renderer.renderLineDiffs(lineDiffs)
                
                // 更新统计信息
                updateStatsLabel(renderer.getDiffStats(lineDiffs))
            }
        }
    }
    
    /**
     * 设置布局
     */
    private fun setupLayout() {
        mainPanel.border = JBUI.Borders.customLine(JBUI.CurrentTheme.CustomFrameDecorations.separatorForeground(), 1)
        
        // 添加统计信息标签
        statsLabel = JBLabel().apply {
            border = EmptyBorder(4, 8, 4, 8)
        }
        
        val headerPanel = JPanel(BorderLayout()).apply {
            add(statsLabel, BorderLayout.WEST)
            border = JBUI.Borders.customLineBottom(JBUI.CurrentTheme.CustomFrameDecorations.separatorForeground())
        }
        
        mainPanel.add(headerPanel, BorderLayout.NORTH)
        
        // 添加编辑器
        editor?.let { ed ->
            mainPanel.add(ed.component, BorderLayout.CENTER)
        }
        
        // 设置首选大小
        mainPanel.preferredSize = Dimension(600, 400)
    }
    
    /**
     * 更新统计信息标签
     */
    private fun updateStatsLabel(stats: DiffHighlightRenderer.DiffStats) {
        statsLabel?.text = buildString {
            if (stats.addedLines > 0) {
                append("+${stats.addedLines}")
            }
            if (stats.deletedLines > 0) {
                if (isNotEmpty()) append(" ")
                append("-${stats.deletedLines}")
            }
            if (stats.modifiedLines > 0) {
                if (isNotEmpty()) append(" ")
                append("~${stats.modifiedLines}")
            }
            if (isEmpty()) {
                append("No changes")
            } else {
                append(" lines")
            }
        }
    }
    
    /**
     * 刷新差异显示
     */
    fun refreshDiffs() {
        calculateAndRenderDiffs()
    }
    
    /**
     * 获取主组件
     */
    fun getComponent(): JComponent = mainPanel
    
    /**
     * 设置编辑器大小
     */
    fun setPreferredSize(width: Int, height: Int) {
        mainPanel.preferredSize = Dimension(width, height)
        mainPanel.revalidate()
    }
    
    /**
     * 获取差异统计信息
     */
    fun getDiffStats(): DiffHighlightRenderer.DiffStats? {
        return diffRenderer?.let { renderer ->
            val lineDiffs = DiffCalculator.calculateLineDiffs(oldCode, newCode)
            renderer.getDiffStats(lineDiffs)
        }
    }
    
    /**
     * 清除所有高亮
     */
    fun clearHighlights() {
        diffRenderer?.clearHighlights()
    }
    
    /**
     * 检查是否有差异
     */
    fun hasDifferences(): Boolean {
        return oldCode != newCode
    }
    
    /**
     * 获取原始代码
     */
    fun getOldCode(): String = oldCode
    
    /**
     * 获取新代码
     */
    fun getNewCode(): String = newCode
    
    /**
     * 更新代码内容
     */
    fun updateContent(newOldCode: String, newNewCode: String) {
        // 创建新的查看器实例
        // 注意：这里简化处理，实际使用中可能需要更复杂的更新逻辑
        editor?.let { ed ->
            ed.document.setText(newNewCode)
            
            // 重新计算和渲染差异
            diffRenderer?.let { renderer ->
                val lineDiffs = DiffCalculator.calculateLineDiffs(newOldCode, newNewCode)
                renderer.renderLineDiffs(lineDiffs)
                updateStatsLabel(renderer.getDiffStats(lineDiffs))
            }
        }
    }
    
    override fun dispose() {
        diffRenderer?.dispose()
        editor?.let { ed ->
            if (!ed.isDisposed) {
                EditorFactory.getInstance().releaseEditor(ed)
            }
        }
    }
    
    companion object {
        /**
         * 创建行内差异视图的便捷方法
         */
        fun create(
            project: Project,
            oldCode: String,
            newCode: String,
            language: Language? = null,
            fileName: String? = null
        ): InlineDiffViewer {
            return InlineDiffViewer(project, oldCode, newCode, language, fileName)
        }
        
        /**
         * 检查两个代码是否有差异
         */
        fun hasDifferences(oldCode: String, newCode: String): Boolean {
            return oldCode != newCode
        }
    }
}
