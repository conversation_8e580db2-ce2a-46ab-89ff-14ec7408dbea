Write unit test for following ${context.lang} code.

${context.frameworkContext}

#if( $context.relatedClasses.length() > 0 )
Here is the relate code maybe you can use
${context.relatedClasses}
#end

#if( $context.currentClass.length() > 0 )
This is the class where the source code resides:
${context.currentClass}
#end
${context.extContext}
Here is the source code to be tested:

```$context.lang
${context.imports}
${context.sourceCode}
```

## if newFile
#if( $context.isNewFile )
Should include package and imports. Start method test code with Markdown code block here:
#else
Should include package and imports. Start ${context.testClassName} test code with Markdown code block here:
#end

