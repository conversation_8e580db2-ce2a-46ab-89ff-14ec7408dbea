package cc.unitmesh.devti.gui.toolbar

import cc.unitmesh.devti.gui.memory.MemoryBankDebugger
import com.intellij.icons.AllIcons
import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent

/**
 * 调试记忆银行 Action - 用于调试 Markdown 渲染问题
 */
class DebugMemoryBankAction : AnAction(
    "调试记忆银行",
    "调试记忆银行的 Markdown 渲染功能",
    AllIcons.Actions.StartDebugger
) {

    override fun getActionUpdateThread(): ActionUpdateThread = ActionUpdateThread.BGT

    override fun update(e: AnActionEvent) {
        val project = e.project
        e.presentation.isEnabled = project != null
    }

    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return
        
        // 在后台线程中运行调试
        Thread {
            try {
                MemoryBankDebugger.runAllDebugTests(project)
            } catch (ex: Exception) {
                ex.printStackTrace()
            }
        }.start()
    }
}
