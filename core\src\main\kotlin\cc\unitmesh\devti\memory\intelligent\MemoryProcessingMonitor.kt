package cc.unitmesh.devti.memory.intelligent

import cc.unitmesh.devti.memory.MemoryBankService
import com.intellij.notification.NotificationGroupManager
import com.intellij.notification.NotificationType
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.logger
import com.intellij.openapi.project.Project
import kotlinx.coroutines.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong

/**
 * 记忆处理状态监控器
 * 
 * 监控记忆处理的各个阶段，提供状态反馈和统计信息
 */
@Service(Service.Level.PROJECT)
class MemoryProcessingMonitor(private val project: Project) {
    
    companion object {
        fun getInstance(project: Project): MemoryProcessingMonitor {
            return project.getService(MemoryProcessingMonitor::class.java)
        }
    }
    
    private val logger = logger<MemoryProcessingMonitor>()
    private val monitorScope = CoroutineScope(Dispatchers.Default + SupervisorJob())
    
    // 处理状态跟踪
    private val processingStates = ConcurrentHashMap<String, MemoryProcessingState>()
    private val processingStats = ProcessingStatistics()
    
    // 事件监听器
    private val eventListeners = mutableListOf<MemoryMonitoringEventListener>()
    
    /**
     * 开始监控记忆处理
     */
    fun startProcessingMonitor(memoryId: String, title: String, source: String) {
        val state = MemoryProcessingState(
            memoryId = memoryId,
            title = title,
            source = source,
            startTime = System.currentTimeMillis(),
            currentStage = ProcessingStage.INITIATED
        )
        
        processingStates[memoryId] = state
        processingStats.totalInitiated.incrementAndGet()
        
        logger.info("开始监控记忆处理: $memoryId - $title")
        notifyListeners(MemoryMonitoringEvent.ProcessingStarted(memoryId, title))
    }
    
    /**
     * 更新处理阶段
     */
    fun updateProcessingStage(memoryId: String, stage: ProcessingStage, details: String = "") {
        val state = processingStates[memoryId]
        if (state != null) {
            val previousStage = state.currentStage
            state.currentStage = stage
            state.stageHistory.add(StageTransition(previousStage, stage, System.currentTimeMillis(), details))
            
            logger.debug("记忆处理阶段更新: $memoryId - $stage")
            notifyListeners(MemoryMonitoringEvent.StageChanged(memoryId, previousStage, stage, details))
            
            // 更新统计
            when (stage) {
                ProcessingStage.WORKING_MEMORY -> processingStats.workingMemoryCount.incrementAndGet()
                ProcessingStage.AI_EVALUATION -> processingStats.aiEvaluationCount.incrementAndGet()
                ProcessingStage.SHORT_TERM_MEMORY -> processingStats.shortTermMemoryCount.incrementAndGet()
                ProcessingStage.LONG_TERM_MEMORY -> processingStats.longTermMemoryCount.incrementAndGet()
                ProcessingStage.COMPLETED -> {
                    processingStats.totalCompleted.incrementAndGet()
                    completeProcessing(memoryId, true)
                }
                ProcessingStage.FAILED -> {
                    processingStats.totalFailed.incrementAndGet()
                    completeProcessing(memoryId, false)
                }
                else -> {}
            }
        }
    }
    
    /**
     * 记录AI摘要生成
     */
    fun recordAISummaryGeneration(memoryId: String, success: Boolean, summary: GeneratedSummary? = null) {
        val state = processingStates[memoryId]
        if (state != null) {
            state.aiSummaryGenerated = success
            state.generatedSummary = summary
            
            if (success) {
                processingStats.aiSummarySuccessCount.incrementAndGet()
                logger.info("AI摘要生成成功: $memoryId")
                notifyListeners(MemoryMonitoringEvent.AISummaryGenerated(memoryId, summary!!))
            } else {
                processingStats.aiSummaryFailureCount.incrementAndGet()
                logger.warn("AI摘要生成失败: $memoryId")
                notifyListeners(MemoryMonitoringEvent.AISummaryFailed(memoryId))
            }
        }
    }
    
    /**
     * 记录记忆银行保存
     */
    fun recordMemoryBankSave(memoryId: String, success: Boolean) {
        val state = processingStates[memoryId]
        if (state != null) {
            state.savedToMemoryBank = success
            
            if (success) {
                processingStats.memoryBankSaveCount.incrementAndGet()
                logger.info("记忆银行保存成功: $memoryId")
                notifyListeners(MemoryMonitoringEvent.MemoryBankSaved(memoryId))
            } else {
                logger.error("记忆银行保存失败: $memoryId")
                notifyListeners(MemoryMonitoringEvent.MemoryBankSaveFailed(memoryId))
            }
        }
    }
    
    /**
     * 完成处理
     */
    private fun completeProcessing(memoryId: String, success: Boolean) {
        val state = processingStates[memoryId]
        if (state != null) {
            state.endTime = System.currentTimeMillis()
            state.completed = true
            state.successful = success
            
            val duration = state.endTime!! - state.startTime
            processingStats.totalProcessingTime.addAndGet(duration)
            
            logger.info("记忆处理完成: $memoryId - 成功: $success - 耗时: ${duration}ms")
            notifyListeners(MemoryMonitoringEvent.ProcessingCompleted(memoryId, success, duration))
            
            // 延迟清理状态（保留一段时间用于查询）
            monitorScope.launch {
                delay(300000) // 5分钟后清理
                processingStates.remove(memoryId)
            }
        }
    }
    
    /**
     * 获取处理状态
     */
    fun getProcessingState(memoryId: String): MemoryProcessingState? {
        return processingStates[memoryId]
    }
    
    /**
     * 获取所有活跃的处理状态
     */
    fun getActiveProcessingStates(): List<MemoryProcessingState> {
        return processingStates.values.filter { !it.completed }
    }
    
    /**
     * 获取处理统计
     */
    fun getProcessingStatistics(): ProcessingStatistics {
        return processingStats.copy()
    }
    
    /**
     * 添加事件监听器
     */
    fun addEventListener(listener: MemoryMonitoringEventListener) {
        eventListeners.add(listener)
    }

    /**
     * 移除事件监听器
     */
    fun removeEventListener(listener: MemoryMonitoringEventListener) {
        eventListeners.remove(listener)
    }
    
    /**
     * 通知监听器
     */
    private fun notifyListeners(event: MemoryMonitoringEvent) {
        eventListeners.forEach { listener ->
            try {
                listener.onMemoryMonitoringEvent(event)
            } catch (e: Exception) {
                logger.error("通知监听器失败", e)
            }
        }
    }
    
    /**
     * 生成处理报告
     */
    fun generateProcessingReport(): String {
        val stats = getProcessingStatistics()
        val activeStates = getActiveProcessingStates()
        
        return buildString {
            append("# 记忆处理监控报告\n\n")
            append("生成时间: ${java.time.LocalDateTime.now()}\n\n")
            
            append("## 总体统计\n")
            append("- 总启动数: ${stats.totalInitiated.get()}\n")
            append("- 总完成数: ${stats.totalCompleted.get()}\n")
            append("- 总失败数: ${stats.totalFailed.get()}\n")
            append("- 成功率: ${"%.2f".format(stats.getSuccessRate())}%\n")
            append("- 平均处理时间: ${stats.getAverageProcessingTime()}ms\n\n")
            
            append("## 阶段统计\n")
            append("- 工作记忆: ${stats.workingMemoryCount.get()}\n")
            append("- AI评估: ${stats.aiEvaluationCount.get()}\n")
            append("- 短期记忆: ${stats.shortTermMemoryCount.get()}\n")
            append("- 长期记忆: ${stats.longTermMemoryCount.get()}\n\n")
            
            append("## AI摘要统计\n")
            append("- 成功生成: ${stats.aiSummarySuccessCount.get()}\n")
            append("- 生成失败: ${stats.aiSummaryFailureCount.get()}\n")
            append("- 成功率: ${"%.2f".format(stats.getAISummarySuccessRate())}%\n\n")
            
            append("## 记忆银行统计\n")
            append("- 保存成功: ${stats.memoryBankSaveCount.get()}\n\n")
            
            if (activeStates.isNotEmpty()) {
                append("## 活跃处理状态\n")
                activeStates.forEach { state ->
                    append("- ${state.title} (${state.memoryId}): ${state.currentStage}\n")
                }
            }
        }
    }
    
    /**
     * 显示处理通知
     */
    fun showProcessingNotification(memoryId: String, message: String, type: NotificationType) {
        NotificationGroupManager.getInstance()
            .getNotificationGroup("AutoDev.Memory")
            .createNotification("记忆处理", message, type)
            .notify(project)
    }
    
    /**
     * 清理过期状态
     */
    fun cleanupExpiredStates() {
        val now = System.currentTimeMillis()
        val expiredThreshold = 24 * 60 * 60 * 1000L // 24小时
        
        val expiredKeys = processingStates.entries
            .filter { (_, state) -> 
                state.completed && (state.endTime ?: now) < now - expiredThreshold 
            }
            .map { it.key }
        
        expiredKeys.forEach { key ->
            processingStates.remove(key)
        }
        
        if (expiredKeys.isNotEmpty()) {
            logger.info("清理了 ${expiredKeys.size} 个过期的处理状态")
        }
    }
}

/**
 * 记忆处理状态
 */
data class MemoryProcessingState(
    val memoryId: String,
    val title: String,
    val source: String,
    val startTime: Long,
    var endTime: Long? = null,
    var currentStage: ProcessingStage,
    var completed: Boolean = false,
    var successful: Boolean = false,
    var aiSummaryGenerated: Boolean = false,
    var savedToMemoryBank: Boolean = false,
    var generatedSummary: GeneratedSummary? = null,
    val stageHistory: MutableList<StageTransition> = mutableListOf()
)

/**
 * 处理阶段
 */
enum class ProcessingStage {
    INITIATED,          // 已启动
    WORKING_MEMORY,     // 工作记忆
    PERIODIC_EVALUATION,// 周期性评估
    AI_EVALUATION,      // AI评估
    SHORT_TERM_MEMORY,  // 短期记忆
    REINFORCEMENT,      // 强化学习
    LONG_TERM_MEMORY,   // 长期记忆
    MEMORY_BANK_SAVE,   // 记忆银行保存
    MARKDOWN_EXPORT,    // Markdown导出
    COMPLETED,          // 已完成
    FAILED,             // 失败
    FORGOTTEN           // 已遗忘
}

/**
 * 阶段转换记录
 */
data class StageTransition(
    val fromStage: ProcessingStage,
    val toStage: ProcessingStage,
    val timestamp: Long,
    val details: String = ""
)

/**
 * 处理统计
 */
data class ProcessingStatistics(
    val totalInitiated: AtomicLong = AtomicLong(0),
    val totalCompleted: AtomicLong = AtomicLong(0),
    val totalFailed: AtomicLong = AtomicLong(0),
    val totalProcessingTime: AtomicLong = AtomicLong(0),
    val workingMemoryCount: AtomicLong = AtomicLong(0),
    val aiEvaluationCount: AtomicLong = AtomicLong(0),
    val shortTermMemoryCount: AtomicLong = AtomicLong(0),
    val longTermMemoryCount: AtomicLong = AtomicLong(0),
    val aiSummarySuccessCount: AtomicLong = AtomicLong(0),
    val aiSummaryFailureCount: AtomicLong = AtomicLong(0),
    val memoryBankSaveCount: AtomicLong = AtomicLong(0)
) {
    fun getSuccessRate(): Double {
        val total = totalCompleted.get() + totalFailed.get()
        return if (total > 0) (totalCompleted.get().toDouble() / total) * 100 else 0.0
    }
    
    fun getAverageProcessingTime(): Long {
        val completed = totalCompleted.get()
        return if (completed > 0) totalProcessingTime.get() / completed else 0L
    }
    
    fun getAISummarySuccessRate(): Double {
        val total = aiSummarySuccessCount.get() + aiSummaryFailureCount.get()
        return if (total > 0) (aiSummarySuccessCount.get().toDouble() / total) * 100 else 0.0
    }
    
    fun copy(): ProcessingStatistics {
        return ProcessingStatistics(
            AtomicLong(totalInitiated.get()),
            AtomicLong(totalCompleted.get()),
            AtomicLong(totalFailed.get()),
            AtomicLong(totalProcessingTime.get()),
            AtomicLong(workingMemoryCount.get()),
            AtomicLong(aiEvaluationCount.get()),
            AtomicLong(shortTermMemoryCount.get()),
            AtomicLong(longTermMemoryCount.get()),
            AtomicLong(aiSummarySuccessCount.get()),
            AtomicLong(aiSummaryFailureCount.get()),
            AtomicLong(memoryBankSaveCount.get())
        )
    }
}

/**
 * 记忆监控事件
 */
sealed class MemoryMonitoringEvent {
    data class ProcessingStarted(val memoryId: String, val title: String) : MemoryMonitoringEvent()
    data class StageChanged(val memoryId: String, val fromStage: ProcessingStage, val toStage: ProcessingStage, val details: String) : MemoryMonitoringEvent()
    data class AISummaryGenerated(val memoryId: String, val summary: GeneratedSummary) : MemoryMonitoringEvent()
    data class AISummaryFailed(val memoryId: String) : MemoryMonitoringEvent()
    data class MemoryBankSaved(val memoryId: String) : MemoryMonitoringEvent()
    data class MemoryBankSaveFailed(val memoryId: String) : MemoryMonitoringEvent()
    data class ProcessingCompleted(val memoryId: String, val success: Boolean, val duration: Long) : MemoryMonitoringEvent()
}

/**
 * 记忆监控事件监听器
 */
interface MemoryMonitoringEventListener {
    fun onMemoryMonitoringEvent(event: MemoryMonitoringEvent)
}
