package cc.unitmesh.devti.gui.example

import java.awt.BorderLayout
import java.awt.Dimension
import java.awt.Font
import javax.swing.*

/**
 * 简单的测试结果显示器 - 不依赖 IntelliJ UI 组件
 */
class SimpleTestRunner {
    data class TestResult(val name: String, val success: Boolean, val duration: Long, val error: String?)
    
    fun runBasicTests(): List<TestResult> {
        val results = mutableListOf<TestResult>()
        
        // 测试1: 基础创建
        results.add(runTest("基础面板创建") {
            val panel = JPanel(BorderLayout())
            val header = JPanel()
            val content = JPanel()
            panel.add(header, BorderLayout.NORTH)
            panel.add(content, BorderLayout.CENTER)
            panel.componentCount == 2
        })
        
        // 测试2: 内容添加
        results.add(runTest("内容添加功能") {
            val panel = JPanel()
            panel.layout = BoxLayout(panel, BoxLayout.Y_AXIS)
            panel.add(JLabel("测试内容"))
            panel.add(JButton("测试按钮"))
            panel.componentCount == 2
        })
        
        // 测试3: 状态切换
        results.add(runTest("状态切换功能") {
            val panel = JPanel()
            var isVisible = true
            panel.isVisible = isVisible
            isVisible = !isVisible
            panel.isVisible = isVisible
            !panel.isVisible
        })
        
        // 测试4: 复杂布局
        results.add(runTest("复杂布局处理") {
            val mainPanel = JPanel(BorderLayout())
            val subPanel = JPanel()
            subPanel.layout = BoxLayout(subPanel, BoxLayout.Y_AXIS)
            subPanel.add(JLabel("标签"))
            subPanel.add(JButton("按钮"))
            mainPanel.add(subPanel, BorderLayout.CENTER)
            mainPanel.componentCount > 0
        })
        
        // 测试5: 鼠标事件处理
        results.add(runTest("鼠标事件处理") {
            val panel = JPanel()
            var clicked = false
            panel.addMouseListener(object : java.awt.event.MouseAdapter() {
                override fun mouseClicked(e: java.awt.event.MouseEvent) {
                    clicked = true
                }
            })
            // 模拟点击
            val event = java.awt.event.MouseEvent(
                panel, java.awt.event.MouseEvent.MOUSE_CLICKED, 
                System.currentTimeMillis(), 0, 10, 10, 1, false
            )
            panel.mouseListeners.forEach { it.mouseClicked(event) }
            clicked
        })
        
        return results
    }
    
    private fun runTest(name: String, test: () -> Boolean): TestResult {
        return try {
            val start = System.currentTimeMillis()
            val success = test()
            val duration = System.currentTimeMillis() - start
            TestResult(name, success, duration, null)
        } catch (e: Exception) {
            TestResult(name, false, 0, e.message)
        }
    }
    
    fun displayResults(results: List<TestResult>) {
        println("\n=== CollapsiblePanel 测试结果 ===")
        println("总测试数: ${results.size}")
        println("通过: ${results.count { it.success }}")
        println("失败: ${results.count { !it.success }}")
        println("成功率: ${String.format("%.1f", results.count { it.success } * 100.0 / results.size)}%")
        println("\n详细结果:")
        
        results.forEach { result ->
            val status = if (result.success) "✓ 通过" else "✗ 失败"
            val error = if (result.error != null) " - ${result.error}" else ""
            println("  ${result.name}: $status (${result.duration}ms)$error")
        }
      //  println("=" * 40)
    }
}

/**
 * 独立运行器 - 可以直接执行查看效果
 */
fun main() {
    SwingUtilities.invokeLater {
        val frame = JFrame("CollapsiblePanel 测试套件")
        frame.defaultCloseOperation = JFrame.EXIT_ON_CLOSE
        frame.size = Dimension(600, 500)
        
        val panel = JPanel()
        panel.layout = BoxLayout(panel, BoxLayout.Y_AXIS)
        panel.border = javax.swing.BorderFactory.createEmptyBorder(20, 20, 20, 20)
        
        val titleLabel = JLabel("CollapsiblePanel 测试套件")
        titleLabel.font = titleLabel.font.deriveFont(16f).deriveFont(Font.BOLD)
        titleLabel.alignmentX = JComponent.CENTER_ALIGNMENT
        
        val resultArea = JTextArea(15, 50)
        resultArea.isEditable = false
        resultArea.font = Font(Font.MONOSPACED, Font.PLAIN, 12)
        val scrollPane = JScrollPane(resultArea)
        
        val testRunner = SimpleTestRunner()
        
        val runTestButton = JButton("运行基础测试")
        runTestButton.alignmentX = JComponent.CENTER_ALIGNMENT
        runTestButton.addActionListener {
            resultArea.text = "正在运行测试...\n"
            
            SwingUtilities.invokeLater {
                val results = testRunner.runBasicTests()
                
                val output = StringBuilder()
                output.append("=== CollapsiblePanel 测试结果 ===\n")
                output.append("总测试数: ${results.size}\n")
                output.append("通过: ${results.count { it.success }}\n")
                output.append("失败: ${results.count { !it.success }}\n")
                output.append("成功率: ${String.format("%.1f", results.count { it.success } * 100.0 / results.size)}%\n\n")
                output.append("详细结果:\n")
                
                results.forEach { result ->
                    val status = if (result.success) "✓ 通过" else "✗ 失败"
                    val error = if (result.error != null) " - ${result.error}" else ""
                    output.append("  ${result.name}: $status (${result.duration}ms)$error\n")
                }
                output.append("=" + "=".repeat(39) + "\n")
                
                resultArea.text = output.toString()
                
                // 同时在控制台输出
                testRunner.displayResults(results)
            }
        }
        
        val demoButton = JButton("查看可视化演示")
        demoButton.alignmentX = JComponent.CENTER_ALIGNMENT
        demoButton.addActionListener {
            // 启动可视化测试
            println("启动可视化演示...")
            resultArea.append("\n\n启动可视化演示窗口...\n")
            resultArea.append("请查看新打开的演示窗口\n")
            
            // 调用可视化测试的 main 方法
           // cc.unitmesh.devti.gui.example.main()
        }
        
        val clearButton = JButton("清空结果")
        clearButton.alignmentX = JComponent.CENTER_ALIGNMENT
        clearButton.addActionListener {
            resultArea.text = ""
        }
        
        // 添加说明文本
        val instructionArea = JTextArea(3, 50)
        instructionArea.isEditable = false
        instructionArea.text = "使用说明：\n" +
                "1. 点击 '运行基础测试' 执行自动化测试\n" +
                "2. 点击 '查看可视化演示' 打开交互式演示窗口"
        instructionArea.background = panel.background
        instructionArea.font = Font(Font.SANS_SERIF, Font.ITALIC, 11)
        
        panel.add(titleLabel)
        panel.add(Box.createVerticalStrut(15))
        panel.add(instructionArea)
        panel.add(Box.createVerticalStrut(15))
        
        val buttonPanel = JPanel()
        buttonPanel.add(runTestButton)
        buttonPanel.add(Box.createHorizontalStrut(10))
        buttonPanel.add(demoButton)
        buttonPanel.add(Box.createHorizontalStrut(10))
        buttonPanel.add(clearButton)
        panel.add(buttonPanel)
        
        panel.add(Box.createVerticalStrut(15))
        panel.add(JLabel("测试结果:"))
        panel.add(Box.createVerticalStrut(5))
        panel.add(scrollPane)
        
        frame.add(panel)
        frame.setLocationRelativeTo(null)
        frame.isVisible = true
        
        println("CollapsiblePanel 测试套件已启动")
        println("点击 '运行基础测试' 按钮开始测试")
        println("点击 '查看可视化演示' 按钮查看UI效果")
    }
}
