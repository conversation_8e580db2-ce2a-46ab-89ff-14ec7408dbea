package cc.unitmesh.devti.memory.intelligent

import cc.unitmesh.devti.llms.LlmFactory
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.logger
import com.intellij.openapi.project.Project
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.collect

/**
 * 记忆摘要生成器 - 使用AI模型生成记忆摘要
 */
@Service(Service.Level.PROJECT)
class MemorySummaryGenerator(private val project: Project) {
    
    companion object {
        fun getInstance(project: Project): MemorySummaryGenerator {
            return project.getService(MemorySummaryGenerator::class.java)
        }
    }
    
    private val logger = logger<MemorySummaryGenerator>()
    private val summaryScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    /**
     * 生成记忆摘要
     */
    suspend fun generateSummary(
        title: String,
        content: String,
        context: Map<String, Any> = emptyMap()
    ): MemorySummaryResult {
        return withContext(Dispatchers.IO) {
            try {
                logger.info("开始生成记忆摘要: $title")
                
                val llm = LlmFactory.create(project)

                val prompt = createSummaryPrompt(title, content, context)

                // 使用stream方法并收集响应
                val responseBuilder = StringBuilder()
                val responseFlow = llm.stream(
                    prompt,
                    systemPrompt = "你是一个专业的内容摘要助手，擅长提取关键信息并生成结构化摘要。",
                    keepHistory = false,
                    usePlanForFirst = false
                )

                responseFlow.collect { chunk ->
                    responseBuilder.append(chunk)
                }

                val responseText = responseBuilder.toString().trim()
                if (responseText.isBlank()) {
                    return@withContext MemorySummaryResult.failure("AI模型返回空响应")
                }
                
                val parsedSummary = parseSummaryResponse(responseText!!)
                
                logger.info("记忆摘要生成成功: ${parsedSummary.summary.take(50)}...")
                
                MemorySummaryResult.success(parsedSummary)
                
            } catch (e: Exception) {
                logger.error("生成记忆摘要失败", e)
                MemorySummaryResult.failure("生成摘要失败: ${e.message}")
            }
        }
    }
    
    /**
     * 批量生成摘要
     */
    suspend fun generateBatchSummaries(
        memories: List<Triple<String, String, Map<String, Any>>>
    ): List<MemorySummaryResult> {
        return memories.map { (title, content, context) ->
            generateSummary(title, content, context)
        }
    }
    
    /**
     * 异步生成摘要
     */
    fun generateSummaryAsync(
        title: String,
        content: String,
        context: Map<String, Any> = emptyMap(),
        callback: (MemorySummaryResult) -> Unit
    ) {
        summaryScope.launch {
            val result = generateSummary(title, content, context)
            callback(result)
        }
    }
    
    /**
     * 创建摘要生成提示词
     */
    private fun createSummaryPrompt(
        title: String,
        content: String,
        context: Map<String, Any>
    ): String {
        val contextInfo = if (context.isNotEmpty()) {
            "\n上下文信息: ${context.entries.joinToString(", ") { "${it.key}: ${it.value}" }}"
        } else ""
        
        return """
你是一个专业的知识管理助手，需要为用户的记忆内容生成结构化摘要。

请根据以下内容生成记忆摘要：

标题: $title
内容: $content$contextInfo

请按照以下格式生成摘要：

## 摘要
[生成简洁明了的摘要，突出核心要点，控制在100-200字]

## 关键词
[提取3-8个关键词，用逗号分隔]

## 分类建议
[建议一个最合适的分类：code, design, discussion, issue, idea, general等]

## 重要性评级
[1-5星评级，说明理由]

## 相关概念
[列出2-5个相关概念或技术]

## 标签建议
[建议3-6个标签，用逗号分隔]

要求：
1. 摘要要准确反映内容核心
2. 关键词要具有代表性
3. 分类要准确合理
4. 重要性评级要有依据
5. 相关概念要有助于知识关联
6. 标签要便于检索和分类

请严格按照上述格式输出，不要添加其他内容。
        """.trimIndent()
    }
    
    /**
     * 解析AI响应
     */
    private fun parseSummaryResponse(response: String): GeneratedSummary {
        try {
            val sections = response.split("##").map { it.trim() }.filter { it.isNotEmpty() }
            
            var summary = ""
            var keywords = emptyList<String>()
            var suggestedCategory = "general"
            var importance = 3
            var relatedConcepts = emptyList<String>()
            var suggestedTags = emptyList<String>()
            var importanceReason = ""
            
            sections.forEach { section ->
                when {
                    section.startsWith("摘要") -> {
                        summary = section.substringAfter("摘要").trim()
                    }
                    section.startsWith("关键词") -> {
                        keywords = section.substringAfter("关键词")
                            .split(",", "，")
                            .map { it.trim() }
                            .filter { it.isNotEmpty() }
                    }
                    section.startsWith("分类建议") -> {
                        suggestedCategory = section.substringAfter("分类建议")
                            .trim()
                            .lowercase()
                            .takeIf { it.isNotEmpty() } ?: "general"
                    }
                    section.startsWith("重要性评级") -> {
                        val importanceText = section.substringAfter("重要性评级").trim()
                        importance = extractImportanceRating(importanceText)
                        importanceReason = importanceText
                    }
                    section.startsWith("相关概念") -> {
                        relatedConcepts = section.substringAfter("相关概念")
                            .split(",", "，", "\n", "•", "-")
                            .map { it.trim() }
                            .filter { it.isNotEmpty() && !it.startsWith("[") }
                    }
                    section.startsWith("标签建议") -> {
                        suggestedTags = section.substringAfter("标签建议")
                            .split(",", "，")
                            .map { it.trim() }
                            .filter { it.isNotEmpty() }
                    }
                }
            }
            
            return GeneratedSummary(
                summary = summary.ifEmpty { "AI生成的摘要解析失败" },
                keywords = keywords,
                suggestedCategory = suggestedCategory,
                importance = importance,
                importanceReason = importanceReason,
                relatedConcepts = relatedConcepts,
                suggestedTags = suggestedTags,
                rawResponse = response
            )
            
        } catch (e: Exception) {
            logger.warn("解析AI摘要响应失败", e)
            return GeneratedSummary(
                summary = "摘要解析失败，请查看原始内容",
                keywords = emptyList(),
                suggestedCategory = "general",
                importance = 3,
                importanceReason = "解析失败",
                relatedConcepts = emptyList(),
                suggestedTags = emptyList(),
                rawResponse = response
            )
        }
    }
    
    /**
     * 提取重要性评级
     */
    private fun extractImportanceRating(text: String): Int {
        // 查找数字评级
        val numberMatch = Regex("([1-5])").find(text)
        if (numberMatch != null) {
            return numberMatch.value.toInt()
        }
        
        // 查找星级评级
        val starCount = text.count { it == '★' || it == '*' }
        if (starCount in 1..5) {
            return starCount
        }
        
        // 根据关键词判断
        return when {
            text.contains("非常重要") || text.contains("极其重要") || text.contains("关键") -> 5
            text.contains("很重要") || text.contains("重要") -> 4
            text.contains("一般重要") || text.contains("中等") -> 3
            text.contains("较低") || text.contains("不太重要") -> 2
            text.contains("很低") || text.contains("不重要") -> 1
            else -> 3
        }
    }
    
    /**
     * 生成快速摘要（用于实时显示）
     */
    suspend fun generateQuickSummary(content: String, maxLength: Int = 100): String {
        return withContext(Dispatchers.Default) {
            try {
                // 如果内容较短，直接返回
                if (content.length <= maxLength) {
                    return@withContext content
                }
                
                // 简单的摘要算法：取前几句话
                val sentences = content.split("。", "！", "？", "\n")
                    .map { it.trim() }
                    .filter { it.isNotEmpty() }
                
                var summary = ""
                for (sentence in sentences) {
                    if (summary.length + sentence.length <= maxLength - 3) {
                        summary += sentence + "。"
                    } else {
                        break
                    }
                }
                
                if (summary.length < content.length) {
                    summary += "..."
                }
                
                summary.ifEmpty { content.take(maxLength) + "..." }
                
            } catch (e: Exception) {
                content.take(maxLength) + "..."
            }
        }
    }
    
    /**
     * 验证摘要质量
     */
    fun validateSummary(summary: GeneratedSummary): SummaryValidation {
        val issues = mutableListOf<String>()
        
        if (summary.summary.length < 20) {
            issues.add("摘要过短")
        }
        
        if (summary.summary.length > 500) {
            issues.add("摘要过长")
        }
        
        if (summary.keywords.isEmpty()) {
            issues.add("缺少关键词")
        }
        
        if (summary.keywords.size > 10) {
            issues.add("关键词过多")
        }
        
        if (summary.importance !in 1..5) {
            issues.add("重要性评级无效")
        }
        
        if (summary.suggestedTags.isEmpty()) {
            issues.add("缺少标签建议")
        }
        
        return SummaryValidation(
            isValid = issues.isEmpty(),
            issues = issues,
            score = calculateQualityScore(summary, issues)
        )
    }
    
    /**
     * 计算摘要质量分数
     */
    private fun calculateQualityScore(summary: GeneratedSummary, issues: List<String>): Double {
        var score = 100.0
        
        // 根据问题扣分
        score -= issues.size * 10
        
        // 根据内容质量加分
        if (summary.summary.length in 50..200) score += 10
        if (summary.keywords.size in 3..8) score += 10
        if (summary.relatedConcepts.isNotEmpty()) score += 5
        if (summary.suggestedTags.size in 3..6) score += 5
        if (summary.importanceReason.isNotEmpty()) score += 5
        
        return maxOf(0.0, minOf(100.0, score))
    }
}

/**
 * 生成的摘要数据
 */
data class GeneratedSummary(
    val summary: String,                    // 摘要内容
    val keywords: List<String>,             // 关键词
    val suggestedCategory: String,          // 建议分类
    val importance: Int,                    // 重要性评级
    val importanceReason: String,           // 重要性理由
    val relatedConcepts: List<String>,      // 相关概念
    val suggestedTags: List<String>,        // 建议标签
    val rawResponse: String                 // 原始AI响应
)

/**
 * 摘要生成结果
 */
data class MemorySummaryResult(
    val success: Boolean,
    val summary: GeneratedSummary?,
    val error: String?
) {
    companion object {
        fun success(summary: GeneratedSummary): MemorySummaryResult {
            return MemorySummaryResult(true, summary, null)
        }
        
        fun failure(error: String): MemorySummaryResult {
            return MemorySummaryResult(false, null, error)
        }
    }
}

/**
 * 摘要验证结果
 */
data class SummaryValidation(
    val isValid: Boolean,
    val issues: List<String>,
    val score: Double
)
