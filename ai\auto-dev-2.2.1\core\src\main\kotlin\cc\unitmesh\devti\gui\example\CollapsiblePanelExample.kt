class CollapsiblePanelExample(project: Project) : DialogWrapper(project) {
    init {
        title = "可折叠面板示例"
        init()
    }

    public override fun createCenterPanel(): JComponent {
        val mainPanel = JPanel(BorderLayout())
        mainPanel.preferredSize = Dimension(500, 400)

        val contentPanel = JPanel(BorderLayout())
        contentPanel.border = JBUI.Borders.empty(10)

        // 创建第一个可折叠面板
        val section1 = CollapsiblePanel("AutoDev for IntelliJ 项目介绍及架构信息", true)
        section1.addContent(JBLabel("这里是项目介绍内容..."))

        // 创建第二个可折叠面板
        val section2 = CollapsiblePanel("功能模块", false)
        section2.addContent(JBLabel("这里是功能模块内容..."))

        // 创建第三个可折叠面板
        val section3 = CollapsiblePanel("开发指南", false)
        section3.addContent(JBLabel("这里是开发指南内容..."))

        // 添加所有面板到内容区
        val sectionsPanel = JPanel(BorderLayout())
        sectionsPanel.add(section1, BorderLayout.NORTH)
        sectionsPanel.add(section2, BorderLayout.CENTER)
        sectionsPanel.add(section3, BorderLayout.SOUTH)

        // 添加滚动条
        contentPanel.add(JBScrollPane(sectionsPanel), BorderLayout.CENTER)
        mainPanel.add(contentPanel, BorderLayout.CENTER)

        return mainPanel
    }

    // 新增方法：显式获取 createCenterPanel 创建的组件
    fun getCreateCenterPanel(): JComponent {
        return createCenterPanel()
    }
}


fun main() {
    val project: Project = TODO("Provide a valid project instance if needed") // 实际运行时需要有效 project 上下文
    val example = CollapsiblePanelExample(project)
    example.title = "Collapsible Panel Manual Test"

    // 调用 getCreateCenterPanel 获取并展示 createCenterPanel 创建的内容
    val centerPanel = example.getCreateCenterPanel()
    example.setContentComponent(centerPanel) // 设置内容组件

    example.show()  // 调用 show() 来展示 createCenterPanel 创建的 UI 内容
}