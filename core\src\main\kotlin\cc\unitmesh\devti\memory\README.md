# 记忆银行功能设计文档

## 🎯 功能概述

记忆银行是 AutoDev 的一个新功能，用于存储、管理和检索用户的记忆摘要。它提供了一个直观的界面来组织和访问重要的信息片段，支持 **Markdown 格式**、分类、标签、搜索和重要性评级。

## ✨ Markdown 支持

### 📝 完整的 Markdown 渲染
- **实时预览**: 记忆内容以美观的 Markdown 格式显示
- **语法高亮**: 代码块支持语法高亮
- **丰富格式**: 支持标题、列表、表格、链接、图片等
- **编辑预览**: 添加/编辑时支持编辑和预览模式切换

## ✨ 主要特性

### 🏦 记忆管理
- **添加记忆**: 创建新的记忆摘要，包含标题、内容、分类、标签和重要性等级
- **编辑记忆**: 修改现有记忆的任何属性
- **删除记忆**: 移除不需要的记忆摘要
- **折叠展示**: 支持折叠/展开记忆内容，节省界面空间

### 🔍 搜索和过滤
- **全文搜索**: 在标题、内容和标签中搜索关键词
- **分类过滤**: 按分类筛选记忆摘要
- **标签搜索**: 按标签查找相关记忆
- **重要性排序**: 按重要性等级排序显示

### 📊 分类和标签
- **预设分类**: general、code、design、discussion 等
- **自定义分类**: 支持添加新的分类
- **多标签支持**: 每个记忆可以有多个标签
- **重要性等级**: 1-5 星评级系统

### 💾 数据管理
- **持久化存储**: 自动保存到项目配置文件
- **导入导出**: 支持 JSON 格式的数据导入导出
- **统计信息**: 显示记忆总数、分类数量等统计数据

## 🚀 使用方式

### 启动记忆银行

#### 方式一：通过 Sketch 工具窗口
1. 打开 AutoDev 工具窗口
2. 点击工具栏上的 "📖" 记忆银行按钮
3. 界面会切换到记忆银行模式
4. 点击 "← 返回" 按钮可以切换回原界面

#### 方式二：独立对话框
1. 通过 Actions 搜索 "记忆银行"
2. 执行对应的 Action
3. 打开独立的记忆银行对话框

### 添加记忆摘要

1. 点击 "添加记忆" 按钮
2. 填写以下信息：
   - **标题**: 记忆的简短标题
   - **分类**: 选择或输入分类（如 code、design 等）
   - **重要性**: 选择 1-5 星等级
   - **标签**: 用逗号分隔的标签列表
   - **内容**: 支持 Markdown 格式的详细内容
3. 使用 **编辑/预览** 按钮切换模式
4. 点击 "保存" 按钮

### Markdown 编辑技巧

#### 支持的 Markdown 语法
```markdown
# 一级标题
## 二级标题
### 三级标题

**粗体文本**
*斜体文本*
`行内代码`

```代码块
function example() {
    console.log("Hello, World!");
}
```

- 无序列表项
- 另一个列表项

1. 有序列表项
2. 另一个有序列表项

[链接文本](https://example.com)
![图片描述](https://example.com/image.png)

> 引用块文本

| 表格 | 列1 | 列2 |
|------|-----|-----|
| 行1  | 数据1 | 数据2 |
```

#### 实用示例
- **代码片段**: 保存常用的代码模板和解决方案
- **会议记录**: 使用表格和列表组织会议内容
- **学习笔记**: 用标题和引用块整理知识点
- **项目文档**: 创建结构化的项目说明

### 管理记忆摘要

#### 查看记忆
- 记忆以**美观的卡片**形式显示
- 点击展开/折叠按钮查看 **Markdown 渲染**的详细内容
- 显示分类标签、重要性星级和 **MD 标识**
- 显示创建时间和标签信息
- **复制按钮**: 一键复制 Markdown 原始内容

#### 编辑记忆
1. 点击记忆卡片上的编辑按钮（✏️）
2. 修改需要更新的信息
3. 点击 "保存" 保存更改

#### 删除记忆
1. 点击记忆卡片上的删除按钮（❌）
2. 确认删除操作

### 搜索和过滤

#### 文本搜索
1. 在搜索框中输入关键词
2. 系统会实时搜索标题、内容和标签
3. 显示匹配的记忆摘要

#### 分类过滤
1. 在分类下拉框中选择特定分类
2. 只显示该分类下的记忆摘要
3. 选择 "全部分类" 显示所有记忆

#### 组合搜索
- 可以同时使用文本搜索和分类过滤
- 结果会同时满足两个条件

### 数据导入导出

#### 导出数据
1. 点击 "导出" 按钮
2. 选择保存位置和文件名
3. 数据会以 JSON 格式保存

#### 导入数据
1. 点击 "导入" 按钮
2. 选择要导入的 JSON 文件
3. 确认导入操作

## 🏗️ 技术架构

### 核心组件

#### 1. MemoryBankService
- **职责**: 记忆数据的存储和管理
- **功能**: CRUD 操作、搜索、分类管理
- **存储**: 使用 IntelliJ Platform 的持久化机制

#### 2. MemoryBankDialog
- **职责**: 主界面和用户交互
- **功能**: 显示记忆列表、搜索过滤、操作按钮

#### 3. MemoryCard
- **职责**: 单个记忆的显示组件
- **功能**: 折叠展开、编辑删除操作

#### 4. MemoryBankAction
- **职责**: 启动记忆银行的 Action
- **功能**: 集成到工具栏和菜单系统

### 数据模型

```kotlin
data class MemorySummary(
    val id: String,                    // 唯一标识
    val title: String,                 // 标题
    val content: String,               // 内容
    val tags: List<String>,            // 标签列表
    val createdAt: String,             // 创建时间
    val updatedAt: String,             // 更新时间
    val category: String,              // 分类
    val importance: Int,               // 重要性 (1-5)
    val isExpanded: Boolean            // UI 状态
)
```

### 存储格式

记忆数据以 XML 格式存储在项目配置中：
```xml
<component name="MemoryBankService">
  <state>
    <memories>
      <memory id="..." title="..." content="..." ... />
    </memories>
    <categories>
      <category>general</category>
      <category>code</category>
    </categories>
  </state>
</component>
```

## 🎨 UI 设计特点

### 现代化界面
- 卡片式布局，清晰的视觉层次
- 折叠展开动画，节省空间
- 响应式设计，适配不同窗口大小

### 直观的操作
- 一键添加、编辑、删除
- 实时搜索和过滤
- 拖拽排序（未来功能）

### 信息密度优化
- 重要信息突出显示
- 次要信息适当隐藏
- 合理的间距和对比度

## 🔧 配置和扩展

### 自定义分类
用户可以添加自己的分类：
- 在添加/编辑记忆时输入新分类
- 系统会自动保存新分类
- 分类会出现在过滤下拉框中

### 标签系统
灵活的标签系统：
- 支持中英文标签
- 自动去重和排序
- 标签可以用于搜索和分组

### 重要性评级
5 星评级系统：
- ⭐ 一般重要
- ⭐⭐ 较重要
- ⭐⭐⭐ 重要
- ⭐⭐⭐⭐ 很重要
- ⭐⭐⭐⭐⭐ 极重要

## 📈 未来规划

### 即将推出
- **智能分类**: 基于内容自动建议分类
- **相关推荐**: 显示相关的记忆摘要
- **快速笔记**: 从代码或对话快速创建记忆
- **时间线视图**: 按时间顺序查看记忆

### 长期目标
- **AI 增强**: 使用 AI 自动总结和标记
- **协作功能**: 团队共享记忆库
- **插件集成**: 与其他工具的深度集成
- **移动端同步**: 跨设备同步记忆数据

## 🐛 故障排除

### 常见问题

#### 1. 记忆无法保存
**原因**: 权限问题或磁盘空间不足
**解决**: 检查项目目录权限和磁盘空间

#### 2. 搜索结果不准确
**原因**: 搜索关键词过于宽泛
**解决**: 使用更具体的关键词或组合过滤条件

#### 3. 界面显示异常
**原因**: 主题或字体设置问题
**解决**: 重启 IDE 或重置界面设置

#### 4. 导入失败
**原因**: JSON 格式错误或版本不兼容
**解决**: 检查 JSON 文件格式，确保是正确的导出文件

### 调试技巧
- 查看 IDE 日志中的错误信息
- 检查项目配置文件是否正常
- 尝试清空记忆数据重新开始

## 📝 开发指南

### 扩展记忆银行功能

#### 添加新的记忆类型
1. 扩展 `MemorySummary` 数据模型
2. 更新 UI 组件支持新字段
3. 修改存储和序列化逻辑

#### 自定义 UI 组件
1. 继承 `MemoryCard` 类
2. 实现自定义的显示逻辑
3. 注册到记忆银行系统

#### 集成外部数据源
1. 实现数据导入接口
2. 添加格式转换逻辑
3. 提供用户配置选项

---

## 🤝 贡献指南

欢迎贡献代码、报告问题或提出建议！请参考项目的贡献指南。

## 📄 许可证

本功能采用与 AutoDev 主项目相同的许可证。
