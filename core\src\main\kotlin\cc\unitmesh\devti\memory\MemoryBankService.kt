package cc.unitmesh.devti.memory

import com.intellij.openapi.components.Service
import com.intellij.openapi.components.State
import com.intellij.openapi.components.Storage
import com.intellij.openapi.components.PersistentStateComponent
import com.intellij.openapi.project.Project
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.*

/**
 * 记忆摘要数据模型
 */
@Serializable
data class MemorySummary(
    val id: String = UUID.randomUUID().toString(),
    val title: String,
    val content: String,
    val tags: List<String> = emptyList(),
    val createdAt: String = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
    val updatedAt: String = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
    val category: String = "general",
    val importance: Int = 1, // 1-5 重要性等级
    val isExpanded: Boolean = false // UI 状态：是否展开
)

/**
 * 记忆银行状态
 */
@Serializable
data class MemoryBankState(
    val memories: MutableList<MemorySummary> = mutableListOf(),
    val categories: MutableSet<String> = mutableSetOf("general", "code", "design", "discussion"),
    var lastUpdated: String = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
)

/**
 * 记忆银行服务 - 管理记忆摘要的存储和检索
 */
@Service(Service.Level.PROJECT)
@State(
    name = "MemoryBankService",
    storages = [Storage("memory-bank.xml")]
)
class MemoryBankService(private val project: Project) : PersistentStateComponent<MemoryBankState> {
    
    private var state = MemoryBankState()
    private val json = Json { 
        ignoreUnknownKeys = true
        prettyPrint = true
    }
    
    companion object {
        fun getInstance(project: Project): MemoryBankService {
            return project.getService(MemoryBankService::class.java)
        }
    }
    
    override fun getState(): MemoryBankState = state
    
    override fun loadState(state: MemoryBankState) {
        this.state = state
    }
    
    /**
     * 添加新的记忆摘要
     */
    fun addMemory(title: String, content: String, category: String = "general", tags: List<String> = emptyList(), importance: Int = 1): MemorySummary {
        val memory = MemorySummary(
            title = title,
            content = content,
            category = category,
            tags = tags,
            importance = importance
        )
        
        state.memories.add(0, memory) // 添加到列表开头
        state.categories.add(category)
        state.lastUpdated = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
        
        return memory
    }
    
    /**
     * 更新记忆摘要
     */
    fun updateMemory(id: String, title: String? = null, content: String? = null, 
                    category: String? = null, tags: List<String>? = null, importance: Int? = null): Boolean {
        val index = state.memories.indexOfFirst { it.id == id }
        if (index == -1) return false
        
        val oldMemory = state.memories[index]
        val updatedMemory = oldMemory.copy(
            title = title ?: oldMemory.title,
            content = content ?: oldMemory.content,
            category = category ?: oldMemory.category,
            tags = tags ?: oldMemory.tags,
            importance = importance ?: oldMemory.importance,
            updatedAt = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
        )
        
        state.memories[index] = updatedMemory
        category?.let { state.categories.add(it) }
        state.lastUpdated = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
        
        return true
    }
    
    /**
     * 删除记忆摘要
     */
    fun deleteMemory(id: String): Boolean {
        val removed = state.memories.removeIf { it.id == id }
        if (removed) {
            state.lastUpdated = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
        }
        return removed
    }
    
    /**
     * 获取所有记忆摘要
     */
    fun getAllMemories(): List<MemorySummary> {
        return state.memories.toList()
    }
    
    /**
     * 按分类获取记忆摘要
     */
    fun getMemoriesByCategory(category: String): List<MemorySummary> {
        return state.memories.filter { it.category == category }
    }
    
    /**
     * 按标签搜索记忆摘要
     */
    fun searchMemoriesByTag(tag: String): List<MemorySummary> {
        return state.memories.filter { memory ->
            memory.tags.any { it.contains(tag, ignoreCase = true) }
        }
    }
    
    /**
     * 按内容搜索记忆摘要
     */
    fun searchMemories(query: String): List<MemorySummary> {
        if (query.isBlank()) return getAllMemories()
        
        return state.memories.filter { memory ->
            memory.title.contains(query, ignoreCase = true) ||
            memory.content.contains(query, ignoreCase = true) ||
            memory.tags.any { it.contains(query, ignoreCase = true) }
        }
    }
    
    /**
     * 获取所有分类
     */
    fun getAllCategories(): Set<String> {
        return state.categories.toSet()
    }
    
    /**
     * 添加新分类
     */
    fun addCategory(category: String) {
        state.categories.add(category)
        state.lastUpdated = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
    }
    
    /**
     * 按重要性排序获取记忆摘要
     */
    fun getMemoriesByImportance(): List<MemorySummary> {
        return state.memories.sortedByDescending { it.importance }
    }
    
    /**
     * 获取最近的记忆摘要
     */
    fun getRecentMemories(limit: Int = 10): List<MemorySummary> {
        return state.memories.take(limit)
    }
    
    /**
     * 导出记忆摘要为 JSON
     */
    fun exportToJson(): String {
        return json.encodeToString(state)
    }
    
    /**
     * 从 JSON 导入记忆摘要
     */
    fun importFromJson(jsonString: String): Boolean {
        return try {
            val importedState = json.decodeFromString<MemoryBankState>(jsonString)
            this.state = importedState
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 清空所有记忆摘要
     */
    fun clearAllMemories() {
        state.memories.clear()
        state.lastUpdated = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
    }

    /**
     * 初始化示例记忆摘要（仅在首次使用时）
     */
    fun initializeSampleMemories() {
        if (state.memories.isNotEmpty()) return

        // 添加一些示例 Markdown 记忆
        addMemory(
            title = "Kotlin 协程基础",
            content = """
# Kotlin 协程基础

## 什么是协程？
协程是一种**轻量级线程**，可以在不阻塞线程的情况下暂停和恢复执行。

## 基本用法
```kotlin
import kotlinx.coroutines.*

fun main() = runBlocking {
    launch {
        delay(1000L)
        println("World!")
    }
    println("Hello,")
}
```

## 关键概念
- `suspend` 函数：可以暂停的函数
- `CoroutineScope`：协程作用域
- `Job`：协程的生命周期管理

> **注意**：协程需要在协程作用域中启动
            """.trimIndent(),
            category = "code",
            tags = listOf("kotlin", "协程", "并发"),
            importance = 4
        )

        addMemory(
            title = "UI 设计原则",
            content = """
# UI 设计原则

## 核心原则

### 1. 简洁性 (Simplicity)
- 去除不必要的元素
- 专注于**核心功能**
- 使用*清晰的视觉层次*

### 2. 一致性 (Consistency)
- 统一的颜色方案
- 一致的交互模式
- 标准化的组件使用

### 3. 可用性 (Usability)
- 直观的导航
- 清晰的反馈
- 容错设计

## 设计检查清单
- [ ] 颜色对比度是否足够？
- [ ] 字体大小是否合适？
- [ ] 交互元素是否明显？
- [ ] 响应式设计是否完善？

---
*记住：好的设计是看不见的设计*
            """.trimIndent(),
            category = "design",
            tags = listOf("UI", "设计", "用户体验"),
            importance = 5
        )

        addMemory(
            title = "Git 常用命令",
            content = """
# Git 常用命令速查

## 基础操作
```bash
# 初始化仓库
git init

# 添加文件
git add .
git add <file>

# 提交更改
git commit -m "commit message"

# 查看状态
git status
```

## 分支操作
```bash
# 创建并切换分支
git checkout -b <branch-name>

# 切换分支
git checkout <branch-name>

# 合并分支
git merge <branch-name>

# 删除分支
git branch -d <branch-name>
```

## 远程操作
```bash
# 添加远程仓库
git remote add origin <url>

# 推送到远程
git push origin <branch>

# 拉取更新
git pull origin <branch>
```

**提示**：使用 `git log --oneline` 查看简洁的提交历史
            """.trimIndent(),
            category = "code",
            tags = listOf("git", "版本控制", "命令行"),
            importance = 3
        )

        addMemory(
            title = "会议记录模板",
            content = """
# 会议记录 - [会议主题]

**时间**：YYYY-MM-DD HH:MM
**地点**：[会议地点/在线]
**主持人**：[姓名]
**参会人员**：[列出参会人员]

## 会议议程
1. [议题一]
2. [议题二]
3. [议题三]

## 讨论内容

### [议题一标题]
- **讨论要点**：
  - 要点1
  - 要点2
- **决定**：[具体决定]
- **负责人**：[姓名]
- **截止时间**：[日期]

### [议题二标题]
- **讨论要点**：
  - 要点1
  - 要点2
- **决定**：[具体决定]

## 行动项目 (Action Items)
| 任务 | 负责人 | 截止时间 | 状态 |
|------|--------|----------|------|
| [任务1] | [姓名] | [日期] | [ ] |
| [任务2] | [姓名] | [日期] | [ ] |

## 下次会议
- **时间**：[日期时间]
- **议题**：[预计讨论内容]

---
*会议记录由 [记录人] 整理*
            """.trimIndent(),
            category = "discussion",
            tags = listOf("会议", "模板", "协作"),
            importance = 2
        )
    }
    
    /**
     * 获取统计信息
     */
    fun getStatistics(): Map<String, Any> {
        return mapOf(
            "totalMemories" to state.memories.size,
            "categories" to state.categories.size,
            "averageImportance" to if (state.memories.isNotEmpty()) 
                state.memories.map { it.importance }.average() else 0.0,
            "lastUpdated" to state.lastUpdated
        )
    }
}
