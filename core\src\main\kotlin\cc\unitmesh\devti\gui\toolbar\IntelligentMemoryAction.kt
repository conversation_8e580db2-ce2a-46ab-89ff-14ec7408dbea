package cc.unitmesh.devti.gui.toolbar

import cc.unitmesh.devti.memory.intelligent.IntelligentMemoryFacade
import cc.unitmesh.devti.memory.intelligent.MemoryExportConfig
import cc.unitmesh.devti.memory.intelligent.MemoryUtils
import com.intellij.icons.AllIcons
import com.intellij.notification.NotificationGroupManager
import com.intellij.notification.NotificationType
import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.progress.ProgressIndicator
import com.intellij.openapi.progress.ProgressManager
import com.intellij.openapi.progress.Task
import com.intellij.openapi.ui.Messages
import kotlinx.coroutines.*

/**
 * 智能记忆处理 Action
 */
class IntelligentMemoryAction : AnAction(
    "智能记忆处理",
    "启动智能记忆处理和导出功能",
    AllIcons.Actions.Lightning
) {

    override fun getActionUpdateThread(): ActionUpdateThread = ActionUpdateThread.BGT

    override fun update(e: AnActionEvent) {
        val project = e.project
        e.presentation.isEnabled = project != null
    }

    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return
        
        val options = arrayOf(
            "添加智能记忆",
            "导出记忆到 Markdown",
            "测试上下文丰富",
            "查看记忆统计",
            "健康检查"
        )
        
        val choice = Messages.showChooseDialog(
            project,
            "请选择要执行的智能记忆操作：",
            "智能记忆处理",
            AllIcons.Actions.Lightning,
            options,
            options[0]
        )
        
        when (choice) {
            0 -> showAddMemoryDialog(project)
            1 -> exportMemoriesToMarkdown(project)
            2 -> testContextEnrichment(project)
            3 -> showMemoryStatistics(project)
            4 -> performHealthCheck(project)
        }
    }
    
    private fun showAddMemoryDialog(project: com.intellij.openapi.project.Project) {
        val title = Messages.showInputDialog(
            project,
            "请输入记忆标题：",
            "添加智能记忆",
            AllIcons.Actions.Edit
        ) ?: return
        
        val content = Messages.showInputDialog(
            project,
            "请输入记忆内容（支持 Markdown）：",
            "添加智能记忆",
            AllIcons.Actions.Edit
        ) ?: return
        
        if (title.isBlank() || content.isBlank()) {
            Messages.showWarningDialog(project, "标题和内容不能为空", "输入错误")
            return
        }
        
        // 异步处理记忆添加
        val facade = IntelligentMemoryFacade.getInstance(project)
        
        ProgressManager.getInstance().run(object : Task.Backgroundable(project, "处理智能记忆...", true) {
            override fun run(indicator: ProgressIndicator) {
                indicator.text = "正在处理记忆..."
                indicator.fraction = 0.3
                
                runBlocking {
                    try {
                        val result = facade.addMemoryWithContext(
                            title = title,
                            content = content,
                            source = "user_action",
                            context = mapOf(
                                "timestamp" to System.currentTimeMillis().toString(),
                                "source_action" to "IntelligentMemoryAction"
                            )
                        )
                        
                        indicator.fraction = 0.8
                        
                        ApplicationManager.getApplication().invokeLater {
                            if (result.success) {
                                showNotification(
                                    project,
                                    "智能记忆处理成功",
                                    "记忆 '$title' 已成功添加到智能记忆系统\nID: ${result.memoryId}",
                                    NotificationType.INFORMATION
                                )
                            } else {
                                showNotification(
                                    project,
                                    "智能记忆处理失败",
                                    result.message,
                                    NotificationType.ERROR
                                )
                            }
                        }
                        
                    } catch (e: Exception) {
                        ApplicationManager.getApplication().invokeLater {
                            showNotification(
                                project,
                                "智能记忆处理异常",
                                "处理过程中发生错误: ${e.message}",
                                NotificationType.ERROR
                            )
                        }
                    }
                }
                
                indicator.fraction = 1.0
            }
        })
    }
    
    private fun exportMemoriesToMarkdown(project: com.intellij.openapi.project.Project) {
        val options = arrayOf("导出所有记忆", "自定义导出设置")
        val choice = Messages.showChooseDialog(
            project,
            "请选择导出方式：",
            "导出记忆到 Markdown",
            AllIcons.Actions.Download,
            options,
            options[0]
        )
        
        val config = if (choice == 1) {
            // 自定义导出设置
            val groupByCategory = Messages.showYesNoDialog(
                project,
                "是否按分类组织文件？",
                "导出设置",
                AllIcons.General.QuestionDialog
            ) == Messages.YES
            
            val includeMetadata = Messages.showYesNoDialog(
                project,
                "是否包含元数据信息？",
                "导出设置",
                AllIcons.General.QuestionDialog
            ) == Messages.YES
            
            MemoryUtils.createExportConfig(
                groupByCategory = groupByCategory,
                includeMetadata = includeMetadata
            )
        } else {
            MemoryExportConfig()
        }
        
        val facade = IntelligentMemoryFacade.getInstance(project)
        
        ProgressManager.getInstance().run(object : Task.Backgroundable(project, "导出记忆到 Markdown...", true) {
            override fun run(indicator: ProgressIndicator) {
                indicator.text = "正在导出记忆..."
                indicator.fraction = 0.2
                
                runBlocking {
                    try {
                        val result = facade.exportToMarkdown(config)
                        
                        indicator.fraction = 0.9
                        
                        ApplicationManager.getApplication().invokeLater {
                            if (result.success) {
                                val message = "${result.message}\n导出位置: ${result.outputDirectory}\n文件数量: ${result.exportedFiles.size}"
                                showNotification(
                                    project,
                                    "记忆导出成功",
                                    message,
                                    NotificationType.INFORMATION
                                )
                                
                                // 询问是否打开导出目录
                                val openDir = Messages.showYesNoDialog(
                                    project,
                                    "导出完成！是否打开导出目录？",
                                    "导出成功",
                                    AllIcons.Actions.Show
                                ) == Messages.YES
                                
                                if (openDir && result.outputDirectory.isNotEmpty()) {
                                    try {
                                        val desktop = java.awt.Desktop.getDesktop()
                                        desktop.open(java.io.File(result.outputDirectory))
                                    } catch (e: Exception) {
                                        showNotification(
                                            project,
                                            "无法打开目录",
                                            "请手动打开: ${result.outputDirectory}",
                                            NotificationType.WARNING
                                        )
                                    }
                                }
                            } else {
                                showNotification(
                                    project,
                                    "记忆导出失败",
                                    result.message,
                                    NotificationType.ERROR
                                )
                            }
                        }
                        
                    } catch (e: Exception) {
                        ApplicationManager.getApplication().invokeLater {
                            showNotification(
                                project,
                                "导出过程异常",
                                "导出过程中发生错误: ${e.message}",
                                NotificationType.ERROR
                            )
                        }
                    }
                }
                
                indicator.fraction = 1.0
            }
        })
    }
    
    private fun testContextEnrichment(project: com.intellij.openapi.project.Project) {
        val context = Messages.showInputDialog(
            project,
            "请输入要丰富的上下文内容：",
            "测试上下文丰富",
            AllIcons.Actions.Find
        ) ?: return
        
        if (context.isBlank()) {
            Messages.showWarningDialog(project, "上下文内容不能为空", "输入错误")
            return
        }
        
        val facade = IntelligentMemoryFacade.getInstance(project)
        
        ProgressManager.getInstance().run(object : Task.Backgroundable(project, "丰富上下文...", true) {
            override fun run(indicator: ProgressIndicator) {
                indicator.text = "正在分析上下文..."
                indicator.fraction = 0.3
                
                runBlocking {
                    try {
                        val enrichedContext = facade.enrichContext(context)
                        val relatedMemories = facade.getRelatedMemories(context, 5)
                        
                        indicator.fraction = 0.8
                        
                        ApplicationManager.getApplication().invokeLater {
                            val resultMessage = buildString {
                                append("原始上下文长度: ${context.length} 字符\n")
                                append("丰富后长度: ${enrichedContext.length} 字符\n")
                                append("相关记忆数量: ${relatedMemories.size}\n\n")
                                
                                if (relatedMemories.isNotEmpty()) {
                                    append("相关记忆:\n")
                                    relatedMemories.forEachIndexed { index, memory ->
                                        append("${index + 1}. ${memory.title} (${memory.type.name}, 重要性: ${memory.importance})\n")
                                    }
                                    append("\n")
                                }
                                
                                append("丰富后的上下文:\n")
                                append("=" + 50 + "\n")
                                append(enrichedContext)
                            }
                            
                            Messages.showInfoMessage(project, resultMessage, "上下文丰富结果")
                        }
                        
                    } catch (e: Exception) {
                        ApplicationManager.getApplication().invokeLater {
                            showNotification(
                                project,
                                "上下文丰富失败",
                                "处理过程中发生错误: ${e.message}",
                                NotificationType.ERROR
                            )
                        }
                    }
                }
                
                indicator.fraction = 1.0
            }
        })
    }
    
    private fun showMemoryStatistics(project: com.intellij.openapi.project.Project) {
        val facade = IntelligentMemoryFacade.getInstance(project)
        
        try {
            val stats = facade.getMemoryStatistics()
            
            val message = buildString {
                append("记忆银行统计信息\n")
                append("=" + 30 + "\n")
                append("工作记忆: ${stats.workingMemoryCount} 个\n")
                append("短期记忆: ${stats.shortTermMemoryCount} 个\n")
                append("长期记忆: ${stats.longTermMemoryCount} 个\n")
                append("总处理量: ${stats.totalProcessed}\n")
                append("平均重要性: ${"%.2f".format(stats.averageImportance)}\n\n")
                
                if (stats.categoryDistribution.isNotEmpty()) {
                    append("分类分布:\n")
                    stats.categoryDistribution.forEach { (category, count) ->
                        append("  $category: $count 个\n")
                    }
                }
            }
            
            Messages.showInfoMessage(project, message, "记忆统计")
            
        } catch (e: Exception) {
            showNotification(
                project,
                "获取统计信息失败",
                "无法获取记忆统计信息: ${e.message}",
                NotificationType.ERROR
            )
        }
    }
    
    private fun performHealthCheck(project: com.intellij.openapi.project.Project) {
        val facade = IntelligentMemoryFacade.getInstance(project)
        
        try {
            val healthStatus = facade.healthCheck()
            
            val message = buildString {
                append("智能记忆系统健康检查\n")
                append("=" +30 + "\n")
                healthStatus.forEach { (service, status) ->
                    val statusText = if (status) "✅ 正常" else "❌ 异常"
                    append("$service: $statusText\n")
                }
                
                val allHealthy = healthStatus.values.all { it }
                append("\n整体状态: ${if (allHealthy) "✅ 系统正常" else "⚠️ 存在问题"}")
            }
            
            val notificationType = if (healthStatus.values.all { it }) {
                NotificationType.INFORMATION
            } else {
                NotificationType.WARNING
            }
            
            showNotification(project, "健康检查完成", message, notificationType)
            
        } catch (e: Exception) {
            showNotification(
                project,
                "健康检查失败",
                "无法执行健康检查: ${e.message}",
                NotificationType.ERROR
            )
        }
    }
    
    private fun showNotification(
        project: com.intellij.openapi.project.Project,
        title: String,
        content: String,
        type: NotificationType
    ) {
        NotificationGroupManager.getInstance()
            .getNotificationGroup("AutoDev.Memory")
            .createNotification(title, content, type)
            .notify(project)
    }
}
