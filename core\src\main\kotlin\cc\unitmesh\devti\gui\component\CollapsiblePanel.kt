package cc.unitmesh.devti.gui.component

import com.intellij.icons.AllIcons
import com.intellij.openapi.ui.VerticalFlowLayout
import com.intellij.ui.JBColor
import com.intellij.util.ui.JBUI
import com.intellij.util.ui.UIUtil
import java.awt.BorderLayout
import java.awt.Cursor
import java.awt.Dimension
import java.awt.event.MouseAdapter
import java.awt.event.MouseEvent
import javax.swing.JLabel
import javax.swing.JPanel

/**
 * 可折叠面板组件
 */
class CollapsiblePanel(title: String, initiallyExpanded: Boolean = false) : JPanel(BorderLayout()) {
    private val headerPanel = JPanel(BorderLayout()).apply {
        background = UIUtil.getPanelBackground()
        border = JBUI.Borders.empty(8, 12)
    }
    
    private val contentPanel = JPanel(VerticalFlowLayout(0, 0)).apply {
        background = UIUtil.getPanelBackground()
        border = JBUI.Borders.empty(0, 12, 8, 12)
    }
    
    private val titleLabel = JLabel(title).apply {
        font = JBUI.Fonts.label(14.0f)
    }
    
    private val toggleIcon = JLabel().apply {
        icon = if (initiallyExpanded) AllIcons.General.ArrowDown else AllIcons.General.ArrowRight
    }
    
    private var isExpanded = initiallyExpanded
    
    init {
        background = UIUtil.getPanelBackground()
        border = JBUI.Borders.customLine(JBColor.border(), 1, 0, 0, 0)
        
        // 设置标题面板
        headerPanel.add(toggleIcon, BorderLayout.WEST)
        headerPanel.add(titleLabel, BorderLayout.CENTER)
        
        // 设置鼠标样式和点击事件
        headerPanel.cursor = Cursor.getPredefinedCursor(Cursor.HAND_CURSOR)
        headerPanel.addMouseListener(object : MouseAdapter() {
            override fun mouseClicked(e: MouseEvent) {
                toggleExpanded()
            }
        })
        
        add(headerPanel, BorderLayout.NORTH)
        add(contentPanel, BorderLayout.CENTER)
        
        // 初始状态
        contentPanel.isVisible = isExpanded
    }
    
    /**
     * 切换展开/折叠状态
     */
    fun toggleExpanded() {
        isExpanded = !isExpanded
        toggleIcon.icon = if (isExpanded) AllIcons.General.ArrowDown else AllIcons.General.ArrowRight
        contentPanel.isVisible = isExpanded
        
        // 通知父容器重新布局
        revalidate()
        repaint()
    }
    
    /**
     * 设置展开状态
     */
    fun setExpanded(expanded: Boolean) {
        if (expanded != isExpanded) {
            toggleExpanded()
        }
    }
    
    /**
     * 添加内容到面板
     */
    fun addContent(component: java.awt.Component) {
        contentPanel.add(component)
    }
}