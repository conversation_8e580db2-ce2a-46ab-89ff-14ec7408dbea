<idea-plugin allow-bundled-update="true">
    <resource-bundle>messages.AutoDevBundle</resource-bundle>

    <depends>com.intellij.modules.platform</depends>
    <depends>com.intellij.modules.lang</depends>

    <depends optional="true" config-file="json-contrib.xml">com.intellij.modules.json</depends>
    <depends optional="true" config-file="docker.xml">Docker</depends>

    <extensions defaultExtensionNs="com.intellij">
        <fileBasedIndex implementation="cc.unitmesh.devti.envior.ShireEnvironmentIndex"/>

        <notificationGroup id="AutoDev.notification.group" displayType="STICKY_BALLOON" bundle="messages.AutoDevBundle"
                           key="name"/>

        <applicationConfigurable parentId="tools" instance="cc.unitmesh.devti.settings.AutoDevSettingsConfigurable"
                                 id="cc.unitmesh.devti.settings.AutoDevSettingsConfigurable"
                                 displayName="AutoDev"/>

        <projectConfigurable provider="cc.unitmesh.devti.settings.coder.AutoDevCoderConfigurableProvider"
                             parentId="cc.unitmesh.devti.settings.AutoDevSettingsConfigurable"
                             id="cc.unitmesh.autodevCoder"
                             bundle="messages.AutoDevBundle" key="settings.autodev.coder"/>

        <projectConfigurable provider="cc.unitmesh.devti.settings.customize.CustomizeConfigurableProvider"
                             parentId="cc.unitmesh.devti.settings.AutoDevSettingsConfigurable"
                             id="cc.unitmesh.counit"
                             bundle="messages.AutoDevBundle" key="settings.customize.title"/>

        <projectConfigurable provider="cc.unitmesh.devti.settings.devops.AutoDevDevOpsConfigurableProvider"
                             parentId="cc.unitmesh.devti.settings.AutoDevSettingsConfigurable"
                             id="cc.unitmesh.autodevDevOps"
                             bundle="messages.AutoDevBundle" key="settings.autodev.devops"/>

        <applicationService serviceImplementation="cc.unitmesh.devti.settings.AutoDevSettingsState"/>

        <applicationService
                serviceInterface="cc.unitmesh.devti.inlay.codecomplete.LLMInlayManager"
                serviceImplementation="cc.unitmesh.devti.inlay.codecomplete.LLMInlayManagerImpl"/>

        <typedHandler order="first, before completionAutoPopup"
                      implementation="cc.unitmesh.devti.inlay.TypeOverHandler"/>


        <statusBarWidgetFactory id="AutoDevAIAssistant"
                                implementation="cc.unitmesh.devti.statusbar.AutoDevStatusBarWidgetFactory"/>

        <toolWindow id="AutoDev"
                    doNotActivateOnStart="true"
                    anchor="right"
                    secondary="false"
                    canCloseContents="true"
                    icon="cc.unitmesh.devti.AutoDevIcons.AI_COPILOT"
                    factoryClass="cc.unitmesh.devti.gui.AutoDevToolWindowFactory"/>

        <toolWindow id="AutoDevPlanner"
                    anchor="left"
                    order="after Structure"
                    icon="cc.unitmesh.devti.AutoDevIcons.PLANNER"
                    secondary="true"
                    factoryClass="cc.unitmesh.devti.gui.AutoDevPlannerToolWindowFactory"/>

        <notificationGroup id="AutoDev.notify" displayType="STICKY_BALLOON" bundle="messages.AutoDevBundle"
                           key="name"/>

        <intentionAction>
            <className>cc.unitmesh.devti.intentions.AutoDevIntentionHelper</className>
            <categoryKey>intention.category.llm</categoryKey>
        </intentionAction>

        <highlightErrorFilter implementation="cc.unitmesh.devti.gui.snippet.error.CodeBlockHighlightErrorFilter"/>
        <daemon.highlightInfoFilter implementation="cc.unitmesh.devti.gui.snippet.error.CodeBlockHighlightingFilter"/>
        <defaultHighlightingSettingProvider
                implementation="cc.unitmesh.devti.gui.snippet.error.CodeBlockHighlightingSettingsProvider"/>
        <daemon.intentionActionFilter
                implementation="cc.unitmesh.devti.gui.snippet.error.CodeBlockIntentionActionFilter"/>

        <httpRequestHandler implementation="cc.unitmesh.devti.mcp.host.MCPService"/>
        <notificationGroup id="UnitMesh.MCPServer" displayType="BALLOON"/>
    </extensions>

    <extensions defaultExtensionNs="JavaScript.JsonSchema">
        <ProviderFactory implementation="cc.unitmesh.devti.custom.schema.AutoDevJsonSchemaProviderFactory"/>
    </extensions>

    <extensionPoints>
        <extensionPoint qualifiedName="cc.unitmesh.autoDevIntention"
                        beanClass="com.intellij.codeInsight.intention.IntentionActionBean"
                        dynamic="true">
            <with tag="className" implements="com.intellij.codeInsight.intention.IntentionAction"/>
        </extensionPoint>

        <extensionPoint qualifiedName="cc.unitmesh.fileContextBuilder"
                        beanClass="com.intellij.lang.LanguageExtensionPoint" dynamic="true">
            <with attribute="implementationClass"
                  implements="cc.unitmesh.devti.context.builder.FileContextBuilder"/>
        </extensionPoint>

        <extensionPoint qualifiedName="cc.unitmesh.classContextBuilder"
                        beanClass="com.intellij.lang.LanguageExtensionPoint" dynamic="true">
            <with attribute="implementationClass"
                  implements="cc.unitmesh.devti.context.builder.ClassContextBuilder"/>
        </extensionPoint>

        <extensionPoint qualifiedName="cc.unitmesh.methodContextBuilder"
                        beanClass="com.intellij.lang.LanguageExtensionPoint" dynamic="true">
            <with attribute="implementationClass"
                  implements="cc.unitmesh.devti.context.builder.MethodContextBuilder"/>
        </extensionPoint>

        <extensionPoint qualifiedName="cc.unitmesh.codeModifier"
                        beanClass="com.intellij.lang.LanguageExtensionPoint" dynamic="true">
            <with attribute="implementationClass"
                  implements="cc.unitmesh.devti.context.builder.CodeModifier"/>
        </extensionPoint>

        <extensionPoint qualifiedName="cc.unitmesh.variableContextBuilder"
                        beanClass="com.intellij.lang.LanguageExtensionPoint" dynamic="true">
            <with attribute="implementationClass"
                  implements="cc.unitmesh.devti.context.builder.VariableContextBuilder"/>
        </extensionPoint>

        <extensionPoint qualifiedName="cc.unitmesh.livingDocumentation"
                        beanClass="com.intellij.lang.LanguageExtensionPoint" dynamic="true">
            <with attribute="implementationClass" implements="cc.unitmesh.devti.provider.LivingDocumentation"/>
        </extensionPoint>

        <extensionPoint qualifiedName="cc.unitmesh.testDataBuilder"
                        beanClass="com.intellij.lang.LanguageExtensionPoint" dynamic="true">
            <with attribute="implementationClass" implements="cc.unitmesh.devti.provider.PsiElementDataBuilder"/>
        </extensionPoint>

        <extensionPoint qualifiedName="cc.unitmesh.refactoringTool"
                        beanClass="com.intellij.lang.LanguageExtensionPoint" dynamic="true">
            <with attribute="implementationClass" implements="cc.unitmesh.devti.provider.RefactoringTool"/>
        </extensionPoint>

        <extensionPoint qualifiedName="cc.unitmesh.buildSystemProvider"
                        interface="cc.unitmesh.devti.provider.BuildSystemProvider"
                        dynamic="true"/>

        <extensionPoint qualifiedName="cc.unitmesh.customPromptProvider"
                        beanClass="com.intellij.lang.LanguageExtensionPoint" dynamic="true">
            <with attribute="implementationClass" implements="cc.unitmesh.devti.provider.CustomPromptProvider"/>
        </extensionPoint>

        <!-- TODO: find better way to share context -->
        <extensionPoint qualifiedName="cc.unitmesh.contextPrompter"
                        interface="cc.unitmesh.devti.provider.ContextPrompter"
                        dynamic="true"/>

        <extensionPoint qualifiedName="cc.unitmesh.testContextProvider"
                        interface="cc.unitmesh.devti.provider.AutoTestService"
                        dynamic="true"/>

        <extensionPoint qualifiedName="cc.unitmesh.runProjectService"
                        interface="cc.unitmesh.devti.provider.ProjectRunService"
                        dynamic="true"/>

        <extensionPoint qualifiedName="cc.unitmesh.chatContextProvider"
                        interface="cc.unitmesh.devti.provider.context.ChatContextProvider"
                        dynamic="true"/>

        <extensionPoint qualifiedName="cc.unitmesh.languageProcessor"
                        interface="cc.unitmesh.devti.provider.devins.LanguageProcessor"
                        dynamic="true"/>

        <extensionPoint qualifiedName="cc.unitmesh.customDevInsCompletionProvider"
                        interface="cc.unitmesh.devti.provider.devins.DevInsSymbolProvider"
                        dynamic="true"/>

        <extensionPoint qualifiedName="cc.unitmesh.jsonTextProvider"
                        interface="cc.unitmesh.devti.provider.local.JsonTextProvider"
                        dynamic="true"/>

        <extensionPoint qualifiedName="cc.unitmesh.httpClientExecutor"
                        interface="cc.unitmesh.devti.provider.http.HttpClientProvider"
                        dynamic="true"/>

        <!-- Lang Sketch Provider -->
        <extensionPoint qualifiedName="cc.unitmesh.langSketchProvider"
                        interface="cc.unitmesh.devti.sketch.ui.LanguageSketchProvider"
                        dynamic="true"/>
        <extensionPoint qualifiedName="cc.unitmesh.sketchToolchainProvider"
                        interface="cc.unitmesh.devti.sketch.SketchToolchainProvider"
                        dynamic="true"/>

        <extensionPoint qualifiedName="cc.unitmesh.relatedClassProvider"
                        beanClass="com.intellij.lang.LanguageExtensionPoint" dynamic="true">
            <with attribute="implementationClass" implements="cc.unitmesh.devti.provider.RelatedClassesProvider"/>
        </extensionPoint>

        <extensionPoint qualifiedName="cc.unitmesh.runService"
                        interface="cc.unitmesh.devti.provider.RunService"
                        dynamic="true"/>

        <!-- Toolchain Function Provider -->
        <extensionPoint qualifiedName="cc.unitmesh.toolchainFunctionProvider"
                        interface="cc.unitmesh.devti.provider.toolchain.ToolchainFunctionProvider"
                        dynamic="true">
        </extensionPoint>

        <extensionPoint qualifiedName="cc.unitmesh.revisionProvider"
                        interface="cc.unitmesh.devti.provider.RevisionProvider"
                        dynamic="true">
        </extensionPoint>

        <extensionPoint qualifiedName="cc.unitmesh.frameworkConfigProvider"
                        interface="cc.unitmesh.devti.provider.FrameworkConfigProvider"
                        dynamic="true">
        </extensionPoint>

        <!--  Bridge -->
        <extensionPoint qualifiedName="cc.unitmesh.componentProvider"
                        interface="cc.unitmesh.devti.bridge.provider.ComponentViewProvider"
                        dynamic="true"/>
        <extensionPoint qualifiedName="cc.unitmesh.knowledgeWebApiProvide"
                        interface="cc.unitmesh.devti.bridge.provider.KnowledgeWebApiProvider"
                        dynamic="true"/>

        <!-- mcp -->
        <extensionPoint qualifiedName="cc.unitmesh.mcpTool"
                        interface="cc.unitmesh.devti.mcp.host.McpTool"
                        dynamic="true"/>

        <!-- DevIns Tool -->
        <extensionPoint qualifiedName="cc.unitmesh.devInsAgentTool"
                        interface="cc.unitmesh.devti.provider.DevInsAgentToolCollector"
                        dynamic="true"/>

        <extensionPoint qualifiedName="cc.unitmesh.agentObserver"
                        interface="cc.unitmesh.devti.provider.observer.AgentObserver"
                        dynamic="true"/>

        <!-- Indexer -->
        <extensionPoint qualifiedName="cc.unitmesh.langDictProvider"
                        interface="cc.unitmesh.devti.indexer.provider.LangDictProvider"
                        dynamic="true"
        />


        <extensionPoint qualifiedName="cc.unitmesh.shireActionLocationEditor"
                        interface="cc.unitmesh.devti.devins.provider.ActionLocationEditor"
                        dynamic="true"/>


        <extensionPoint qualifiedName="cc.unitmesh.shireFileCreateService"
                        beanClass="com.intellij.lang.LanguageExtensionPoint"
                        dynamic="true">
            <with attribute="implementationClass" implements="cc.unitmesh.devti.devins.provider.FileCreateService"/>
        </extensionPoint>

        <!-- Toolchain Provider -->
        <extensionPoint qualifiedName="cc.unitmesh.shireLanguageToolchainProvider"
                        beanClass="com.intellij.lang.LanguageExtensionPoint" dynamic="true">
            <with attribute="implementationClass"
                  implements="cc.unitmesh.devti.devins.provider.LanguageToolchainProvider"/>
        </extensionPoint>

        <extensionPoint qualifiedName="cc.unitmesh.shirePsiVariableProvider"
                        beanClass="com.intellij.lang.LanguageExtensionPoint" dynamic="true">
            <with attribute="implementationClass"
                  implements="cc.unitmesh.devti.devins.provider.PsiContextVariableProvider"/>
        </extensionPoint>

        <!-- PSI Query Expression -->
        <extensionPoint qualifiedName="cc.unitmesh.shirePsiQLInterpreter"
                        beanClass="com.intellij.lang.LanguageExtensionPoint" dynamic="true">
            <with attribute="implementationClass"
                  implements="cc.unitmesh.devti.devins.provider.shireql.ShireQLInterpreter"/>
        </extensionPoint>

        <!-- Toolchain Variable Provider -->
        <extensionPoint qualifiedName="cc.unitmesh.shireToolchainVariableProvider"
                        interface="cc.unitmesh.devti.devins.provider.ToolchainVariableProvider"
                        dynamic="true">
        </extensionPoint>

        <!-- Toolchain Function Provider -->
        <extensionPoint qualifiedName="cc.unitmesh.shireToolchainFunctionProvider"
                        interface="cc.unitmesh.devti.devins.provider.ToolchainFunctionProvider"
                        dynamic="true">
        </extensionPoint>

        <!-- Post Code Middleware -->
        <extensionPoint qualifiedName="cc.unitmesh.shirePostProcessor"
                        interface="cc.unitmesh.devti.devins.post.PostProcessor"
                        dynamic="true"/>

        <!-- Location Interaction  -->
        <extensionPoint qualifiedName="cc.unitmesh.shireLocationInteraction"
                        interface="cc.unitmesh.devti.devins.provider.LocationInteractionProvider"
                        dynamic="true"/>

        <extensionPoint qualifiedName="cc.unitmesh.shirePsiCapture"
                        beanClass="com.intellij.lang.LanguageExtensionPoint" dynamic="true">
            <with attribute="implementationClass" implements="cc.unitmesh.devti.devins.provider.PsiCapture"/>
        </extensionPoint>

        <extensionPoint qualifiedName="cc.unitmesh.shireSymbolProvider"
                        interface="cc.unitmesh.devti.devins.provider.ShireSymbolProvider"
                        dynamic="true"/>

        <extensionPoint qualifiedName="cc.unitmesh.shireQLDataProvider"
                        interface="cc.unitmesh.devti.devins.provider.ShireQLDataProvider"
                        dynamic="true"/>

        <extensionPoint qualifiedName="cc.unitmesh.shireRevisionProvider"
                        interface="cc.unitmesh.devti.devins.provider.RevisionProvider"
                        dynamic="true"/>

        <extensionPoint qualifiedName="cc.unitmesh.shireTerminalExecutor"
                        interface="cc.unitmesh.devti.language.provider.TerminalLocationExecutor"
                        dynamic="true"/>

        <extensionPoint qualifiedName="cc.unitmesh.shireHttpHandler"
                        interface="cc.unitmesh.devti.devins.provider.http.HttpHandler"
                        dynamic="true"/>

        <!-- Code Complexity -->
        <extensionPoint qualifiedName="cc.unitmesh.shireComplexityProvider"
                        beanClass="com.intellij.lang.LanguageExtensionPoint" dynamic="true">
            <with attribute="implementationClass"
                  implements="cc.unitmesh.devti.devins.provider.ComplexityProvider"/>
        </extensionPoint>
    </extensionPoints>

<!--    <projectListeners>-->
<!--        <listener class="cc.unitmesh.devti.practise.RenameLookupManagerListener"-->
<!--                  topic="com.intellij.codeInsight.lookup.LookupManagerListener"/>-->
<!--    </projectListeners>-->

    <extensions defaultExtensionNs="cc.unitmesh">
        <autoDevIntention>
            <className>cc.unitmesh.devti.intentions.action.NewChatWithCodeBaseIntention</className>
            <bundleName>messages.AutoDevBundle</bundleName>
            <categoryKey>intention.category.llm</categoryKey>
        </autoDevIntention>
        <autoDevIntention>
            <className>cc.unitmesh.devti.intentions.action.AutoTestThisIntention</className>
            <bundleName>messages.AutoDevBundle</bundleName>
            <categoryKey>intention.category.llm</categoryKey>
        </autoDevIntention>
        <autoDevIntention>
            <className>cc.unitmesh.devti.intentions.action.DefaultDocumentationBaseIntention</className>
            <bundleName>messages.AutoDevBundle</bundleName>
            <categoryKey>intention.category.llm</categoryKey>
        </autoDevIntention>

        <chatContextProvider implementation="cc.unitmesh.devti.provider.context.IdeVersionChatContextProvider"/>

        <langSketchProvider implementation="cc.unitmesh.devti.sketch.ui.patch.DiffLangSketchProvider"/>
        <langSketchProvider implementation="cc.unitmesh.devti.sketch.ui.webview.WebpageSketchProvider"/>
        <langSketchProvider implementation="cc.unitmesh.devti.sketch.ui.openapi.OpenAPISketchProvider"/>
        <langSketchProvider implementation="cc.unitmesh.devti.sketch.ui.MarkdownPreviewSketchProvider"/>
        <langSketchProvider implementation="cc.unitmesh.devti.sketch.ui.PlanSketchProvider"/>

        <toolchainFunctionProvider implementation="cc.unitmesh.devti.bridge.archview.ComponentViewFunctionProvider"/>
        <toolchainFunctionProvider implementation="cc.unitmesh.devti.bridge.archview.ContainerViewFunctionProvider"/>
        <toolchainFunctionProvider implementation="cc.unitmesh.devti.bridge.assessment.SccFunctionProvider"/>
        <toolchainFunctionProvider implementation="cc.unitmesh.devti.bridge.knowledge.HistoryFunctionProvider"/>
        <toolchainFunctionProvider implementation="cc.unitmesh.devti.bridge.knowledge.KnowledgeFunctionProvider"/>
        <toolchainFunctionProvider implementation="cc.unitmesh.devti.mcp.client.McpFunctionProvider"/>

        <agentObserver implementation="cc.unitmesh.devti.observer.TestAgentObserver" />
        <agentObserver implementation="cc.unitmesh.devti.observer.BuiltTaskAgentObserver" />
<!--        <agentObserver implementation="cc.unitmesh.devti.observer.AddDependencyAgentObserver" />-->
    </extensions>

    <actions>
        <action id="llm.applyInlays"
                class="cc.unitmesh.devti.actions.completion.LLMApplyInlaysAction">
            <keyboard-shortcut first-keystroke="TAB" keymap="$default"/>
            <override-text place="MainMenu" text="Apply Completions to Editor"/>
            <override-text place="EditorPopup" text="Accept"/>
        </action>

        <action id="cc.unitmesh.devti.inlayCompleteCode"
                class="cc.unitmesh.devti.actions.completion.InlayCompleteCodeAction"
                text="Inlay Complete Code (AutoDev)"
                description="Inlay complete code!">
            <keyboard-shortcut keymap="$default" first-keystroke="alt PERIOD"/>
            <add-to-group group-id="ShowIntentionsGroup" relative-to-action="ShowIntentionActions" anchor="after"/>
        </action>
        <action id="cc.unitmesh.devti.disposeInlayCompleteCode"
                class="cc.unitmesh.devti.actions.completion.InlayCompleteCodeDisposedAction"
                text="Disposed Inlay Complete Code"
                description="Disposed Inlay complete code!">
            <keyboard-shortcut keymap="$default" first-keystroke="ESCAPE"/>
        </action>

        <group id="AutoDevIntentionsActionGroup" class="cc.unitmesh.devti.intentions.IntentionsActionGroup"
               icon="cc.unitmesh.devti.AutoDevIcons.AI_COPILOT" searchable="false">

            <add-to-group group-id="ShowIntentionsGroup" relative-to-action="ShowIntentionActions" anchor="after"/>
        </group>

        <!-- For right click -->
        <group id="autodev.groups.AutoChatDynamicActionGroup" popup="true" description="AutoDev chat"
               class="cc.unitmesh.devti.actions.groups.AutoChatDynamicActionGroup">
            <action id="cc.unitmesh.devti.actions.chat.ExplainThisAction"
                    class="cc.unitmesh.devti.actions.chat.ExplainThisAction"
                    description="Ask AI about this code">
            </action>

            <action id="cc.unitmesh.devti.actions.chat.RefactorThisAction"
                    class="cc.unitmesh.devti.actions.chat.RefactorThisAction"
                    description="Ask AI refactor this code">
            </action>

            <action id="cc.unitmesh.devti.actions.chat.FixThisAction"
                    class="cc.unitmesh.devti.actions.chat.FixThisAction"
                    description="Ask AI refactor this code">
            </action>

            <action id="cc.unitmesh.devti.actions.chat.ChatWithThisAction"
                    class="cc.unitmesh.devti.actions.chat.ChatWithThisAction"
                    description="Ask AI chat with this code">
            </action>

            <action id="cc.unitmesh.devti.actions.chat.GenerateApiTestAction"
                    class="cc.unitmesh.devti.actions.chat.GenerateApiTestAction"
                    description="Ask AI generate test data">

                <add-to-group group-id="GenerateGroup" anchor="last"/>
            </action>

            <add-to-group group-id="EditorPopupMenu" anchor="first"/>
        </group>

        <action id="cc.unitmesh.devti.QuickAssistant"
                class="cc.unitmesh.devti.actions.quick.QuickAssistantAction"
                description="You can custom any assistant as you want!"
                icon="cc.unitmesh.devti.AutoDevIcons.AI_COPILOT"
        >
            <keyboard-shortcut keymap="$default" first-keystroke="control BACK_SLASH"/>

            <add-to-group group-id="ShowIntentionsGroup" relative-to-action="ShowIntentionActions" anchor="after"/>
        </action>

        <action id="cc.unitmesh.devti.actions.console.FixThisAction"
                class="cc.unitmesh.devti.actions.console.FixThisAction"
                description="Ask AI fix this code"
                icon="cc.unitmesh.devti.AutoDevIcons.AI_COPILOT">
            <add-to-group group-id="ConsoleEditorPopupMenu" anchor="first"/>
        </action>

        <action id="cc.unitmesh.devti.actions.chat.CodeCompleteChatAction"
                class="cc.unitmesh.devti.actions.chat.CodeCompleteChatAction"
                description="Ask AI about this code">

            <add-to-group group-id="EditorPopupMenu" anchor="first"/>
        </action>
        <group id="AutoDev.ToolWindow.Snippet.DependenciesToolbar">
            <action id="AutoDev.ToolWindow.Snippet.DependenciesLabelAction"
                    class="cc.unitmesh.devti.gui.snippet.AutoDevDependenciesLabelAction"/>
        </group>

        <group id="AutoDev.ToolWindow.Snippet.Toolbar">
            <action id="AutoDev.ToolWindow.Snippet.LanguageLabelAction"
                    class="cc.unitmesh.devti.gui.snippet.AutoDevLanguageLabelAction"/>

            <action id="AutoDev.ToolWindow.Snippet.CopyToClipboard"
                    icon="cc.unitmesh.devti.AutoDevIcons.COPY"
                    class="cc.unitmesh.devti.gui.snippet.AutoDevCopyToClipboardAction"/>
            <action id="AutoDev.ToolWindow.Snippet.InsertCode"
                    icon="cc.unitmesh.devti.AutoDevIcons.INSERT"
                    class="cc.unitmesh.devti.gui.snippet.AutoDevInsertCodeAction"/>
            <action id="AutoDev.ToolWindow.Snippet.RunDevIns"
                    icon="cc.unitmesh.devti.AutoDevIcons.RUN"
                    class="cc.unitmesh.devti.gui.snippet.AutoDevRunAction"/>
            <action id="AutoDev.ToolWindow.Snippet.SaveFile"
                    icon="cc.unitmesh.devti.AutoDevIcons.SAVE_FILE"
                    class="cc.unitmesh.devti.gui.snippet.AutoDevSaveFileAction"/>
        </group>

        <group id="AutoDev.ToolWindow.Chat.TitleActions">
            <action id="AutoDev.ToolWindow.NewChatAction" class="cc.unitmesh.devti.gui.toolbar.NewChatAction"/>
        </group>

        <action id="AutoDev.NewPromptTemplate" class="cc.unitmesh.devti.actions.template.NewPromptTemplateAction"
                icon="cc.unitmesh.devti.AutoDevIcons.AI_COPILOT">
            <add-to-group group-id="NewGroup" anchor="before" relative-to-action="NewFromTemplate"/>
        </action>

        <group id="AutoDev.NewActions"
               text="AutoDev CI/CD Actions"
               popup="true" icon="cc.unitmesh.devti.AutoDevIcons.AI_COPILOT">

            <separator/>
            <action id="GeniusDockerfile" class="cc.unitmesh.devti.actions.GenerateDockerfileAction"/>
            <action id="GeniusGitHubActions" class="cc.unitmesh.devti.actions.GenerateGitHubActionsAction"/>

            <add-to-group group-id="NewGroup" anchor="last"/>
        </group>

        <action id="AutoDev.BatchTest"
                icon="cc.unitmesh.devti.AutoDevIcons.AI_COPILOT"
                class="cc.unitmesh.devti.actions.chat.AutoTestInMenuAction">

            <add-to-group group-id="ProjectViewPopupMenu" anchor="after"
                          relative-to-action="ProjectViewPopupMenuRefactoringGroup"/>
            <add-to-group group-id="RefactoringMenu" anchor="last"/>
            <add-to-group group-id="EditorTabPopupMenu" anchor="last"/>
        </action>

        <action id="cc.unitmesh.devti.EditSettings"
                class="cc.unitmesh.devti.actions.EditSettingsAction"
                description="Edit autoDev settings"
                icon="cc.unitmesh.devti.AutoDevIcons.AI_COPILOT">
        </action>

        <action id="cc.unitmesh.devti.DomainDictGenerate"
                class="cc.unitmesh.devti.indexer.DomainDictGenerateAction"
                description="Generate domains.csv"
                icon="cc.unitmesh.devti.AutoDevIcons.AI_COPILOT">

            <add-to-group group-id="ProjectViewToolbar" anchor="last"/>
        </action>

        <group id="autodev.statusBarPopup">
            <reference id="cc.unitmesh.devti.QuickAssistant"/>
            <reference id="cc.unitmesh.devti.EditSettings"/>
        </group>

        <group id="AutoDevPlanner.ToolWindow.TitleActions">
            <action id="ReviewPlan" icon="cc.unitmesh.devti.AutoDevIcons.REVIEWER"
                    class="cc.unitmesh.devti.observer.plan.PlanReviewAction"/>
            <action id="EditPlan" icon="cc.unitmesh.devti.AutoDevIcons.EDIT_TASK"
                    class="cc.unitmesh.devti.observer.plan.EditPlanAction"/>
            <action id="CreateIssue" icon="cc.unitmesh.devti.AutoDevIcons.INPUT"
                    class="cc.unitmesh.devti.observer.plan.CreateIssueAction"/>
            <separator/>
        </group>

        <!-- MCP Marketplace Actions -->
        <action id="cc.unitmesh.devti.mcp.marketplace.McpMarketplaceAction"
                class="cc.unitmesh.devti.gui.toolbar.McpMarketplaceAction"
                text="MCP Marketplace"
                description="Browse and install MCP packages"
                icon="AllIcons.Actions.Download">
            <add-to-group group-id="MainToolBar" anchor="last"/>
        </action>

        <action id="cc.unitmesh.devti.mcp.marketplace.McpMarketplaceQuickAction"
                class="cc.unitmesh.devti.gui.toolbar.McpMarketplaceQuickAction"
                text="Browse MCP Marketplace"
                description="Quick access to MCP marketplace"
                icon="AllIcons.General.Web">
        </action>

        <action id="cc.unitmesh.devti.mcp.marketplace.McpConfigMarketplaceAction"
                class="cc.unitmesh.devti.gui.toolbar.McpConfigMarketplaceAction"
                text="Browse Marketplace"
                description="Browse MCP marketplace for more packages"
                icon="AllIcons.General.Web">
        </action>

        <!-- Memory Bank Actions -->
        <action id="cc.unitmesh.devti.memory.MemoryBankAction"
                class="cc.unitmesh.devti.gui.toolbar.MemoryBankAction"
                text="记忆银行"
                description="查看和管理记忆摘要"
                icon="AllIcons.General.Bookmark">
        </action>

        <action id="cc.unitmesh.devti.memory.TestSimpleMemoryBankAction"
                class="cc.unitmesh.devti.gui.toolbar.TestSimpleMemoryBankAction"
                text="测试记忆银行"
                description="测试简化的记忆银行 Markdown 渲染功能"
                icon="AllIcons.Actions.Preview">
        </action>

        <action id="cc.unitmesh.devti.memory.IntelligentMemoryAction"
                class="cc.unitmesh.devti.gui.toolbar.IntelligentMemoryAction"
                text="智能记忆处理"
                description="启动智能记忆处理和导出功能"
                icon="AllIcons.Actions.Lightning">
        </action>

        <action id="cc.unitmesh.devti.memory.SketchMemoryIntegrationAction"
                class="cc.unitmesh.devti.gui.toolbar.SketchMemoryIntegrationAction"
                text="SketchWindow记忆集成"
                description="为SketchWindow集成智能记忆功能"
                icon="AllIcons.Actions.IntentionBulb">
        </action>

        <action id="cc.unitmesh.devti.memory.LLMMemoryConfigAction"
                class="cc.unitmesh.devti.gui.toolbar.LLMMemoryConfigAction"
                text="LLM记忆配置"
                description="配置LLM记忆增强和存储功能"
                icon="AllIcons.General.Settings">
        </action>

        <action id="cc.unitmesh.devti.memory.LLMMemoryStatusAction"
                class="cc.unitmesh.devti.gui.toolbar.LLMMemoryStatusAction"
                text="LLM记忆状态"
                description="查看LLM记忆增强和处理状态"
                icon="AllIcons.General.InspectionsEye">
        </action>
    </actions>
</idea-plugin>
