package cc.unitmesh.devti.memory.intelligent

import cc.unitmesh.devti.llms.custom.Message
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.logger
import com.intellij.openapi.project.Project
import kotlinx.coroutines.*

/**
 * LLM记忆集成服务
 * 
 * 在LLM调用时自动集成记忆存储和检索功能
 */
@Service(Service.Level.PROJECT)
class LLMMemoryIntegration(private val project: Project) {
    
    companion object {
        fun getInstance(project: Project): LLMMemoryIntegration {
            return project.getService(LLMMemoryIntegration::class.java)
        }
    }
    
    private val logger = logger<LLMMemoryIntegration>()
    private val memoryFacade = IntelligentMemoryFacade.getInstance(project)
    private val contextService = ContextEnrichmentService.getInstance(project)
    private val integrationScope = CoroutineScope(Dispatchers.Default + SupervisorJob())
    
    // 配置选项
    private var enableMemoryStorage = true
    private var enableContextEnrichment = true
    private var autoSaveThreshold = 100 // 最小内容长度
    
    /**
     * 处理LLM请求前的记忆增强
     * 
     * @param userPrompt 用户输入
     * @param systemPrompt 系统提示
     * @param messages 历史消息
     * @return 增强后的提示和相关记忆
     */
    suspend fun enhancePromptWithMemory(
        userPrompt: String,
        systemPrompt: String,
        messages: List<Message>
    ): PromptEnhancementResult {
        logger.info("开始记忆增强处理，用户输入长度: ${userPrompt.length}")
        
        return try {
            val enhancementResult = if (enableContextEnrichment) {
                // 1. 检索相关记忆
                val relatedMemories = contextService.getRelatedMemoriesSummary(userPrompt, 5)
                
                // 2. 生成增强的上下文
                val enrichedContext = if (relatedMemories.isNotEmpty()) {
                    generateEnrichedPrompt(userPrompt, systemPrompt, relatedMemories)
                } else {
                    userPrompt
                }
                
                // 3. 构建结果
                PromptEnhancementResult(
                    originalPrompt = userPrompt,
                    enhancedPrompt = enrichedContext,
                    relatedMemories = relatedMemories,
                    systemPromptSuggestion = enhanceSystemPrompt(systemPrompt, relatedMemories),
                    memoryContext = buildMemoryContext(relatedMemories)
                )
            } else {
                // 不启用记忆增强时返回原始内容
                PromptEnhancementResult(
                    originalPrompt = userPrompt,
                    enhancedPrompt = userPrompt,
                    relatedMemories = emptyList(),
                    systemPromptSuggestion = systemPrompt,
                    memoryContext = ""
                )
            }
            
            logger.info("记忆增强完成，找到 ${enhancementResult.relatedMemories.size} 个相关记忆")
            enhancementResult
            
        } catch (e: Exception) {
            logger.error("记忆增强处理失败", e)
            // 失败时返回原始内容
            PromptEnhancementResult(
                originalPrompt = userPrompt,
                enhancedPrompt = userPrompt,
                relatedMemories = emptyList(),
                systemPromptSuggestion = systemPrompt,
                memoryContext = "",
                error = e.message
            )
        }
    }
    
    /**
     * 处理LLM响应后的记忆存储
     * 
     * @param userPrompt 用户输入
     * @param aiResponse AI响应
     * @param messages 完整对话历史
     * @param enhancementResult 之前的增强结果
     */
    fun processResponseForMemoryStorage(
        userPrompt: String,
        aiResponse: String,
        messages: List<Message>,
        enhancementResult: PromptEnhancementResult? = null
    ) {
        if (!enableMemoryStorage) return
        
        integrationScope.launch {
            try {
                logger.info("开始处理LLM响应的记忆存储")
                
                // 判断是否需要存储
                if (shouldStoreConversation(userPrompt, aiResponse)) {
                    val title = generateConversationTitle(userPrompt)
                    val content = buildConversationContent(userPrompt, aiResponse, enhancementResult)
                    val category = determineConversationCategory(userPrompt, aiResponse)
                    
                    // 存储到记忆系统
                    val result = memoryFacade.addMemoryWithContext(
                        title = title,
                        content = content,
                        source = "llm_conversation",
                        context = mapOf(
                            "user_prompt_length" to userPrompt.length.toString(),
                            "ai_response_length" to aiResponse.length.toString(),
                            "category" to category,
                            "has_related_memories" to (enhancementResult?.relatedMemories?.isNotEmpty() == true).toString(),
                            "timestamp" to System.currentTimeMillis().toString()
                        )
                    )
                    
                    if (result.success) {
                        logger.info("LLM对话已保存为记忆: $title (ID: ${result.memoryId})")
                    } else {
                        logger.warn("LLM对话保存失败: ${result.message}")
                    }
                }
                
            } catch (e: Exception) {
                logger.error("处理LLM响应记忆存储失败", e)
            }
        }
    }
    
    /**
     * 生成增强的提示
     */
    private fun generateEnrichedPrompt(
        userPrompt: String,
        systemPrompt: String,
        relatedMemories: List<MemoryAccessResult>
    ): String {
        return buildString {
            append("# 用户请求\n")
            append("$userPrompt\n\n")
            
            if (relatedMemories.isNotEmpty()) {
                append("# 相关记忆参考\n")
                append("以下是可能相关的历史记忆，请参考这些信息来提供更准确和一致的回答：\n\n")
                
                relatedMemories.forEachIndexed { index, memory ->
                    append("## 记忆 ${index + 1}: ${memory.title}\n")
                    append("**重要性**: ${"★".repeat(memory.importance)}\n")
                    append("**分类**: ${memory.category}\n")
                    
                    if (memory.tags.isNotEmpty()) {
                        append("**标签**: ${memory.tags.joinToString(", ")}\n")
                    }
                    
                    // 添加记忆内容摘要
                    val contentSummary = if (memory.content.length > 200) {
                        memory.content.take(200) + "..."
                    } else {
                        memory.content
                    }
                    append("**内容**: $contentSummary\n\n")
                }
                
                append("---\n\n")
                append("请基于以上相关记忆和当前用户请求，提供准确、一致且有用的回答。\n\n")
            }
        }
    }
    
    /**
     * 增强系统提示
     */
    private fun enhanceSystemPrompt(
        originalSystemPrompt: String,
        relatedMemories: List<MemoryAccessResult>
    ): String {
        if (relatedMemories.isEmpty()) return originalSystemPrompt
        
        return buildString {
            append(originalSystemPrompt)
            
            if (originalSystemPrompt.isNotEmpty()) {
                append("\n\n")
            }
            
            append("## 记忆上下文指导\n")
            append("你有访问相关历史记忆的能力。在回答时请：\n")
            append("1. 参考提供的相关记忆内容\n")
            append("2. 保持回答的一致性和连贯性\n")
            append("3. 如果记忆中有相关解决方案，请优先参考\n")
            append("4. 如果发现记忆内容过时或不准确，请指出并提供更新的信息\n")
            
            // 添加记忆分类信息
            val categories = relatedMemories.map { it.category }.distinct()
            if (categories.isNotEmpty()) {
                append("5. 当前相关记忆涉及的领域：${categories.joinToString(", ")}\n")
            }
        }
    }
    
    /**
     * 构建记忆上下文
     */
    private fun buildMemoryContext(relatedMemories: List<MemoryAccessResult>): String {
        if (relatedMemories.isEmpty()) return ""
        
        return buildString {
            append("相关记忆上下文：\n")
            relatedMemories.forEach { memory ->
                append("- ${memory.title} (${memory.category}, 重要性: ${memory.importance})\n")
            }
        }
    }
    
    /**
     * 判断是否应该存储对话
     */
    private fun shouldStoreConversation(userPrompt: String, aiResponse: String): Boolean {
        return when {
            // 内容长度检查
            userPrompt.length < autoSaveThreshold && aiResponse.length < autoSaveThreshold -> false
            
            // 包含重要关键词
            listOf("重要", "关键", "问题", "解决", "bug", "错误", "优化", "设计").any {
                userPrompt.contains(it, ignoreCase = true) || aiResponse.contains(it, ignoreCase = true)
            } -> true
            
            // 包含代码
            aiResponse.contains("```") || aiResponse.contains("代码") -> true
            
            // 用户明确要求保存
            userPrompt.contains("保存") || userPrompt.contains("记住") -> true
            
            // AI提供了详细解决方案
            aiResponse.contains("解决方案") || aiResponse.contains("步骤") || aiResponse.contains("方法") -> true
            
            // 长对话通常有价值
            userPrompt.length > 200 || aiResponse.length > 500 -> true
            
            else -> false
        }
    }
    
    /**
     * 生成对话标题
     */
    private fun generateConversationTitle(userPrompt: String): String {
        // 提取关键词作为标题
        val keywords = extractKeywords(userPrompt)
        return if (keywords.isNotEmpty()) {
            "LLM对话 - ${keywords.take(3).joinToString("、")}"
        } else {
            "LLM对话 - ${userPrompt.take(30)}..."
        }
    }
    
    /**
     * 构建对话内容
     */
    private fun buildConversationContent(
        userPrompt: String,
        aiResponse: String,
        enhancementResult: PromptEnhancementResult?
    ): String {
        return buildString {
            append("# LLM对话记录\n\n")
            append("**时间**: ${java.time.LocalDateTime.now()}\n")
            append("**来源**: LLM Provider Adapter\n\n")
            
            // 如果使用了记忆增强，记录相关信息
            if (enhancementResult != null && enhancementResult.relatedMemories.isNotEmpty()) {
                append("## 📚 使用的相关记忆\n")
                enhancementResult.relatedMemories.forEach { memory ->
                    append("- **${memory.title}** (${memory.category}, 重要性: ${memory.importance})\n")
                }
                append("\n")
            }
            
            append("## 👤 用户问题\n")
            append("$userPrompt\n\n")
            
            append("## 🤖 AI回答\n")
            append("$aiResponse\n\n")
            
            // 添加元数据
            append("---\n")
            append("*自动保存的LLM对话 | 用户输入: ${userPrompt.length}字符 | AI回答: ${aiResponse.length}字符*")
        }
    }
    
    /**
     * 确定对话分类
     */
    private fun determineConversationCategory(userPrompt: String, aiResponse: String): String {
        val text = "$userPrompt $aiResponse"
        return when {
            text.contains("代码") || text.contains("```") || text.contains("function") || text.contains("class") -> "code"
            text.contains("设计") || text.contains("架构") || text.contains("模式") -> "design"
            text.contains("bug") || text.contains("错误") || text.contains("问题") || text.contains("异常") -> "issue"
            text.contains("优化") || text.contains("性能") || text.contains("改进") -> "optimization"
            text.contains("学习") || text.contains("教程") || text.contains("如何") -> "learning"
            text.contains("配置") || text.contains("设置") || text.contains("安装") -> "configuration"
            else -> "discussion"
        }
    }
    
    /**
     * 提取关键词
     */
    private fun extractKeywords(text: String): List<String> {
        val commonWords = setOf("如何", "什么", "为什么", "怎么", "能否", "可以", "是否", "的", "了", "在", "和", "与", "或", "但是", "然而")
        return text.split(Regex("\\s+|[，。！？；：]"))
            .map { it.trim() }
            .filter { it.length > 1 && !commonWords.contains(it) }
            .take(5)
    }
    
    /**
     * 配置方法
     */
    fun setMemoryStorageEnabled(enabled: Boolean) {
        enableMemoryStorage = enabled
        logger.info("记忆存储功能${if (enabled) "已启用" else "已禁用"}")
    }
    
    fun setContextEnrichmentEnabled(enabled: Boolean) {
        enableContextEnrichment = enabled
        logger.info("上下文增强功能${if (enabled) "已启用" else "已禁用"}")
    }
    
    fun setAutoSaveThreshold(threshold: Int) {
        autoSaveThreshold = threshold
        logger.info("自动保存阈值设置为: $threshold")
    }
    
    /**
     * 获取配置状态
     */
    fun getConfiguration(): LLMMemoryConfig {
        return LLMMemoryConfig(
            memoryStorageEnabled = enableMemoryStorage,
            contextEnrichmentEnabled = enableContextEnrichment,
            autoSaveThreshold = autoSaveThreshold
        )
    }
}

/**
 * 提示增强结果
 */
data class PromptEnhancementResult(
    val originalPrompt: String,
    val enhancedPrompt: String,
    val relatedMemories: List<MemoryAccessResult>,
    val systemPromptSuggestion: String,
    val memoryContext: String,
    val error: String? = null
)

/**
 * LLM记忆配置
 */
data class LLMMemoryConfig(
    val memoryStorageEnabled: Boolean,
    val contextEnrichmentEnabled: Boolean,
    val autoSaveThreshold: Int
)
