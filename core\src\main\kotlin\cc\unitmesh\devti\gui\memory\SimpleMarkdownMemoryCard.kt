package cc.unitmesh.devti.gui.memory

import cc.unitmesh.devti.memory.MemoryBankService
import cc.unitmesh.devti.memory.MemorySummary
import com.intellij.icons.AllIcons
import com.intellij.openapi.project.Project
import com.intellij.ui.JBColor
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBScrollPane
import com.intellij.util.ui.JBUI
import com.intellij.util.ui.UIUtil
import org.commonmark.parser.Parser
import org.commonmark.renderer.html.HtmlRenderer
import java.awt.*
import javax.swing.*
import javax.swing.text.html.HTMLEditorKit

/**
 * 简化的 Markdown 记忆卡片组件 - 使用 HTML 渲染 Markdown
 */
class SimpleMarkdownMemoryCard(
    private val project: Project,
    private val memory: MemorySummary,
    private val onUpdate: (MemorySummary) -> Unit
) : JPanel(BorderLayout()) {
    
    private val memoryBankService = MemoryBankService.getInstance(project)
    private var isExpanded = memory.isExpanded
    
    init {
        setupUI()
    }
    
    private fun setupUI() {
        border = JBUI.Borders.compound(
            JBUI.Borders.customLine(JBColor.border()),
            JBUI.Borders.empty(12)
        )
        background = UIUtil.getPanelBackground()
        
        // 头部面板（标题和控制按钮）
        val headerPanel = createHeaderPanel()
        add(headerPanel, BorderLayout.NORTH)
        
        // 内容面板（可折叠的 Markdown 渲染）
        if (isExpanded) {
            val contentPanel = createContentPanel()
            add(contentPanel, BorderLayout.CENTER)
        }
    }
    
    private fun createHeaderPanel(): JComponent {
        val panel = JPanel(BorderLayout())
        panel.isOpaque = false
        
        // 左侧：展开/折叠图标 + 标题
        val leftPanel = JPanel(FlowLayout(FlowLayout.LEFT, 0, 0))
        leftPanel.isOpaque = false
        
        val expandIcon = if (isExpanded) AllIcons.General.ArrowDown else AllIcons.General.ArrowRight
        val expandButton = JButton(expandIcon).apply {
            isOpaque = false
            isBorderPainted = false
            isContentAreaFilled = false
            toolTipText = if (isExpanded) "折叠" else "展开"
            addActionListener { toggleExpanded() }
        }
        
        val titleLabel = JBLabel(memory.title).apply {
            font = JBUI.Fonts.label(14f).asBold()
            foreground = UIUtil.getLabelForeground()
        }
        
        leftPanel.add(expandButton)
        leftPanel.add(Box.createHorizontalStrut(8))
        leftPanel.add(titleLabel)
        
        // 中间：标签和分类信息
        val middlePanel = JPanel(FlowLayout(FlowLayout.LEFT, 8, 0))
        middlePanel.isOpaque = false
        
        // 分类标签
        val categoryLabel = JBLabel(memory.category).apply {
            font = JBUI.Fonts.smallFont()
            foreground = Color.WHITE
            isOpaque = true
            background = getCategoryColor(memory.category)
            border = JBUI.Borders.empty(3, 8)
        }
        middlePanel.add(categoryLabel)
        
        // 重要性星级显示
        val starsPanel = JPanel(FlowLayout(FlowLayout.LEFT, 1, 0))
        starsPanel.isOpaque = false
        repeat(memory.importance) {
            starsPanel.add(JBLabel("★").apply { 
                foreground = Color.ORANGE
                font = JBUI.Fonts.smallFont()
            })
        }
        repeat(5 - memory.importance) {
            starsPanel.add(JBLabel("☆").apply { 
                foreground = UIUtil.getInactiveTextColor()
                font = JBUI.Fonts.smallFont()
            })
        }
        middlePanel.add(starsPanel)
        
        // Markdown 标识
        val markdownLabel = JBLabel("MD").apply {
            font = JBUI.Fonts.miniFont()
            foreground = Color.WHITE
            isOpaque = true
            background = JBColor.BLUE
            border = JBUI.Borders.empty(2, 4)
            toolTipText = "Markdown 格式"
        }
        middlePanel.add(markdownLabel)
        
        // 右侧：操作按钮
        val rightPanel = JPanel(FlowLayout(FlowLayout.RIGHT, 3, 0))
        rightPanel.isOpaque = false
        
        val editButton = JButton(AllIcons.Actions.Edit).apply {
            isOpaque = false
            isBorderPainted = false
            isContentAreaFilled = false
            toolTipText = "编辑记忆"
            addActionListener { editMemory() }
        }
        
        val copyButton = JButton(AllIcons.Actions.Copy).apply {
            isOpaque = false
            isBorderPainted = false
            isContentAreaFilled = false
            toolTipText = "复制 Markdown 内容"
            addActionListener { copyMarkdownContent() }
        }
        
        val deleteButton = JButton(AllIcons.Actions.Cancel).apply {
            isOpaque = false
            isBorderPainted = false
            isContentAreaFilled = false
            toolTipText = "删除记忆"
            addActionListener { deleteMemory() }
        }
        
        rightPanel.add(copyButton)
        rightPanel.add(editButton)
        rightPanel.add(deleteButton)
        
        panel.add(leftPanel, BorderLayout.WEST)
        panel.add(middlePanel, BorderLayout.CENTER)
        panel.add(rightPanel, BorderLayout.EAST)
        
        return panel
    }
    
    private fun createContentPanel(): JComponent {
        val panel = JPanel(BorderLayout())
        panel.isOpaque = false
        panel.border = JBUI.Borders.emptyTop(12)
        
        // 检查内容是否为空
        if (memory.content.isBlank()) {
            val emptyLabel = JBLabel("内容为空").apply {
                horizontalAlignment = SwingConstants.CENTER
                foreground = UIUtil.getInactiveTextColor()
            }
            panel.add(emptyLabel, BorderLayout.CENTER)
            return panel
        }
        
        try {
            // 使用 CommonMark 解析 Markdown 并转换为 HTML
            val parser = Parser.builder().build()
            val document = parser.parse(memory.content)
            val renderer = HtmlRenderer.builder().build()
            val html = renderer.render(document)
            
            // 创建 HTML 显示组件
            val htmlPane = JEditorPane().apply {
                contentType = "text/html"
                editorKit = HTMLEditorKit()
                text = """
                    <html>
                    <head>
                        <style>
                            body { 
                                font-family: ${UIUtil.getLabelFont().family}; 
                                font-size: ${UIUtil.getLabelFont().size}px;
                                color: ${colorToHex(UIUtil.getLabelForeground())};
                                background-color: ${colorToHex(UIUtil.getPanelBackground())};
                                margin: 8px;
                            }
                            pre { 
                                background-color: ${colorToHex(UIUtil.getTextFieldBackground())};
                                border: 1px solid ${colorToHex(UIUtil.getBoundsColor())};
                                padding: 8px;
                                border-radius: 4px;
                                overflow-x: auto;
                            }
                            code {
                                background-color: ${colorToHex(UIUtil.getTextFieldBackground())};
                                padding: 2px 4px;
                                border-radius: 3px;
                                font-family: monospace;
                            }
                            blockquote {
                                border-left: 4px solid ${colorToHex(JBColor.BLUE)};
                                margin: 8px 0;
                                padding-left: 12px;
                                color: ${colorToHex(UIUtil.getContextHelpForeground())};
                            }
                            table {
                                border-collapse: collapse;
                                width: 100%;
                            }
                            th, td {
                                border: 1px solid ${colorToHex(UIUtil.getBoundsColor())};
                                padding: 8px;
                                text-align: left;
                            }
                            th {
                                background-color: ${colorToHex(UIUtil.getTextFieldBackground())};
                            }
                        </style>
                    </head>
                    <body>
                        $html
                    </body>
                    </html>
                """.trimIndent()
                isEditable = false
                isOpaque = false
            }
            
            // 滚动面板
            val scrollPane = JBScrollPane(htmlPane).apply {
                border = JBUI.Borders.compound(
                    JBUI.Borders.customLine(JBColor.border()),
                    JBUI.Borders.empty(8)
                )
                verticalScrollBarPolicy = JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED
                horizontalScrollBarPolicy = JScrollPane.HORIZONTAL_SCROLLBAR_NEVER
                preferredSize = Dimension(600, 300)
                minimumSize = Dimension(400, 100)
            }
            
            panel.add(scrollPane, BorderLayout.CENTER)
            
        } catch (e: Exception) {
            // 如果 Markdown 解析失败，显示纯文本
            val fallbackArea = JTextArea(memory.content).apply {
                isEditable = false
                lineWrap = true
                wrapStyleWord = true
                font = JBUI.Fonts.label()
                background = UIUtil.getPanelBackground()
            }
            
            val scrollPane = JBScrollPane(fallbackArea).apply {
                border = JBUI.Borders.compound(
                    JBUI.Borders.customLine(JBColor.border()),
                    JBUI.Borders.empty(8)
                )
                preferredSize = Dimension(600, 200)
            }
            
            panel.add(scrollPane, BorderLayout.CENTER)
            
            // 添加错误提示
            val errorLabel = JBLabel("Markdown 解析失败，显示纯文本: ${e.message}").apply {
                font = JBUI.Fonts.smallFont()
                foreground = UIUtil.getErrorForeground()
                horizontalAlignment = SwingConstants.CENTER
            }
            panel.add(errorLabel, BorderLayout.NORTH)
        }
        
        // 底部信息面板
        val infoPanel = createInfoPanel()
        panel.add(infoPanel, BorderLayout.SOUTH)
        
        return panel
    }
    
    private fun createInfoPanel(): JComponent {
        val panel = JPanel(FlowLayout(FlowLayout.LEFT, 0, 0))
        panel.isOpaque = false
        panel.border = JBUI.Borders.emptyTop(8)
        
        // 标签显示
        if (memory.tags.isNotEmpty()) {
            memory.tags.forEach { tag ->
                val tagLabel = JBLabel("#$tag").apply {
                    font = JBUI.Fonts.smallFont()
                    foreground = JBColor.BLUE
                    border = JBUI.Borders.empty(2, 4)
                }
                panel.add(tagLabel)
                panel.add(Box.createHorizontalStrut(6))
            }
            
            panel.add(JBLabel("|").apply {
                foreground = UIUtil.getInactiveTextColor()
                font = JBUI.Fonts.smallFont()
            })
            panel.add(Box.createHorizontalStrut(6))
        }
        
        // 时间信息
        val timeLabel = JBLabel("创建: ${formatDateTime(memory.createdAt)}").apply {
            font = JBUI.Fonts.smallFont()
            foreground = UIUtil.getInactiveTextColor()
        }
        panel.add(timeLabel)
        
        if (memory.updatedAt != memory.createdAt) {
            panel.add(Box.createHorizontalStrut(10))
            val updateLabel = JBLabel("更新: ${formatDateTime(memory.updatedAt)}").apply {
                font = JBUI.Fonts.smallFont()
                foreground = UIUtil.getInactiveTextColor()
            }
            panel.add(updateLabel)
        }
        
        return panel
    }
    
    private fun getCategoryColor(category: String): Color {
        return when (category.lowercase()) {
            "code" -> JBColor.GREEN
            "design" -> JBColor.MAGENTA
            "discussion" -> JBColor.ORANGE
            "important" -> JBColor.RED
            "idea" -> JBColor.CYAN
            else -> UIUtil.getContextHelpForeground()
        }
    }
    
    private fun colorToHex(color: Color): String {
        return String.format("#%02x%02x%02x", color.red, color.green, color.blue)
    }
    
    private fun formatDateTime(dateTimeString: String): String {
        return try {
            dateTimeString.substring(0, 16).replace("T", " ")
        } catch (e: Exception) {
            dateTimeString
        }
    }
    
    private fun toggleExpanded() {
        isExpanded = !isExpanded
        
        // 移除现有内容
        removeAll()
        
        // 重新构建UI
        setupUI()
        
        // 刷新显示
        revalidate()
        repaint()
    }
    
    private fun editMemory() {
        val dialog = EditMemoryDialog(project, memory) { updatedMemory ->
            onUpdate(updatedMemory)
        }
        dialog.show()
    }
    
    private fun copyMarkdownContent() {
        val clipboard = Toolkit.getDefaultToolkit().systemClipboard
        val stringSelection = java.awt.datatransfer.StringSelection(memory.content)
        clipboard.setContents(stringSelection, null)
        
        JOptionPane.showMessageDialog(
            this,
            "Markdown 内容已复制到剪贴板",
            "复制成功",
            JOptionPane.INFORMATION_MESSAGE
        )
    }
    
    private fun deleteMemory() {
        val result = JOptionPane.showConfirmDialog(
            this,
            "确定要删除这条记忆摘要吗？\n\n标题: ${memory.title}",
            "确认删除",
            JOptionPane.YES_NO_OPTION,
            JOptionPane.WARNING_MESSAGE
        )
        
        if (result == JOptionPane.YES_OPTION) {
            memoryBankService.deleteMemory(memory.id)
            onUpdate(memory)
        }
    }
}
