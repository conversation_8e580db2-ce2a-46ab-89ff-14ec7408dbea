package cc.unitmesh.devti.sketch.ui.patch.inline

import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.EditorCustomElementRenderer
import com.intellij.openapi.editor.Inlay
import com.intellij.openapi.editor.colors.EditorColors
import com.intellij.openapi.editor.colors.EditorFontType
import com.intellij.openapi.editor.markup.HighlighterLayer
import com.intellij.openapi.editor.markup.HighlighterTargetArea
import com.intellij.openapi.editor.markup.RangeHighlighter
import com.intellij.openapi.editor.markup.TextAttributes
import com.intellij.ui.JBColor
import java.awt.Color
import java.awt.Font
import java.awt.Graphics
import java.awt.Graphics2D
import java.awt.Rectangle

/**
 * 差异高亮渲染器，负责在编辑器中渲染差异高亮效果
 */
class DiffHighlightRenderer(private val editor: Editor) {
    
    companion object {
        // 差异颜色定义
        private val ADDED_LINE_COLOR = JBColor(Color(0xE6FFE6), Color(0x2D4A2D))
        private val DELETED_LINE_COLOR = JBColor(Color(0xFFE6E6), Color(0x4A2D2D))
        private val MODIFIED_LINE_COLOR = JBColor(Color(0xFFF8E1), Color(0x4A4A2D))
        
        private val ADDED_CHAR_COLOR = JBColor(Color(0xB3FFB3), Color(0x1A3D1A))
        private val DELETED_CHAR_COLOR = JBColor(Color(0xFFB3B3), Color(0x3D1A1A))
        
        // 高亮层级
        private const val LINE_HIGHLIGHT_LAYER = HighlighterLayer.SELECTION - 1
        private const val CHAR_HIGHLIGHT_LAYER = HighlighterLayer.SELECTION
    }
    
    private val highlighters = mutableListOf<RangeHighlighter>()
    
    /**
     * 渲染行级差异
     */
    fun renderLineDiffs(lineDiffs: List<DiffCalculator.LineDiff>) {
        clearHighlights()
        
        var currentLineIndex = 0
        
        for (lineDiff in lineDiffs) {
            when (lineDiff) {
                is DiffCalculator.LineDiff.Added -> {
                    renderAddedLine(currentLineIndex, lineDiff.line)
                    currentLineIndex++
                }
                is DiffCalculator.LineDiff.Deleted -> {
                    renderDeletedLine(currentLineIndex, lineDiff.line)
                    // 删除行不增加行索引，因为它们不在新文本中
                }
                is DiffCalculator.LineDiff.Modified -> {
                    renderModifiedLine(currentLineIndex, lineDiff)
                    currentLineIndex++
                }
                is DiffCalculator.LineDiff.Unchanged -> {
                    currentLineIndex++
                }
            }
        }
    }
    
    /**
     * 渲染添加的行
     */
    private fun renderAddedLine(lineIndex: Int, line: String) {
        if (lineIndex >= editor.document.lineCount) return
        
        val startOffset = editor.document.getLineStartOffset(lineIndex)
        val endOffset = editor.document.getLineEndOffset(lineIndex)
        
        val attributes = TextAttributes().apply {
            backgroundColor = ADDED_LINE_COLOR
        }
        
        val highlighter = editor.markupModel.addRangeHighlighter(
            startOffset, endOffset,
            LINE_HIGHLIGHT_LAYER,
            attributes,
            HighlighterTargetArea.LINES_IN_RANGE
        )
        
        highlighters.add(highlighter)
    }
    
    /**
     * 渲染删除的行（作为inlay显示）
     */
    private fun renderDeletedLine(lineIndex: Int, line: String) {
        if (lineIndex >= editor.document.lineCount) return

        val offset = if (lineIndex == 0) {
            0
        } else {
            editor.document.getLineStartOffset(lineIndex)
        }

        // 创建删除行的inlay
        val inlayText = "- $line"
        val attributes = TextAttributes().apply {
            backgroundColor = DELETED_LINE_COLOR
            foregroundColor = JBColor.GRAY
            fontType = Font.ITALIC
        }

        // 使用block inlay来显示删除的行
        val renderer: EditorCustomElementRenderer = DeletedLineRenderer(inlayText, attributes)
        val inlay = editor.inlayModel.addBlockElement(
            offset,
            true,
            false,
            0,
            renderer
        )

        // 注意：inlay不是RangeHighlighter，需要单独管理
    }
    
    /**
     * 渲染修改的行
     */
    private fun renderModifiedLine(lineIndex: Int, modifiedDiff: DiffCalculator.LineDiff.Modified) {
        if (lineIndex >= editor.document.lineCount) return
        
        val startOffset = editor.document.getLineStartOffset(lineIndex)
        val endOffset = editor.document.getLineEndOffset(lineIndex)
        
        // 先添加行级背景
        val lineAttributes = TextAttributes().apply {
            backgroundColor = MODIFIED_LINE_COLOR
        }
        
        val lineHighlighter = editor.markupModel.addRangeHighlighter(
            startOffset, endOffset,
            LINE_HIGHLIGHT_LAYER,
            lineAttributes,
            HighlighterTargetArea.LINES_IN_RANGE
        )
        
        highlighters.add(lineHighlighter)
        
        // 然后添加字符级高亮
        renderCharDiffs(startOffset, modifiedDiff.charDiffs)
    }
    
    /**
     * 渲染字符级差异
     */
    private fun renderCharDiffs(lineStartOffset: Int, charDiffs: List<DiffCalculator.CharDiff>) {
        var currentOffset = lineStartOffset
        
        for (charDiff in charDiffs) {
            when (charDiff) {
                is DiffCalculator.CharDiff.Added -> {
                    val attributes = TextAttributes().apply {
                        backgroundColor = ADDED_CHAR_COLOR
                        fontType = Font.BOLD
                    }
                    
                    val highlighter = editor.markupModel.addRangeHighlighter(
                        currentOffset,
                        currentOffset + charDiff.text.length,
                        CHAR_HIGHLIGHT_LAYER,
                        attributes,
                        HighlighterTargetArea.EXACT_RANGE
                    )
                    
                    highlighters.add(highlighter)
                    currentOffset += charDiff.text.length
                }
                is DiffCalculator.CharDiff.Deleted -> {
                    // 删除的字符通过特殊标记显示
                    val attributes = TextAttributes().apply {
                        backgroundColor = DELETED_CHAR_COLOR
                        fontType = Font.BOLD or Font.ITALIC
                    }

                    // 在当前位置插入删除标记
                    val deletedText = "[${charDiff.text}]"
                    // 注意：这里需要特殊处理，因为删除的字符不在新文本中
                }
                is DiffCalculator.CharDiff.Unchanged -> {
                    currentOffset += charDiff.text.length
                }
            }
        }
    }
    
    /**
     * 清除所有高亮
     */
    fun clearHighlights() {
        highlighters.forEach { highlighter ->
            editor.markupModel.removeHighlighter(highlighter)
        }
        highlighters.clear()
        
        // 清除所有inlay
        editor.inlayModel.getBlockElementsInRange(0, editor.document.textLength).forEach { inlay ->
            if (inlay.renderer is DeletedLineRenderer) {
                inlay.dispose()
            }
        }
    }
    
    /**
     * 获取差异统计信息
     */
    fun getDiffStats(lineDiffs: List<DiffCalculator.LineDiff>): DiffStats {
        var addedLines = 0
        var deletedLines = 0
        var modifiedLines = 0
        
        for (lineDiff in lineDiffs) {
            when (lineDiff) {
                is DiffCalculator.LineDiff.Added -> addedLines++
                is DiffCalculator.LineDiff.Deleted -> deletedLines++
                is DiffCalculator.LineDiff.Modified -> modifiedLines++
                is DiffCalculator.LineDiff.Unchanged -> { /* 不计算 */ }
            }
        }
        
        return DiffStats(addedLines, deletedLines, modifiedLines)
    }
    
    /**
     * 差异统计信息
     */
    data class DiffStats(
        val addedLines: Int,
        val deletedLines: Int,
        val modifiedLines: Int
    ) {
        val totalChanges: Int get() = addedLines + deletedLines + modifiedLines
    }
    
    /**
     * 释放资源
     */
    fun dispose() {
        clearHighlights()
    }
}

/**
 * 删除行渲染器，用于显示删除的行作为inlay
 */
private class DeletedLineRenderer(
    private val text: String,
    private val attributes: TextAttributes
) : EditorCustomElementRenderer {

    override fun calcWidthInPixels(inlay: Inlay<*>): Int {
        val editor = inlay.editor
        val fontMetrics = editor.contentComponent.getFontMetrics(editor.colorsScheme.getFont(EditorFontType.PLAIN))
        return fontMetrics.stringWidth(text)
    }

    override fun calcHeightInPixels(inlay: Inlay<*>): Int {
        val editor = inlay.editor
        return editor.lineHeight
    }

    override fun paint(
        inlay: Inlay<*>,
        g: Graphics,
        targetRegion: Rectangle,
        textAttributes: TextAttributes
    ) {
        val editor = inlay.editor
        val g2d = g as Graphics2D

        // 设置背景色
        g2d.color = attributes.backgroundColor ?: DELETED_LINE_COLOR
        g2d.fillRect(targetRegion.x, targetRegion.y, targetRegion.width, targetRegion.height)

        // 设置文字颜色和字体
        g2d.color = attributes.foregroundColor ?: JBColor.GRAY
        g2d.font = editor.colorsScheme.getFont(EditorFontType.PLAIN)

        // 绘制文字
        val fontMetrics = g2d.fontMetrics
        val textY = targetRegion.y + fontMetrics.ascent
        g2d.drawString(text, targetRegion.x + 4, textY)
    }
}
