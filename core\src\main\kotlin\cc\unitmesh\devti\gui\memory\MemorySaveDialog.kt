package cc.unitmesh.devti.gui.memory

import cc.unitmesh.devti.memory.intelligent.MemorySummaryGenerator
import cc.unitmesh.devti.memory.intelligent.GeneratedSummary
import com.intellij.icons.AllIcons
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.JBColor
import com.intellij.ui.components.*
import com.intellij.util.ui.JBUI
import com.intellij.util.ui.UIUtil
import kotlinx.coroutines.*
import java.awt.*
import javax.swing.*

/**
 * 记忆保存对话框 - 支持AI摘要预览
 */
class MemorySaveDialog(
    private val project: Project,
    private val initialContent: String,
    private val onSave: (title: String, content: String, category: String) -> Unit
) : DialogWrapper(project) {
    
    private val summaryGenerator = MemorySummaryGenerator.getInstance(project)
    private val dialogScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    // UI组件
    private lateinit var titleField: JBTextField
    private lateinit var categoryCombo: JComboBox<String>
    private lateinit var contentArea: JTextArea
    private lateinit var summaryArea: JTextArea
    private lateinit var generateSummaryButton: JButton
    private lateinit var summaryPanel: JPanel
    private lateinit var previewButton: JButton
    private lateinit var editButton: JButton
    
    private var isShowingPreview = false
    private var generatedSummaryText = ""
    
    init {
        title = "保存为记忆"
        setSize(800, 600)
        init()
    }
    
    override fun createCenterPanel(): JComponent {
        val mainPanel = JPanel(BorderLayout())
        mainPanel.preferredSize = Dimension(780, 550)
        
        // 顶部信息面板
        val infoPanel = createInfoPanel()
        mainPanel.add(infoPanel, BorderLayout.NORTH)
        
        // 中间内容面板
        val contentPanel = createContentPanel()
        mainPanel.add(contentPanel, BorderLayout.CENTER)
        
        // 底部摘要面板
        val bottomPanel = createSummaryPanel()
        mainPanel.add(bottomPanel, BorderLayout.SOUTH)
        
        return mainPanel
    }
    
    private fun createInfoPanel(): JComponent {
        val panel = JPanel(BorderLayout())
        panel.border = JBUI.Borders.empty(0, 0, 10, 0)
        
        val infoLabel = JBLabel("💡 将对话内容保存为记忆，支持AI自动生成摘要").apply {
            font = JBUI.Fonts.label(12f)
            foreground = UIUtil.getContextHelpForeground()
        }
        
        panel.add(infoLabel, BorderLayout.WEST)
        
        return panel
    }
    
    private fun createContentPanel(): JComponent {
        val panel = JPanel(BorderLayout())
        
        // 左侧：基本信息
        val leftPanel = createBasicInfoPanel()
        
        // 右侧：内容编辑
        val rightPanel = createContentEditPanel()
        
        val splitPane = JSplitPane(JSplitPane.HORIZONTAL_SPLIT, leftPanel, rightPanel)
        splitPane.dividerLocation = 250
        splitPane.resizeWeight = 0.3
        
        panel.add(splitPane, BorderLayout.CENTER)
        
        return panel
    }
    
    private fun createBasicInfoPanel(): JComponent {
        val panel = JPanel(BorderLayout())
        panel.border = JBUI.Borders.empty(0, 0, 0, 10)
        
        val formPanel = JPanel(GridBagLayout())
        val gbc = GridBagConstraints()
        
        // 标题
        gbc.gridx = 0; gbc.gridy = 0
        gbc.anchor = GridBagConstraints.WEST
        gbc.insets = JBUI.insets(5)
        formPanel.add(JBLabel("标题:"), gbc)
        
        gbc.gridx = 0; gbc.gridy = 1
        gbc.fill = GridBagConstraints.HORIZONTAL
        gbc.weightx = 1.0
        titleField = JBTextField(extractTitleFromContent(initialContent))
        formPanel.add(titleField, gbc)
        
        // 分类
        gbc.gridx = 0; gbc.gridy = 2
        gbc.fill = GridBagConstraints.NONE
        gbc.weightx = 0.0
        formPanel.add(JBLabel("分类:"), gbc)
        
        gbc.gridx = 0; gbc.gridy = 3
        gbc.fill = GridBagConstraints.HORIZONTAL
        gbc.weightx = 1.0
        categoryCombo = JComboBox(arrayOf(
            "discussion", "code", "idea", "issue", "design", "general"
        ))
        categoryCombo.selectedItem = "discussion"
        formPanel.add(categoryCombo, gbc)
        
        // AI摘要生成按钮
        gbc.gridx = 0; gbc.gridy = 4
        gbc.fill = GridBagConstraints.HORIZONTAL
        gbc.insets = JBUI.insets(15, 5, 5, 5)
        generateSummaryButton = JButton("🤖 生成AI摘要").apply {
            addActionListener { generateAISummary() }
        }
        formPanel.add(generateSummaryButton, gbc)
        
        // 预览/编辑切换按钮
        gbc.gridx = 0; gbc.gridy = 5
        gbc.insets = JBUI.insets(5)
        previewButton = JButton("📋 预览摘要").apply {
            addActionListener { togglePreview() }
            isEnabled = false
        }
        formPanel.add(previewButton, gbc)
        
        panel.add(formPanel, BorderLayout.NORTH)
        
        return panel
    }
    
    private fun createContentEditPanel(): JComponent {
        val panel = JPanel(BorderLayout())
        
        // 标签
        val labelPanel = JPanel(FlowLayout(FlowLayout.LEFT))
        labelPanel.add(JBLabel("内容 (支持Markdown):"))
        
        editButton = JButton("✏️ 编辑").apply {
            addActionListener { switchToEditMode() }
            isVisible = false
        }
        labelPanel.add(editButton)
        
        panel.add(labelPanel, BorderLayout.NORTH)
        
        // 内容区域
        contentArea = JTextArea(initialContent).apply {
            lineWrap = true
            wrapStyleWord = true
            font = JBUI.Fonts.label()
        }
        
        val scrollPane = JBScrollPane(contentArea).apply {
            preferredSize = Dimension(400, 300)
            verticalScrollBarPolicy = JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED
            horizontalScrollBarPolicy = JScrollPane.HORIZONTAL_SCROLLBAR_NEVER
        }
        
        panel.add(scrollPane, BorderLayout.CENTER)
        
        return panel
    }
    
    private fun createSummaryPanel(): JComponent {
        summaryPanel = JPanel(BorderLayout())
        summaryPanel.border = JBUI.Borders.compound(
            JBUI.Borders.customLine(JBColor.border()),
            JBUI.Borders.empty(10)
        )
        summaryPanel.isVisible = false
        
        val titleLabel = JBLabel("🤖 AI生成摘要").apply {
            font = JBUI.Fonts.label(12f).asBold()
        }
        summaryPanel.add(titleLabel, BorderLayout.NORTH)
        
        summaryArea = JTextArea().apply {
            isEditable = false
            lineWrap = true
            wrapStyleWord = true
            font = JBUI.Fonts.label()
            background = UIUtil.getTextFieldBackground()
        }
        
        val summaryScrollPane = JBScrollPane(summaryArea).apply {
            preferredSize = Dimension(0, 120)
            verticalScrollBarPolicy = JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED
            horizontalScrollBarPolicy = JScrollPane.HORIZONTAL_SCROLLBAR_NEVER
        }
        
        summaryPanel.add(summaryScrollPane, BorderLayout.CENTER)
        
        return summaryPanel
    }
    
    private fun generateAISummary() {
        generateSummaryButton.isEnabled = false
        generateSummaryButton.text = "🔄 生成中..."
        
        dialogScope.launch {
            try {
                val result = summaryGenerator.generateSummary(
                    title = titleField.text.ifBlank { "未命名记忆" },
                    content = contentArea.text,
                    context = mapOf(
                        "source" to "dialog",
                        "category" to (categoryCombo.selectedItem as String)
                    )
                )
                
                SwingUtilities.invokeLater {
                    if (result.success && result.summary != null) {
                        val summary = result.summary
                        
                        // 更新UI建议
                        if (titleField.text.isBlank()) {
                            titleField.text = summary.summary.take(30) + "..."
                        }
                        
                        categoryCombo.selectedItem = summary.suggestedCategory
                        
                        // 显示摘要
                        generatedSummaryText = formatSummaryForDisplay(summary)
                        summaryArea.text = generatedSummaryText
                        summaryPanel.isVisible = true
                        previewButton.isEnabled = true
                        
                        // 重新布局
                        contentPane.revalidate()
                        contentPane.repaint()
                        
                    } else {
                        showError("AI摘要生成失败: ${result.error}")
                    }
                    
                    generateSummaryButton.isEnabled = true
                    generateSummaryButton.text = "🤖 生成AI摘要"
                }
                
            } catch (e: Exception) {
                SwingUtilities.invokeLater {
                    showError("生成摘要时发生错误: ${e.message}")
                    generateSummaryButton.isEnabled = true
                    generateSummaryButton.text = "🤖 生成AI摘要"
                }
            }
        }
    }
    
    private fun formatSummaryForDisplay(summary: GeneratedSummary): String {
        return buildString {
            append("📝 摘要: ${summary.summary}\n\n")
            
            if (summary.keywords.isNotEmpty()) {
                append("🔑 关键词: ${summary.keywords.joinToString(", ")}\n\n")
            }
            
            append("⭐ 重要性: ${"★".repeat(summary.importance)} (${summary.importance}/5)\n")
            if (summary.importanceReason.isNotEmpty()) {
                append("理由: ${summary.importanceReason}\n\n")
            }
            
            if (summary.relatedConcepts.isNotEmpty()) {
                append("🔗 相关概念: ${summary.relatedConcepts.joinToString(", ")}\n\n")
            }
            
            if (summary.suggestedTags.isNotEmpty()) {
                append("🏷️ 建议标签: ${summary.suggestedTags.joinToString(", ")}\n")
            }
        }
    }
    
    private fun togglePreview() {
        if (isShowingPreview) {
            switchToEditMode()
        } else {
            switchToPreviewMode()
        }
    }
    
    private fun switchToPreviewMode() {
        isShowingPreview = true
        contentArea.isEditable = false
        contentArea.background = UIUtil.getTextFieldBackground()
        previewButton.text = "✏️ 编辑内容"
        editButton.isVisible = true
    }
    
    private fun switchToEditMode() {
        isShowingPreview = false
        contentArea.isEditable = true
        contentArea.background = UIUtil.getTextFieldBackground()
        previewButton.text = "📋 预览摘要"
        editButton.isVisible = false
    }
    
    private fun extractTitleFromContent(content: String): String {
        // 尝试从内容中提取标题
        val lines = content.split("\n").filter { it.isNotBlank() }
        
        // 查找Markdown标题
        val titleLine = lines.find { it.startsWith("#") }
        if (titleLine != null) {
            return titleLine.removePrefix("#").trim()
        }
        
        // 查找"用户:"后的内容作为标题
        val userLine = lines.find { it.startsWith("**用户**:") || it.startsWith("用户:") }
        if (userLine != null) {
            val title = userLine.substringAfter(":").trim()
            if (title.length > 5) {
                return title.take(50) + if (title.length > 50) "..." else ""
            }
        }
        
        // 使用第一行非空内容
        val firstLine = lines.firstOrNull()
        if (firstLine != null && firstLine.length > 5) {
            return firstLine.take(50) + if (firstLine.length > 50) "..." else ""
        }
        
        return "对话记录 - ${java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("MM-dd HH:mm"))}"
    }
    
    private fun showError(message: String) {
        JOptionPane.showMessageDialog(
            contentPane,
            message,
            "错误",
            JOptionPane.ERROR_MESSAGE
        )
    }
    
    override fun doOKAction() {
        val title = titleField.text.trim()
        val content = contentArea.text.trim()
        val category = categoryCombo.selectedItem as String
        
        if (title.isBlank()) {
            showError("请输入标题")
            return
        }
        
        if (content.isBlank()) {
            showError("请输入内容")
            return
        }
        
        onSave(title, content, category)
        super.doOKAction()
    }
    
    override fun doCancelAction() {
        dialogScope.cancel()
        super.doCancelAction()
    }
    
    override fun dispose() {
        dialogScope.cancel()
        super.dispose()
    }
}
