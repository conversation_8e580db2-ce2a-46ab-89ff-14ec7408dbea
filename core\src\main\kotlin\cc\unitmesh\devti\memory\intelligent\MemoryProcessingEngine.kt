package cc.unitmesh.devti.memory.intelligent

import cc.unitmesh.devti.memory.MemoryBankService
import cc.unitmesh.devti.memory.MemorySummary
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.logger
import com.intellij.openapi.project.Project
import kotlinx.coroutines.*
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong

/**
 * 智能记忆处理引擎 - 基于流程图的记忆生命周期管理
 * 
 * 功能：
 * 1. 新信息输入处理
 * 2. 工作记忆管理
 * 3. 重要性评估（周期性 + AI智能）
 * 4. 短期记忆转换
 * 5. 强化学习机制
 * 6. 长期记忆存储
 * 7. 遗忘衰减处理
 * 8. Markdown 文件导出
 */
@Service(Service.Level.PROJECT)
class MemoryProcessingEngine(private val project: Project) {
    
    companion object {
        fun getInstance(project: Project): MemoryProcessingEngine {
            return project.getService(MemoryProcessingEngine::class.java)
        }
    }
    
    private val logger = logger<MemoryProcessingEngine>()
    private val memoryBankService = MemoryBankService.getInstance(project)
    private val summaryGenerator = MemorySummaryGenerator.getInstance(project)
    private val processingMonitor = MemoryProcessingMonitor.getInstance(project)

    // 记忆处理状态
    private val workingMemories = ConcurrentHashMap<String, WorkingMemory>()
    private val shortTermMemories = ConcurrentHashMap<String, ShortTermMemory>()
    private val processingScope = CoroutineScope(Dispatchers.Default + SupervisorJob())

    // 统计信息
    private val accessCounter = ConcurrentHashMap<String, AtomicLong>()
    private val lastAccessTime = ConcurrentHashMap<String, Long>()

    // AI生成的摘要缓存
    private val generatedSummaries = ConcurrentHashMap<String, GeneratedSummary>()
    
    /**
     * 1. 新信息输入处理
     */
    suspend fun processNewInformation(
        title: String,
        content: String,
        source: String = "user_input",
        context: Map<String, Any> = emptyMap()
    ): ProcessingResult {
        logger.info("处理新信息输入: $title")
        
        try {
            // 创建工作记忆
            val workingMemory = createWorkingMemory(title, content, source, context)
            workingMemories[workingMemory.id] = workingMemory

            // 开始监控处理过程
            processingMonitor.startProcessingMonitor(workingMemory.id, title, source)
            processingMonitor.updateProcessingStage(workingMemory.id, ProcessingStage.WORKING_MEMORY, "工作记忆已创建")

            // 临时存储到工作记忆
            delay(100) // 模拟临时存储时间

            // 启动周期性评估
            schedulePeriodicEvaluation(workingMemory.id)

            return ProcessingResult.success("新信息已进入工作记忆", workingMemory.id)
            
        } catch (e: Exception) {
            logger.error("处理新信息失败", e)
            return ProcessingResult.failure("处理失败: ${e.message}")
        }
    }
    
    /**
     * 2. 周期性重要性评估
     */
    private fun schedulePeriodicEvaluation(memoryId: String) {
        processingScope.launch {
            delay(5000) // 5秒后进行首次评估
            
            val workingMemory = workingMemories[memoryId] ?: return@launch
            
            // 周期性评估
            val periodicImportance = evaluatePeriodicImportance(workingMemory)
            
            // 如果需要，触发AI智能评估
            val finalImportance = if (periodicImportance >= 3) {
                evaluateWithAI(workingMemory)
            } else {
                periodicImportance
            }
            
            // 根据重要性决定下一步处理
            when {
                finalImportance >= 4 -> promoteToShortTerm(workingMemory, finalImportance)
                finalImportance >= 2 -> keepInWorking(workingMemory, finalImportance)
                else -> initiateForgetProcess(workingMemory)
            }
        }
    }
    
    /**
     * 3. 周期性重要性评估算法
     */
    private fun evaluatePeriodicImportance(workingMemory: WorkingMemory): Int {
        var importance = 1
        
        // 基于内容长度
        if (workingMemory.content.length > 500) importance++
        
        // 基于关键词
        val keywords = listOf("重要", "关键", "核心", "问题", "解决", "bug", "error", "critical")
        if (keywords.any { workingMemory.content.contains(it, ignoreCase = true) }) {
            importance++
        }
        
        // 基于代码内容
        if (workingMemory.content.contains("```") || workingMemory.content.contains("function") || 
            workingMemory.content.contains("class")) {
            importance++
        }
        
        // 基于上下文
        if (workingMemory.context.containsKey("urgent") || workingMemory.context.containsKey("important")) {
            importance++
        }
        
        return minOf(importance, 5)
    }
    
    /**
     * 4. AI智能多维度分析
     */
    private suspend fun evaluateWithAI(workingMemory: WorkingMemory): Int {
        return try {
            // 这里可以集成现有的大模型访问逻辑
            val aiEvaluation = requestAIEvaluation(workingMemory)
            aiEvaluation.importance
        } catch (e: Exception) {
            logger.warn("AI评估失败，使用周期性评估结果", e)
            evaluatePeriodicImportance(workingMemory)
        }
    }
    
    /**
     * 5. 提升到短期记忆
     */
    private suspend fun promoteToShortTerm(workingMemory: WorkingMemory, importance: Int) {
        logger.info("提升到短期记忆: ${workingMemory.title}")
        
        val shortTermMemory = ShortTermMemory(
            id = workingMemory.id,
            title = workingMemory.title,
            content = workingMemory.content,
            importance = importance,
            createdAt = workingMemory.createdAt,
            lastAccessed = System.currentTimeMillis(),
            accessCount = 1,
            source = workingMemory.source,
            context = workingMemory.context
        )
        
        shortTermMemories[shortTermMemory.id] = shortTermMemory
        workingMemories.remove(workingMemory.id)
        
        // 根据重要性和使用频率决定后续处理
        scheduleShortTermProcessing(shortTermMemory.id)
    }
    
    /**
     * 6. 短期记忆处理调度
     */
    private fun scheduleShortTermProcessing(memoryId: String) {
        processingScope.launch {
            delay(10000) // 10秒后评估
            
            val shortTermMemory = shortTermMemories[memoryId] ?: return@launch
            val accessCount = accessCounter[memoryId]?.get() ?: 0
            
            when {
                // 高重要性 + 频繁使用 -> 强化学习
                shortTermMemory.importance >= 4 && accessCount >= 3 -> {
                    reinforceLearning(shortTermMemory)
                }
                // 低重要性 + 很少使用 -> 遗忘衰减
                shortTermMemory.importance <= 2 && accessCount <= 1 -> {
                    initiateForgetDecay(shortTermMemory)
                }
                // 其他情况继续在短期记忆中
                else -> {
                    extendShortTermLife(shortTermMemory)
                }
            }
        }
    }
    
    /**
     * 7. 强化学习 - 提升重要性
     */
    private suspend fun reinforceLearning(shortTermMemory: ShortTermMemory) {
        logger.info("强化学习: ${shortTermMemory.title}")
        
        val enhancedImportance = minOf(shortTermMemory.importance + 1, 5)
        val updatedMemory = shortTermMemory.copy(importance = enhancedImportance)
        shortTermMemories[updatedMemory.id] = updatedMemory
        
        // 如果重要性足够高，转为长期记忆
        if (enhancedImportance >= 4) {
            promoteToLongTerm(updatedMemory)
        }
    }
    
    /**
     * 8. 转为长期记忆 - 集成AI摘要生成
     */
    private suspend fun promoteToLongTerm(shortTermMemory: ShortTermMemory) {
        logger.info("转为长期记忆: ${shortTermMemory.title}")

        try {
            // 生成AI摘要
            val summaryResult = summaryGenerator.generateSummary(
                title = shortTermMemory.title,
                content = shortTermMemory.content,
                context = shortTermMemory.context
            )

            val (finalTitle, finalContent, finalTags, finalCategory, finalImportance) = if (summaryResult.success && summaryResult.summary != null) {
                val aiSummary = summaryResult.summary

                // 缓存生成的摘要
                generatedSummaries[shortTermMemory.id] = aiSummary

                // 使用AI建议的信息
                val enhancedContent = buildString {
                    append("# ${shortTermMemory.title}\n\n")
                    append("## AI生成摘要\n")
                    append("${aiSummary.summary}\n\n")
                    append("## 原始内容\n")
                    append(shortTermMemory.content)

                    if (aiSummary.relatedConcepts.isNotEmpty()) {
                        append("\n\n## 相关概念\n")
                        aiSummary.relatedConcepts.forEach { concept ->
                            append("- $concept\n")
                        }
                    }

                    if (aiSummary.keywords.isNotEmpty()) {
                        append("\n\n## 关键词\n")
                        append(aiSummary.keywords.joinToString(", "))
                    }
                }

                Tuple5(
                    shortTermMemory.title,
                    enhancedContent,
                    (aiSummary.suggestedTags + extractTags(shortTermMemory.content)).distinct(),
                    aiSummary.suggestedCategory,
                    maxOf(shortTermMemory.importance, aiSummary.importance)
                )
            } else {
                // AI摘要生成失败，使用原始信息
                logger.warn("AI摘要生成失败: ${summaryResult.error}")
                Tuple5(
                    shortTermMemory.title,
                    shortTermMemory.content,
                    extractTags(shortTermMemory.content),
                    determineCategory(shortTermMemory.content, shortTermMemory.context),
                    shortTermMemory.importance
                )
            }

            // 转换为 MemorySummary 格式
            val longTermMemory = MemorySummary(
                id = shortTermMemory.id,
                title = finalTitle,
                content = finalContent,
                tags = finalTags,
                category = finalCategory,
                importance = finalImportance,
                createdAt = shortTermMemory.createdAt,
                updatedAt = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
            )

            // 保存到记忆银行
            memoryBankService.addMemory(
                longTermMemory.title,
                longTermMemory.content,
                longTermMemory.category,
                longTermMemory.tags,
                longTermMemory.importance
            )

            // 导出为 Markdown 文件
            exportToMarkdownFile(longTermMemory)

            // 清理短期记忆
            shortTermMemories.remove(shortTermMemory.id)

            // 基于艾宾浩斯遗忘曲线算法调度复习
            scheduleReviewBasedOnForgettingCurve(longTermMemory.id)

            logger.info("长期记忆创建成功，包含AI摘要: ${longTermMemory.title}")

        } catch (e: Exception) {
            logger.error("转为长期记忆失败", e)
            // 降级处理：不使用AI摘要
            promoteToLongTermFallback(shortTermMemory)
        }
    }

    /**
     * 降级处理：不使用AI摘要的长期记忆转换
     */
    private suspend fun promoteToLongTermFallback(shortTermMemory: ShortTermMemory) {
        val longTermMemory = MemorySummary(
            id = shortTermMemory.id,
            title = shortTermMemory.title,
            content = shortTermMemory.content,
            tags = extractTags(shortTermMemory.content),
            category = determineCategory(shortTermMemory.content, shortTermMemory.context),
            importance = shortTermMemory.importance,
            createdAt = shortTermMemory.createdAt,
            updatedAt = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
        )

        memoryBankService.addMemory(
            longTermMemory.title,
            longTermMemory.content,
            longTermMemory.category,
            longTermMemory.tags,
            longTermMemory.importance
        )

        shortTermMemories.remove(shortTermMemory.id)
    }

    /**
     * 获取记忆的AI生成摘要
     */
    fun getGeneratedSummary(memoryId: String): GeneratedSummary? {
        return generatedSummaries[memoryId]
    }

    /**
     * 获取所有AI生成的摘要
     */
    fun getAllGeneratedSummaries(): Map<String, GeneratedSummary> {
        return generatedSummaries.toMap()
    }

    /**
     * 9. 遗忘衰减处理
     */
    private suspend fun initiateForgetDecay(shortTermMemory: ShortTermMemory) {
        logger.info("遗忘衰减: ${shortTermMemory.title}")
        
        val decayedImportance = maxOf(shortTermMemory.importance - 1, 1)
        
        if (decayedImportance <= 1) {
            // 自动清理
            shortTermMemories.remove(shortTermMemory.id)
            accessCounter.remove(shortTermMemory.id)
            lastAccessTime.remove(shortTermMemory.id)
        } else {
            // 降低重要性继续保留
            val decayedMemory = shortTermMemory.copy(importance = decayedImportance)
            shortTermMemories[decayedMemory.id] = decayedMemory
        }
    }
    
    /**
     * 10. 记忆访问接口 - 用于丰富上下文
     */
    fun accessMemory(memoryId: String): MemoryAccessResult? {
        // 更新访问统计
        accessCounter.computeIfAbsent(memoryId) { AtomicLong(0) }.incrementAndGet()
        lastAccessTime[memoryId] = System.currentTimeMillis()
        
        // 查找记忆
        workingMemories[memoryId]?.let { 
            return MemoryAccessResult.fromWorking(it)
        }
        
        shortTermMemories[memoryId]?.let { 
            return MemoryAccessResult.fromShortTerm(it)
        }
        
        // 查找长期记忆
        memoryBankService.getAllMemories().find { it.id == memoryId }?.let {
            return MemoryAccessResult.fromLongTerm(it)
        }
        
        return null
    }
    
    /**
     * 11. 获取相关记忆 - 用于上下文丰富
     */
    fun getRelatedMemories(query: String, limit: Int = 5): List<MemoryAccessResult> {
        val results = mutableListOf<MemoryAccessResult>()
        
        // 搜索工作记忆
        workingMemories.values.filter { 
            it.title.contains(query, ignoreCase = true) || 
            it.content.contains(query, ignoreCase = true) 
        }.take(limit).forEach {
            results.add(MemoryAccessResult.fromWorking(it))
        }
        
        // 搜索短期记忆
        if (results.size < limit) {
            shortTermMemories.values.filter { 
                it.title.contains(query, ignoreCase = true) || 
                it.content.contains(query, ignoreCase = true) 
            }.take(limit - results.size).forEach {
                results.add(MemoryAccessResult.fromShortTerm(it))
            }
        }
        
        // 搜索长期记忆
        if (results.size < limit) {
            memoryBankService.searchMemories(query).take(limit - results.size).forEach {
                results.add(MemoryAccessResult.fromLongTerm(it))
            }
        }
        
        return results.sortedByDescending { it.importance }
    }
    
    // 辅助方法
    private fun createWorkingMemory(title: String, content: String, source: String, context: Map<String, Any>): WorkingMemory {
        // 将 Map<String, Any> 转换为 Map<String, String>
        val stringContext = context.mapValues { (_, value) ->
            when (value) {
                is String -> value
                is Number -> value.toString()
                is Boolean -> value.toString()
                else -> value.toString()
            }
        }

        return WorkingMemory(
            id = generateMemoryId(),
            title = title,
            content = content,
            createdAt = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
            source = source,
            context = stringContext
        )
    }
    
    private fun generateMemoryId(): String {
        return "mem_${System.currentTimeMillis()}_${(1000..9999).random()}"
    }
    
    private fun extractTags(content: String): List<String> {
        val tags = mutableSetOf<String>()
        
        // 基于关键词提取标签
        val keywords = mapOf(
            "kotlin" to listOf("kotlin", "fun ", "class ", "object "),
            "java" to listOf("java", "public class", "private ", "static "),
            "javascript" to listOf("javascript", "function", "const ", "let "),
            "python" to listOf("python", "def ", "import ", "class "),
            "bug" to listOf("bug", "error", "exception", "问题"),
            "solution" to listOf("solution", "解决", "fix", "修复"),
            "api" to listOf("api", "接口", "endpoint", "request"),
            "database" to listOf("database", "数据库", "sql", "query")
        )
        
        keywords.forEach { (tag, patterns) ->
            if (patterns.any { content.contains(it, ignoreCase = true) }) {
                tags.add(tag)
            }
        }
        
        return tags.toList()
    }
    
    private fun determineCategory(content: String, context: Map<String, Any>): String {
        return when {
            content.contains("```") || content.contains("function") || content.contains("class") -> "code"
            content.contains("设计") || content.contains("UI") || content.contains("界面") -> "design"
            content.contains("会议") || content.contains("讨论") || content.contains("决定") -> "discussion"
            content.contains("bug") || content.contains("error") || content.contains("问题") -> "issue"
            context.containsKey("category") -> context["category"].toString()
            else -> "general"
        }
    }
    
    private suspend fun exportToMarkdownFile(memory: MemorySummary) {
        // 这里可以实现导出到 Markdown 文件的逻辑
        // 与现有的导出功能集成
        logger.info("导出长期记忆到 Markdown 文件: ${memory.title}")
    }
    
    private fun scheduleReviewBasedOnForgettingCurve(memoryId: String) {
        // 基于艾宾浩斯遗忘曲线的复习调度
        // 1天、3天、7天、15天、30天后提醒复习
        logger.info("调度基于遗忘曲线的复习: $memoryId")
    }
    
    private suspend fun requestAIEvaluation(workingMemory: WorkingMemory): AIEvaluationResult {
        // 这里集成现有的大模型访问逻辑
        // 返回AI评估结果
        return AIEvaluationResult(
            importance = evaluatePeriodicImportance(workingMemory),
            confidence = 0.8,
            reasoning = "基于内容分析的重要性评估"
        )
    }
    
    private suspend fun keepInWorking(workingMemory: WorkingMemory, importance: Int) {
        // 继续保留在工作记忆中
        val updatedMemory = workingMemory.copy(
            lastEvaluated = System.currentTimeMillis(),
            evaluatedImportance = importance
        )
        workingMemories[updatedMemory.id] = updatedMemory
    }
    
    private suspend fun initiateForgetProcess(workingMemory: WorkingMemory) {
        // 启动遗忘流程
        logger.info("启动遗忘流程: ${workingMemory.title}")
        workingMemories.remove(workingMemory.id)
    }
    
    private suspend fun extendShortTermLife(shortTermMemory: ShortTermMemory) {
        // 延长短期记忆生命周期
        val extendedMemory = shortTermMemory.copy(
            lastAccessed = System.currentTimeMillis()
        )
        shortTermMemories[extendedMemory.id] = extendedMemory
    }
}

/**
 * 五元组数据类 - 用于返回多个值
 */
data class Tuple5<A, B, C, D, E>(
    val first: A,
    val second: B,
    val third: C,
    val fourth: D,
    val fifth: E
)
