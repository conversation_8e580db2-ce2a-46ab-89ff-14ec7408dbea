package cc.unitmesh.devti.gui.memory

import cc.unitmesh.devti.memory.MemoryBankService
import cc.unitmesh.devti.memory.MemorySummary
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.components.*
import com.intellij.util.ui.JBUI
import java.awt.*
import javax.swing.*

/**
 * 编辑记忆对话框
 */
class EditMemoryDialog(
    private val project: Project,
    private val memory: MemorySummary,
    private val onUpdate: (MemorySummary) -> Unit
) : DialogWrapper(project) {
    
    private lateinit var titleField: JBTextField
    private lateinit var categoryCombo: JComboBox<String>
    private lateinit var contentArea: JTextArea
    private lateinit var tagsField: JBTextField
    private lateinit var importanceSpinner: JSpinner
    
    init {
        title = "编辑记忆"
        setSize(700, 500)
        init()
    }
    
    override fun createCenterPanel(): JComponent {
        val mainPanel = JPanel(BorderLayout())
        mainPanel.preferredSize = Dimension(680, 450)
        
        // 创建表单面板
        val formPanel = createFormPanel()
        mainPanel.add(formPanel, BorderLayout.CENTER)
        
        return mainPanel
    }
    
    private fun createFormPanel(): JComponent {
        val panel = JPanel(GridBagLayout())
        val gbc = GridBagConstraints()
        
        // 标题
        gbc.gridx = 0; gbc.gridy = 0
        gbc.anchor = GridBagConstraints.WEST
        gbc.insets = JBUI.insets(5)
        panel.add(JBLabel("标题:"), gbc)
        
        gbc.gridx = 1
        gbc.fill = GridBagConstraints.HORIZONTAL
        gbc.weightx = 1.0
        titleField = JBTextField(memory.title)
        panel.add(titleField, gbc)
        
        // 分类
        gbc.gridx = 0; gbc.gridy = 1
        gbc.fill = GridBagConstraints.NONE
        gbc.weightx = 0.0
        panel.add(JBLabel("分类:"), gbc)
        
        gbc.gridx = 1
        gbc.fill = GridBagConstraints.HORIZONTAL
        gbc.weightx = 1.0
        categoryCombo = JComboBox(arrayOf(
            "general", "code", "design", "discussion", "issue", "idea", "important"
        ))
        categoryCombo.selectedItem = memory.category
        panel.add(categoryCombo, gbc)
        
        // 重要性
        gbc.gridx = 0; gbc.gridy = 2
        gbc.fill = GridBagConstraints.NONE
        gbc.weightx = 0.0
        panel.add(JBLabel("重要性:"), gbc)
        
        gbc.gridx = 1
        gbc.fill = GridBagConstraints.HORIZONTAL
        gbc.weightx = 1.0
        importanceSpinner = JSpinner(SpinnerNumberModel(memory.importance, 1, 5, 1))
        panel.add(importanceSpinner, gbc)
        
        // 标签
        gbc.gridx = 0; gbc.gridy = 3
        gbc.fill = GridBagConstraints.NONE
        gbc.weightx = 0.0
        panel.add(JBLabel("标签:"), gbc)
        
        gbc.gridx = 1
        gbc.fill = GridBagConstraints.HORIZONTAL
        gbc.weightx = 1.0
        tagsField = JBTextField(memory.tags.joinToString(", "))
        panel.add(tagsField, gbc)
        
        // 内容
        gbc.gridx = 0; gbc.gridy = 4
        gbc.fill = GridBagConstraints.NONE
        gbc.weightx = 0.0
        gbc.anchor = GridBagConstraints.NORTHWEST
        panel.add(JBLabel("内容:"), gbc)
        
        gbc.gridx = 1
        gbc.fill = GridBagConstraints.BOTH
        gbc.weightx = 1.0
        gbc.weighty = 1.0
        contentArea = JTextArea(memory.content).apply {
            lineWrap = true
            wrapStyleWord = true
            font = JBUI.Fonts.label()
        }
        
        val scrollPane = JBScrollPane(contentArea).apply {
            preferredSize = Dimension(500, 300)
            verticalScrollBarPolicy = JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED
            horizontalScrollBarPolicy = JScrollPane.HORIZONTAL_SCROLLBAR_NEVER
        }
        
        panel.add(scrollPane, gbc)
        
        return panel
    }
    
    override fun doOKAction() {
        val title = titleField.text.trim()
        val category = categoryCombo.selectedItem as String
        val content = contentArea.text.trim()
        val importance = importanceSpinner.value as Int
        val tags = tagsField.text.split(",").map { it.trim() }.filter { it.isNotEmpty() }
        
        if (title.isBlank()) {
            showError("请输入标题")
            return
        }
        
        if (content.isBlank()) {
            showError("请输入内容")
            return
        }
        
        // 创建更新后的记忆
        val updatedMemory = memory.copy(
            title = title,
            category = category,
            content = content,
            importance = importance,
            tags = tags,
            updatedAt = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ISO_LOCAL_DATE_TIME)
        )
        
        try {
            // 更新记忆银行中的记忆
            val memoryBankService = MemoryBankService.getInstance(project)
            val updateSuccess = memoryBankService.updateMemory(
                id = memory.id,
                title = title,
                content = content,
                category = category,
                tags = tags,
                importance = importance
            )

            if (updateSuccess) {
                // 通知更新
                onUpdate(updatedMemory)
                super.doOKAction()
            } else {
                showError("更新记忆失败：记忆不存在")
            }

        } catch (e: Exception) {
            showError("更新记忆失败: ${e.message}")
        }
    }
    
    private fun showError(message: String) {
        JOptionPane.showMessageDialog(
            contentPane,
            message,
            "错误",
            JOptionPane.ERROR_MESSAGE
        )
    }
}
