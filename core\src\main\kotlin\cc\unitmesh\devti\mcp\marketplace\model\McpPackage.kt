package cc.unitmesh.devti.mcp.marketplace.model

import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject

/**
 * MCP 包信息模型
 */
@Serializable
data class McpPackage(
    val id: String,
    val name: String,
    val displayName: String,
    val description: String,
    val version: String,
    val author: String,
    val repository: String? = null,
    val homepage: String? = null,
    val license: String? = null,
    val keywords: List<String> = emptyList(),
    val category: McpCategory = McpCategory.GENERAL,
    val installType: InstallType = InstallType.NPX,
    val installCommand: String,
    val args: List<String> = emptyList(),
    val env: Map<String, String> = emptyMap(),
    val requirements: List<String> = emptyList(),
    val tools: List<McpToolInfo> = emptyList(),
    val rating: Double = 0.0,
    val downloads: Long = 0,
    val lastUpdated: String,
    val isOfficial: Boolean = false,
    val isVerified: Boolean = false,
    val screenshots: List<String> = emptyList(),
    val documentation: String? = null,
    val changelog: String? = null
)

/**
 * MCP 工具信息
 */
@Serializable
data class McpToolInfo(
    val name: String,
    val description: String,
    val parameters: JsonObject = JsonObject(emptyMap())
)

/**
 * MCP 包分类
 */
@Serializable
enum class McpCategory(val displayName: String, val description: String) {
    GENERAL("General", "General purpose tools"),
    DEVELOPMENT("Development", "Development and coding tools"),
    DATA("Data", "Data processing and analysis tools"),
    AI_ML("AI/ML", "Artificial Intelligence and Machine Learning tools"),
    WEB("Web", "Web development and API tools"),
    DATABASE("Database", "Database and storage tools"),
    SYSTEM("System", "System administration and monitoring tools"),
    PRODUCTIVITY("Productivity", "Productivity and workflow tools"),
    COMMUNICATION("Communication", "Communication and collaboration tools"),
    SECURITY("Security", "Security and authentication tools"),
    TESTING("Testing", "Testing and quality assurance tools"),
    DOCUMENTATION("Documentation", "Documentation and knowledge management tools")
}

/**
 * 安装类型
 */
@Serializable
enum class InstallType(val displayName: String, val description: String) {
    NPX("NPX", "Install via npx (Node.js package)"),
    NPM("NPM", "Install via npm globally"),
    PYTHON("Python", "Install via pip (Python package)"),
    DOCKER("Docker", "Run via Docker container"),
    BINARY("Binary", "Download and run binary executable"),
    GIT("Git", "Clone from Git repository"),
    CUSTOM("Custom", "Custom installation command")
}

/**
 * 安装状态
 */
enum class InstallStatus {
    NOT_INSTALLED,
    INSTALLING,
    INSTALLED,
    FAILED,
    UPDATING,
    UNINSTALLING
}

/**
 * MCP 包安装信息
 */
data class McpPackageInstallation(
    val packageId: String,
    val installedVersion: String,
    val installPath: String? = null,
    val installDate: String,
    val status: InstallStatus = InstallStatus.INSTALLED,
    val configName: String? = null, // 在 MCP 配置中的名称
    val isEnabled: Boolean = true
)

/**
 * 市场搜索过滤器
 */
data class MarketplaceFilter(
    val query: String = "",
    val category: McpCategory? = null,
    val installType: InstallType? = null,
    val isOfficial: Boolean? = null,
    val isVerified: Boolean? = null,
    val sortBy: SortBy = SortBy.RELEVANCE
)

/**
 * 排序方式
 */
enum class SortBy(val displayName: String) {
    RELEVANCE("Relevance"),
    NAME("Name"),
    RATING("Rating"),
    DOWNLOADS("Downloads"),
    LAST_UPDATED("Last Updated"),
    NEWEST("Newest")
}

/**
 * 市场响应数据
 */
@Serializable
data class MarketplaceResponse(
    val packages: List<McpPackage>,
    val total: Int,
    val page: Int,
    val pageSize: Int,
    val hasMore: Boolean
)
