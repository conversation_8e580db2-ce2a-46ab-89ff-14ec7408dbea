package cc.unitmesh.devti.gui.toolbar

import cc.unitmesh.devti.memory.intelligent.LLMMemoryIntegration
import com.intellij.icons.AllIcons
import com.intellij.notification.NotificationGroupManager
import com.intellij.notification.NotificationType
import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.ui.Messages
import javax.swing.JCheckBox
import javax.swing.JLabel
import javax.swing.JPanel
import javax.swing.JSpinner
import javax.swing.SpinnerNumberModel
import java.awt.GridBagConstraints
import java.awt.GridBagLayout
import java.awt.Insets

/**
 * LLM记忆配置Action
 */
class LLMMemoryConfigAction : AnAction(
    "LLM记忆配置",
    "配置LLM记忆增强和存储功能",
    AllIcons.General.Settings
) {

    override fun getActionUpdateThread(): ActionUpdateThread = ActionUpdateThread.BGT

    override fun update(e: AnActionEvent) {
        val project = e.project
        e.presentation.isEnabled = project != null
    }

    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return
        
        val integration = LLMMemoryIntegration.getInstance(project)
        val currentConfig = integration.getConfiguration()
        
        // 创建配置面板
        val panel = createConfigPanel(currentConfig)
        
        val result = Messages.showOkCancelDialog(
            panel,
//            "LLM记忆功能配置",
            "确定",
            "取消",
            AllIcons.General.Settings
        )
        
        if (result == Messages.OK) {
            // 应用配置
            applyConfiguration(integration, panel)
            
            showNotification(
                project,
                "配置已保存",
                "LLM记忆功能配置已更新",
                NotificationType.INFORMATION
            )
        }
    }
    
    private fun createConfigPanel(config: cc.unitmesh.devti.memory.intelligent.LLMMemoryConfig): JPanel {
        val panel = JPanel(GridBagLayout())
        val gbc = GridBagConstraints()
        
        // 记忆存储开关
        gbc.gridx = 0; gbc.gridy = 0
        gbc.anchor = GridBagConstraints.WEST
        gbc.insets = Insets(5, 5, 5, 5)
        panel.add(JLabel("启用记忆存储:"), gbc)
        
        gbc.gridx = 1
        val memoryStorageCheckBox = JCheckBox("", config.memoryStorageEnabled)
        memoryStorageCheckBox.name = "memoryStorageEnabled"
        panel.add(memoryStorageCheckBox, gbc)
        
        // 上下文增强开关
        gbc.gridx = 0; gbc.gridy = 1
        panel.add(JLabel("启用上下文增强:"), gbc)
        
        gbc.gridx = 1
        val contextEnrichmentCheckBox = JCheckBox("", config.contextEnrichmentEnabled)
        contextEnrichmentCheckBox.name = "contextEnrichmentEnabled"
        panel.add(contextEnrichmentCheckBox, gbc)
        
        // 自动保存阈值
        gbc.gridx = 0; gbc.gridy = 2
        panel.add(JLabel("自动保存阈值:"), gbc)
        
        gbc.gridx = 1
        val thresholdSpinner = JSpinner(SpinnerNumberModel(config.autoSaveThreshold, 50, 1000, 10))
        thresholdSpinner.name = "autoSaveThreshold"
        panel.add(thresholdSpinner, gbc)
        
        // 说明文本
        gbc.gridx = 0; gbc.gridy = 3
        gbc.gridwidth = 2
        gbc.fill = GridBagConstraints.HORIZONTAL
        gbc.insets = Insets(15, 5, 5, 5)
        
        val helpText = """
            <html>
            <b>功能说明:</b><br>
            • <b>记忆存储</b>: 自动将重要的LLM对话保存为记忆<br>
            • <b>上下文增强</b>: 使用相关记忆增强LLM输入提示<br>
            • <b>自动保存阈值</b>: 内容长度超过此值时考虑自动保存<br><br>
            
            <b>注意事项:</b><br>
            • 上下文增强会增加LLM调用的token消耗<br>
            • 记忆存储是异步进行的，不会影响响应速度<br>
            • 可以随时在记忆银行中查看和管理保存的对话
            </html>
        """.trimIndent()
        
        val helpLabel = JLabel(helpText)
        panel.add(helpLabel, gbc)
        
        return panel
    }
    
    private fun applyConfiguration(integration: LLMMemoryIntegration, panel: JPanel) {
        // 获取组件值
        val memoryStorageEnabled = (panel.components.find { 
            it.name == "memoryStorageEnabled" 
        } as? JCheckBox)?.isSelected ?: true
        
        val contextEnrichmentEnabled = (panel.components.find { 
            it.name == "contextEnrichmentEnabled" 
        } as? JCheckBox)?.isSelected ?: true
        
        val autoSaveThreshold = (panel.components.find { 
            it.name == "autoSaveThreshold" 
        } as? JSpinner)?.value as? Int ?: 100
        
        // 应用配置
        integration.setMemoryStorageEnabled(memoryStorageEnabled)
        integration.setContextEnrichmentEnabled(contextEnrichmentEnabled)
        integration.setAutoSaveThreshold(autoSaveThreshold)
    }
    
    private fun showNotification(
        project: com.intellij.openapi.project.Project,
        title: String,
        content: String,
        type: NotificationType
    ) {
        NotificationGroupManager.getInstance()
            .getNotificationGroup("AutoDev.Memory")
            .createNotification(title, content, type)
            .notify(project)
    }
}
