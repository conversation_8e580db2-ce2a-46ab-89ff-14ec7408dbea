import cc.unitmesh.devti.gui.example.CollapsiblePanelExample
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.testFramework.LightPlatformTestCase
import javax.swing.JComponent

class CollapsiblePanelExampleTest : LightPlatformTestCase() {
    companion object {
        // 新增静态方法用于获取测试用的 Project 实例
        fun getTestProject(): Project {
            return LightPlatformTestCase.getProject()
        }
    }

    private lateinit var project: Project

    override fun setUp() {
        super.setUp()
        project = getProject()
    }

    fun testCreateCenterPanel_ReturnsJComponent() {
        val example = CollapsiblePanelExample(project)
        val result = example.createCenterPanel()
        example.show()
        assert(result is JComponent)
    }

    fun testShowDialog_DisplayCollapsiblePanel() {
        val example = CollapsiblePanelExample(project)
        example.title = "Collapsible Panel Test Dialog"
        example.setContentComponent(example.createCenterPanel()) // 明确设置内容组件
        example.show()  // 调用 show() 展示对话框
    }
}

fun main() {
    val project: Project = CollapsiblePanelExampleTest.getTestProject() // 使用测试类提供的项目实例
    val example = CollapsiblePanelExample(project)
    example.title = "Collapsible Panel Manual Test"
    example.setContentComponent(example.createCenterPanel()) // 设置内容组件
    example.show()  // 调用 show() 来展示 createCenterPanel 创建的 UI 内容
}