package cc.unitmesh.devti.memory.intelligent

import cc.unitmesh.devti.memory.MemorySummary
import kotlinx.serialization.Serializable

/**
 * 工作记忆 - 临时存储新输入的信息
 */
@Serializable
data class WorkingMemory(
    val id: String,
    val title: String,
    val content: String,
    val createdAt: String,
    val source: String = "user_input", // user_input, ai_generated, system, etc.
    val context: Map<String, String> = emptyMap(), // 上下文信息
    val lastEvaluated: Long = 0L, // 最后评估时间
    val evaluatedImportance: Int = 0 // 评估的重要性
)

/**
 * 短期记忆 - 经过初步筛选的重要信息
 */
@Serializable
data class ShortTermMemory(
    val id: String,
    val title: String,
    val content: String,
    val importance: Int, // 1-5 重要性等级
    val createdAt: String,
    val lastAccessed: Long, // 最后访问时间
    val accessCount: Long, // 访问次数
    val source: String,
    val context: Map<String, String> = emptyMap(),
    val reinforcementLevel: Int = 0 // 强化等级
)

/**
 * 记忆访问结果 - 统一的记忆访问接口
 */
data class MemoryAccessResult(
    val id: String,
    val title: String,
    val content: String,
    val importance: Int,
    val type: MemoryType,
    val createdAt: String,
    val lastAccessed: Long,
    val accessCount: Long,
    val tags: List<String> = emptyList(),
    val category: String = "general",
    val source: String = "unknown"
) {
    companion object {
        fun fromWorking(working: WorkingMemory): MemoryAccessResult {
            return MemoryAccessResult(
                id = working.id,
                title = working.title,
                content = working.content,
                importance = working.evaluatedImportance,
                type = MemoryType.WORKING,
                createdAt = working.createdAt,
                lastAccessed = working.lastEvaluated,
                accessCount = 0,
                source = working.source
            )
        }
        
        fun fromShortTerm(shortTerm: ShortTermMemory): MemoryAccessResult {
            return MemoryAccessResult(
                id = shortTerm.id,
                title = shortTerm.title,
                content = shortTerm.content,
                importance = shortTerm.importance,
                type = MemoryType.SHORT_TERM,
                createdAt = shortTerm.createdAt,
                lastAccessed = shortTerm.lastAccessed,
                accessCount = shortTerm.accessCount,
                source = shortTerm.source
            )
        }
        
        fun fromLongTerm(longTerm: MemorySummary): MemoryAccessResult {
            return MemoryAccessResult(
                id = longTerm.id,
                title = longTerm.title,
                content = longTerm.content,
                importance = longTerm.importance,
                type = MemoryType.LONG_TERM,
                createdAt = longTerm.createdAt,
                lastAccessed = System.currentTimeMillis(),
                accessCount = 0,
                tags = longTerm.tags,
                category = longTerm.category,
                source = "long_term"
            )
        }
    }
}

/**
 * 记忆类型枚举
 */
enum class MemoryType {
    WORKING,     // 工作记忆
    SHORT_TERM,  // 短期记忆
    LONG_TERM    // 长期记忆
}

/**
 * 处理结果
 */
data class ProcessingResult(
    val success: Boolean,
    val message: String,
    val memoryId: String? = null,
    val data: Map<String, Any> = emptyMap()
) {
    companion object {
        fun success(message: String, memoryId: String? = null, data: Map<String, Any> = emptyMap()): ProcessingResult {
            return ProcessingResult(true, message, memoryId, data)
        }
        
        fun failure(message: String, data: Map<String, Any> = emptyMap()): ProcessingResult {
            return ProcessingResult(false, message, null, data)
        }
    }
}

/**
 * AI评估结果
 */
data class AIEvaluationResult(
    val importance: Int, // 1-5 重要性等级
    val confidence: Double, // 0.0-1.0 置信度
    val reasoning: String, // 评估理由
    val suggestedTags: List<String> = emptyList(), // 建议的标签
    val suggestedCategory: String? = null, // 建议的分类
    val relatedConcepts: List<String> = emptyList() // 相关概念
)

/**
 * 记忆统计信息
 */
data class MemoryStatistics(
    val workingMemoryCount: Int,
    val shortTermMemoryCount: Int,
    val longTermMemoryCount: Int,
    val totalProcessed: Long,
    val averageImportance: Double,
    val mostAccessedMemories: List<MemoryAccessResult>,
    val recentlyCreated: List<MemoryAccessResult>,
    val categoryDistribution: Map<String, Int>,
    val sourceDistribution: Map<String, Int>
)

/**
 * 记忆搜索请求
 */
data class MemorySearchRequest(
    val query: String,
    val types: Set<MemoryType> = setOf(MemoryType.WORKING, MemoryType.SHORT_TERM, MemoryType.LONG_TERM),
    val categories: Set<String> = emptySet(),
    val minImportance: Int = 1,
    val maxResults: Int = 10,
    val includeContent: Boolean = true,
    val sortBy: MemorySortBy = MemorySortBy.RELEVANCE
)

/**
 * 记忆排序方式
 */
enum class MemorySortBy {
    RELEVANCE,      // 相关性
    IMPORTANCE,     // 重要性
    CREATED_TIME,   // 创建时间
    ACCESS_TIME,    // 访问时间
    ACCESS_COUNT    // 访问次数
}

/**
 * 记忆上下文丰富请求
 */
data class ContextEnrichmentRequest(
    val currentContext: String, // 当前上下文
    val maxMemories: Int = 5, // 最大记忆数量
    val relevanceThreshold: Double = 0.3, // 相关性阈值
    val includeTypes: Set<MemoryType> = setOf(MemoryType.SHORT_TERM, MemoryType.LONG_TERM),
    val preferredCategories: Set<String> = emptySet() // 偏好的分类
)

/**
 * 上下文丰富结果
 */
data class ContextEnrichmentResult(
    val enrichedContext: String, // 丰富后的上下文
    val usedMemories: List<MemoryAccessResult>, // 使用的记忆
    val relevanceScores: Map<MemoryAccessResult, Double>, // 相关性分数
    val suggestions: List<String> = emptyList() // 建议
)

/**
 * 记忆导出配置
 */
data class MemoryExportConfig(
    val format: ExportFormat = ExportFormat.MARKDOWN,
    val includeMetadata: Boolean = true,
    val groupByCategory: Boolean = true,
    val sortBy: MemorySortBy = MemorySortBy.IMPORTANCE,
    val minImportance: Int = 1,
    val includeTypes: Set<MemoryType> = setOf(MemoryType.LONG_TERM),
    val outputPath: String? = null
)

/**
 * 导出格式
 */
enum class ExportFormat {
    MARKDOWN,
    JSON,
    HTML,
    PDF
}

/**
 * 记忆处理配置
 */
data class MemoryProcessingConfig(
    val enableAIEvaluation: Boolean = true, // 启用AI评估
    val aiEvaluationThreshold: Int = 3, // AI评估阈值
    val workingMemoryLifetime: Long = 300000L, // 工作记忆生命周期（5分钟）
    val shortTermMemoryLifetime: Long = 3600000L, // 短期记忆生命周期（1小时）
    val forgettingCurveEnabled: Boolean = true, // 启用遗忘曲线
    val autoExportEnabled: Boolean = true, // 自动导出
    val reinforcementThreshold: Int = 3, // 强化学习阈值
    val accessCountThreshold: Long = 3L, // 访问次数阈值
    val importanceDecayRate: Double = 0.1 // 重要性衰减率
)

/**
 * 记忆处理事件
 */
sealed class MemoryProcessingEvent {
    data class NewMemoryCreated(val memory: WorkingMemory) : MemoryProcessingEvent()
    data class MemoryPromoted(val fromType: MemoryType, val toType: MemoryType, val memoryId: String) : MemoryProcessingEvent()
    data class MemoryAccessed(val memoryId: String, val type: MemoryType) : MemoryProcessingEvent()
    data class MemoryForgotten(val memoryId: String, val type: MemoryType) : MemoryProcessingEvent()
    data class MemoryReinforced(val memoryId: String, val newImportance: Int) : MemoryProcessingEvent()
    data class MemoryExported(val memoryId: String, val format: ExportFormat) : MemoryProcessingEvent()
    data class AIEvaluationCompleted(val memoryId: String, val result: AIEvaluationResult) : MemoryProcessingEvent()
}

/**
 * 记忆处理监听器
 */
interface MemoryProcessingListener {
    fun onMemoryEvent(event: MemoryProcessingEvent)
}

/**
 * 记忆相似度计算结果
 */
data class MemorySimilarity(
    val memoryId: String,
    val similarity: Double, // 0.0-1.0 相似度
    val matchedKeywords: List<String>, // 匹配的关键词
    val semanticSimilarity: Double = 0.0 // 语义相似度（如果使用AI）
)
