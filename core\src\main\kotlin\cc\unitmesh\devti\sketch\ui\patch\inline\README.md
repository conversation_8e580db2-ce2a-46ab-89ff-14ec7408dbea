# 行内差异视图模块 (Inline Diff Viewer Module)

这是一个外挂独立模块，为现有的 `SingleFileDiffSketch` 提供行内差异视图功能，类似于 GitHub 或其他代码托管平台的行内差异显示效果。

## 功能特性

- ✅ **行内差异显示**: 在单个编辑器中显示代码差异，而不是并排显示
- ✅ **语法高亮**: 支持各种编程语言的语法高亮
- ✅ **字符级差异**: 精确显示行内字符级别的变更
- ✅ **颜色标识**: 
  - 🟢 绿色背景表示新增行
  - 🔴 红色背景表示删除行  
  - 🟡 黄色背景表示修改行
- ✅ **统计信息**: 显示添加、删除、修改的行数统计
- ✅ **切换功能**: 在传统并排视图和行内视图之间切换
- ✅ **非侵入式**: 不修改原有 `SingleFileDiffSketch` 代码

## 核心组件

### 1. DiffCalculator
负责计算文本差异的核心算法类：
```kotlin
val lineDiffs = DiffCalculator.calculateLineDiffs(oldCode, newCode)
val charDiffs = DiffCalculator.calculateCharDiffs(oldLine, newLine)
```

### 2. DiffHighlightRenderer  
负责在编辑器中渲染差异高亮效果：
```kotlin
val renderer = DiffHighlightRenderer(editor)
renderer.renderLineDiffs(lineDiffs)
```

### 3. InlineDiffViewer
主要的行内差异视图组件：
```kotlin
val viewer = InlineDiffViewer.create(project, oldCode, newCode, language, fileName)
panel.add(viewer.getComponent())
```

### 4. InlineDiffExtension
为现有 `SingleFileDiffSketch` 提供扩展功能的包装器。

## 使用方法

### 方法1: 最简单的集成方式

只需要在创建 `SingleFileDiffSketch` 的地方做如下修改：

```kotlin
// 原来的代码
val diffSketch = SingleFileDiffSketch(project, file, patch, viewDiffAction)
panel.add(diffSketch.getComponent())

// 修改后的代码  
val diffSketch = SingleFileDiffSketch(project, file, patch, viewDiffAction)
val enhancedSketch = diffSketch.withInlineDiffSupport(project, file, patch, oldCode, newCode)
panel.add(enhancedSketch.getEnhancedComponent())
```

### 方法2: 使用集成工具类

```kotlin
val originalSketch = SingleFileDiffSketch(project, file, patch, viewDiffAction)
val enhancedSketch = InlineDiffIntegration.enhance(
    originalSketch, project, file, patch, oldCode, newCode
)

// 添加到UI
panel.add(enhancedSketch.getEnhancedComponent())

// 可选：添加切换按钮
val toggleButton = InlineDiffIntegration.createToggleButton { isInlineMode ->
    if (isInlineMode) {
        enhancedSketch.switchToInlineView()
    } else {
        enhancedSketch.switchToTraditionalView()
    }
}
toolbar.add(toggleButton)
```

### 方法3: 创建独立的行内差异视图

```kotlin
val inlineViewer = InlineDiffIntegration.createStandaloneViewer(
    project, oldCode, newCode, language, fileName
)
panel.add(inlineViewer)
```

## 配置选项

可以通过 `InlineDiffConfig` 自定义行为：

```kotlin
val config = InlineDiffConfig(
    maxLinesForInlineView = 500,    // 最大支持行数
    showLineNumbers = true,         // 显示行号
    showDiffStats = true,          // 显示统计信息
    enableCharLevelDiff = true,    // 启用字符级差异
    defaultToInlineView = false    // 默认视图模式
)

val factory = InlineDiffViewerFactory(config)
val viewer = factory.createViewer(project, oldCode, newCode, language, fileName)
```

## 性能考虑

- 默认限制处理文件最大行数为 1000 行
- 对于大文件会自动回退到传统视图
- 使用 LCS 算法计算差异，时间复杂度为 O(mn)
- 支持字符级差异计算，但可以通过配置禁用

## 兼容性

- ✅ 兼容现有的 `SingleFileDiffSketch` 
- ✅ 支持所有 IntelliJ 支持的编程语言
- ✅ 支持 IntelliJ IDEA 2020.3+
- ✅ 不影响原有的差异视图功能

## 文件结构

```
inline/
├── DiffCalculator.kt           # 差异计算算法
├── DiffHighlightRenderer.kt    # 差异高亮渲染
├── InlineDiffViewer.kt         # 主要视图组件
├── InlineDiffExtension.kt      # 扩展包装器
├── InlineDiffIntegration.kt    # 集成工具类
├── InlineDiffUsageExample.kt   # 使用示例
└── README.md                   # 说明文档
```

## 集成到现有项目

### 步骤1: 复制文件
将整个 `inline` 包复制到您的项目中：
```
core/src/main/kotlin/cc/unitmesh/devti/sketch/ui/patch/inline/
```

### 步骤2: 修改现有代码
在创建 `SingleFileDiffSketch` 的地方进行如下修改：

**原有代码**:
```kotlin
val diffSketch = SingleFileDiffSketch(project, file, patch, viewDiffAction)
panel.add(diffSketch.getComponent())
```

**修改后的代码**:
```kotlin
val diffSketch = SingleFileDiffSketch(project, file, patch, viewDiffAction)

// 检查是否支持行内视图
if (InlineDiffIntegration.isSupported(oldCode, newCode)) {
    val enhancedSketch = diffSketch.withInlineDiffSupport(project, file, patch, oldCode, newCode)
    panel.add(enhancedSketch.getEnhancedComponent())
} else {
    // 回退到原有视图
    panel.add(diffSketch.getComponent())
}
```

### 步骤3: 添加切换按钮（可选）
```kotlin
val toggleButton = InlineDiffIntegration.createToggleButton { isInlineMode ->
    // 处理视图切换逻辑
}
toolbar.add(toggleButton)
```

## 测试验证

运行 `InlineDiffTest.kt` 来验证功能：
```kotlin
InlineDiffTest.main(arrayOf())
```

## 注意事项

- ✅ **非侵入式**: 不会修改原有的 `SingleFileDiffSketch` 类
- ⚡ **性能优化**: 对于超大文件（>1000行），自动回退到传统视图
- 🎨 **可定制**: 字符级差异计算可以通过配置禁用
- 🧹 **资源管理**: 确保在组件销毁时调用 `dispose()` 方法释放资源
- 🔧 **向后兼容**: 完全兼容现有的差异视图功能

## 故障排除

### 常见问题

1. **编译错误**: 确保导入了正确的包
2. **性能问题**: 对大文件使用 `InlineDiffConfig` 调整参数
3. **显示异常**: 检查是否正确调用了 `dispose()` 方法

### 调试技巧

```kotlin
// 启用调试信息
val stats = InlineDiffIntegration.calculateDiffStats(oldCode, newCode)
println("差异统计: +${stats.addedLines} -${stats.deletedLines} ~${stats.modifiedLines}")

// 检查支持性
if (!InlineDiffIntegration.isSupported(oldCode, newCode)) {
    println("文件过大，不支持行内视图")
}
```

## 扩展性

该模块设计为高度可扩展：
- 🔄 **算法扩展**: 可以添加更多的差异计算算法
- 🎨 **主题定制**: 可以自定义颜色主题和样式
- ✨ **效果增强**: 可以添加更多的渲染效果
- 🔌 **组件集成**: 可以集成到其他差异视图组件中

## 版本兼容性

- ✅ IntelliJ IDEA 2020.3+
- ✅ Kotlin 1.5+
- ✅ Java 11+
