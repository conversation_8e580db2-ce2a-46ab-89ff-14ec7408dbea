package cc.unitmesh.devti.sketch.ui.patch.inline

import cc.unitmesh.devti.sketch.ui.patch.SingleFileDiffSketch
import com.intellij.icons.AllIcons
import com.intellij.lang.Language
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.patch.TextFilePatch
import com.intellij.util.ui.JBUI
import java.awt.Dimension
import javax.swing.JButton
import javax.swing.JComponent

/**
 * 行内差异视图集成工具类
 * 提供简单的API来集成行内差异视图到现有的SingleFileDiffSketch中
 */
object InlineDiffIntegration {
    
    /**
     * 为SingleFileDiffSketch添加行内差异视图支持
     * 
     * 使用示例：
     * ```kotlin
     * val originalSketch = SingleFileDiffSketch(project, file, patch, viewDiffAction)
     * val enhancedSketch = InlineDiffIntegration.enhance(
     *     originalSketch, project, file, patch, oldCode, newCode
     * )
     * 
     * // 使用增强后的组件
     * panel.add(enhancedSketch.getEnhancedComponent())
     * 
     * // 可选：添加切换按钮到工具栏
     * val toggleButton = enhancedSketch.createToggleButton()
     * toolbar.add(toggleButton)
     * ```
     */
    fun enhance(
        originalSketch: SingleFileDiffSketch,
        project: Project,
        currentFile: VirtualFile,
        patch: TextFilePatch,
        oldCode: String,
        newCode: String
    ): EnhancedSingleFileDiffSketch {
        return EnhancedSingleFileDiffSketch(
            originalSketch, project, currentFile, patch, oldCode, newCode
        )
    }
    
    /**
     * 创建独立的行内差异视图组件
     * 
     * 使用示例：
     * ```kotlin
     * val inlineViewer = InlineDiffIntegration.createStandaloneViewer(
     *     project, oldCode, newCode, language, fileName
     * )
     * 
     * panel.add(inlineViewer)
     * ```
     */
    fun createStandaloneViewer(
        project: Project,
        oldCode: String,
        newCode: String,
        language: Language? = null,
        fileName: String? = null
    ): JComponent {
        val viewer = InlineDiffViewer.create(project, oldCode, newCode, language, fileName)
        return viewer.getComponent()
    }
    
    /**
     * 创建切换按钮
     * 
     * 使用示例：
     * ```kotlin
     * val toggleButton = InlineDiffIntegration.createToggleButton { isInlineMode ->
     *     if (isInlineMode) {
     *         // 切换到行内视图
     *         showInlineView()
     *     } else {
     *         // 切换到传统视图
     *         showTraditionalView()
     *     }
     * }
     * ```
     */
    fun createToggleButton(onToggle: (Boolean) -> Unit): JButton {
        var isInlineMode = false
        
        return JButton().apply {
            icon = AllIcons.Actions.InlayGlobe
            preferredSize = Dimension(24, 24)
            margin = JBUI.emptyInsets()
            isBorderPainted = false
            isContentAreaFilled = false
            isOpaque = false
            toolTipText = "Toggle Inline Diff View"
            
            addActionListener {
                isInlineMode = !isInlineMode
                
                // 更新图标
                icon = if (isInlineMode) {
                    AllIcons.Actions.Diff
                } else {
                    AllIcons.Actions.InlayGlobe
                }
                
                // 更新提示文本
                toolTipText = if (isInlineMode) {
                    "Switch to Side-by-Side Diff"
                } else {
                    "Switch to Inline Diff"
                }
                
                // 调用回调
                onToggle(isInlineMode)
            }
        }
    }
    
    /**
     * 检查是否支持行内差异视图
     */
    fun isSupported(oldCode: String, newCode: String): Boolean {
        return InlineDiffExtension.isSupported(oldCode, newCode)
    }
    
    /**
     * 获取差异统计信息
     */
    fun calculateDiffStats(oldCode: String, newCode: String): DiffHighlightRenderer.DiffStats {
        val lineDiffs = DiffCalculator.calculateLineDiffs(oldCode, newCode)
        var addedLines = 0
        var deletedLines = 0
        var modifiedLines = 0
        
        for (lineDiff in lineDiffs) {
            when (lineDiff) {
                is DiffCalculator.LineDiff.Added -> addedLines++
                is DiffCalculator.LineDiff.Deleted -> deletedLines++
                is DiffCalculator.LineDiff.Modified -> modifiedLines++
                is DiffCalculator.LineDiff.Unchanged -> { /* 不计算 */ }
            }
        }
        
        return DiffHighlightRenderer.DiffStats(addedLines, deletedLines, modifiedLines)
    }
}

/**
 * 扩展方法，为SingleFileDiffSketch添加行内差异视图支持
 */
fun SingleFileDiffSketch.withInlineDiffSupport(
    project: Project,
    currentFile: VirtualFile,
    patch: TextFilePatch,
    oldCode: String,
    newCode: String
): EnhancedSingleFileDiffSketch {
    return InlineDiffIntegration.enhance(this, project, currentFile, patch, oldCode, newCode)
}

/**
 * 配置类，用于自定义行内差异视图的行为
 */
data class InlineDiffConfig(
    val maxLinesForInlineView: Int = 1000,
    val showLineNumbers: Boolean = true,
    val showDiffStats: Boolean = true,
    val enableCharLevelDiff: Boolean = true,
    val defaultToInlineView: Boolean = false
) {
    companion object {
        val DEFAULT = InlineDiffConfig()
    }
}

/**
 * 行内差异视图工厂类
 */
class InlineDiffViewerFactory(private val config: InlineDiffConfig = InlineDiffConfig.DEFAULT) {
    
    fun createViewer(
        project: Project,
        oldCode: String,
        newCode: String,
        language: com.intellij.lang.Language? = null,
        fileName: String? = null
    ): InlineDiffViewer? {
        // 检查是否支持
        if (!isSupported(oldCode, newCode)) {
            return null
        }
        
        return InlineDiffViewer.create(project, oldCode, newCode, language, fileName)
    }
    
    private fun isSupported(oldCode: String, newCode: String): Boolean {
        val oldLines = oldCode.lines().size
        val newLines = newCode.lines().size
        
        return oldLines <= config.maxLinesForInlineView && newLines <= config.maxLinesForInlineView
    }
}
