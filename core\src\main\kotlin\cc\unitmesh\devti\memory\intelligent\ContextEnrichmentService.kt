package cc.unitmesh.devti.memory.intelligent

import ai.grazie.utils.text
import cc.unitmesh.devti.llms.LlmFactory
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.logger
import com.intellij.openapi.project.Project
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.collect

/**
 * 上下文丰富服务 - 基于记忆银行丰富对话上下文
 * 
 * 功能：
 * 1. 根据当前上下文检索相关记忆
 * 2. 计算记忆相关性
 * 3. 生成丰富的上下文
 * 4. 与大模型集成
 */
@Service(Service.Level.PROJECT)
class ContextEnrichmentService(private val project: Project) {
    
    companion object {
        fun getInstance(project: Project): ContextEnrichmentService {
            return project.getService(ContextEnrichmentService::class.java)
        }
    }
    
    private val logger = logger<ContextEnrichmentService>()
    private val memoryEngine = MemoryProcessingEngine.getInstance(project)
    private val processingScope = CoroutineScope(Dispatchers.Default + SupervisorJob())
    
    /**
     * 丰富上下文 - 主要接口
     */
    suspend fun enrichContext(request: ContextEnrichmentRequest): ContextEnrichmentResult {
        logger.info("开始丰富上下文，当前上下文长度: ${request.currentContext.length}")
        
        try {
            // 1. 提取关键词
            val keywords = extractKeywords(request.currentContext)
            
            // 2. 搜索相关记忆
            val relevantMemories = findRelevantMemories(keywords, request)
            
            // 3. 计算相关性分数
            val scoredMemories = calculateRelevanceScores(request.currentContext, relevantMemories)
            
            // 4. 过滤和排序
            val filteredMemories = scoredMemories
                .filter { it.value >= request.relevanceThreshold }
                .toList()
                .sortedByDescending { it.second }
                .take(request.maxMemories)
                .map { it.first }
            
            // 5. 生成丰富的上下文
            val enrichedContext = generateEnrichedContext(request.currentContext, filteredMemories)
            
            // 6. 生成建议
            val suggestions = generateSuggestions(filteredMemories)
            
            return ContextEnrichmentResult(
                enrichedContext = enrichedContext,
                usedMemories = filteredMemories,
                relevanceScores = scoredMemories,
                suggestions = suggestions
            )
            
        } catch (e: Exception) {
            logger.error("上下文丰富失败", e)
            return ContextEnrichmentResult(
                enrichedContext = request.currentContext,
                usedMemories = emptyList(),
                relevanceScores = emptyMap(),
                suggestions = listOf("上下文丰富失败: ${e.message}")
            )
        }
    }
    
    /**
     * 提取关键词
     */
    private fun extractKeywords(context: String): List<String> {
        val keywords = mutableSetOf<String>()
        
        // 基本关键词提取
        val words = context.split(Regex("\\s+"))
            .map { it.replace(Regex("[^\\w\\u4e00-\\u9fa5]"), "") }
            .filter { it.length > 2 }
        
        keywords.addAll(words)
        
        // 技术关键词
        val techKeywords = listOf(
            "kotlin", "java", "javascript", "python", "react", "vue", "spring", "android",
            "api", "database", "sql", "json", "xml", "http", "rest", "graphql",
            "bug", "error", "exception", "fix", "solution", "problem", "issue",
            "function", "class", "method", "variable", "interface", "abstract",
            "test", "unit", "integration", "mock", "stub", "debug"
        )
        
        techKeywords.forEach { keyword ->
            if (context.contains(keyword, ignoreCase = true)) {
                keywords.add(keyword)
            }
        }
        
        return keywords.toList()
    }
    
    /**
     * 查找相关记忆
     */
    private fun findRelevantMemories(keywords: List<String>, request: ContextEnrichmentRequest): List<MemoryAccessResult> {
        val allMemories = mutableListOf<MemoryAccessResult>()
        
        // 为每个关键词搜索记忆
        keywords.forEach { keyword ->
            val memories = memoryEngine.getRelatedMemories(keyword, 10)
            allMemories.addAll(memories)
        }
        
        // 去重并过滤
        return allMemories
            .distinctBy { it.id }
            .filter { memory ->
                request.includeTypes.contains(memory.type) &&
                (request.preferredCategories.isEmpty() || request.preferredCategories.contains(memory.category))
            }
    }
    
    /**
     * 计算相关性分数
     */
    private fun calculateRelevanceScores(
        context: String, 
        memories: List<MemoryAccessResult>
    ): Map<MemoryAccessResult, Double> {
        val scores = mutableMapOf<MemoryAccessResult, Double>()
        
        memories.forEach { memory ->
            var score = 0.0
            
            // 1. 关键词匹配分数
            val keywordScore = calculateKeywordMatchScore(context, memory)
            score += keywordScore * 0.4
            
            // 2. 重要性分数
            val importanceScore = memory.importance / 5.0
            score += importanceScore * 0.3
            
            // 3. 访问频率分数
            val accessScore = minOf(memory.accessCount / 10.0, 1.0)
            score += accessScore * 0.2
            
            // 4. 时间新鲜度分数
            val freshnessScore = calculateFreshnessScore(memory.lastAccessed)
            score += freshnessScore * 0.1
            
            scores[memory] = score
        }
        
        return scores
    }
    
    /**
     * 计算关键词匹配分数
     */
    private fun calculateKeywordMatchScore(context: String, memory: MemoryAccessResult): Double {
        val contextWords = context.lowercase().split(Regex("\\s+")).toSet()
        val memoryWords = (memory.title + " " + memory.content).lowercase().split(Regex("\\s+")).toSet()
        
        val intersection = contextWords.intersect(memoryWords)
        val union = contextWords.union(memoryWords)
        
        return if (union.isNotEmpty()) {
            intersection.size.toDouble() / union.size.toDouble()
        } else {
            0.0
        }
    }
    
    /**
     * 计算时间新鲜度分数
     */
    private fun calculateFreshnessScore(lastAccessed: Long): Double {
        val now = System.currentTimeMillis()
        val daysSinceAccess = (now - lastAccessed) / (24 * 60 * 60 * 1000)
        
        return when {
            daysSinceAccess <= 1 -> 1.0
            daysSinceAccess <= 7 -> 0.8
            daysSinceAccess <= 30 -> 0.6
            daysSinceAccess <= 90 -> 0.4
            else -> 0.2
        }
    }
    
    /**
     * 生成丰富的上下文
     */
    private fun generateEnrichedContext(
        originalContext: String, 
        relevantMemories: List<MemoryAccessResult>
    ): String {
        if (relevantMemories.isEmpty()) {
            return originalContext
        }
        
        val contextBuilder = StringBuilder()
        
        // 添加原始上下文
        contextBuilder.append("## 当前上下文\n")
        contextBuilder.append(originalContext)
        contextBuilder.append("\n\n")
        
        // 添加相关记忆
        contextBuilder.append("## 相关记忆\n")
        relevantMemories.forEachIndexed { index, memory ->
            contextBuilder.append("### ${index + 1}. ${memory.title}\n")
            contextBuilder.append("**类型**: ${memory.type.name} | ")
            contextBuilder.append("**重要性**: ${"★".repeat(memory.importance)} | ")
            contextBuilder.append("**分类**: ${memory.category}\n")
            
            if (memory.tags.isNotEmpty()) {
                contextBuilder.append("**标签**: ${memory.tags.joinToString(", ")}\n")
            }
            
            // 添加内容摘要（限制长度）
            val contentSummary = if (memory.content.length > 200) {
                memory.content.take(200) + "..."
            } else {
                memory.content
            }
            contextBuilder.append("**内容**: $contentSummary\n\n")
        }
        
        return contextBuilder.toString()
    }
    
    /**
     * 生成建议
     */
    private fun generateSuggestions(memories: List<MemoryAccessResult>): List<String> {
        val suggestions = mutableListOf<String>()
        
        if (memories.isEmpty()) {
            suggestions.add("没有找到相关记忆，建议创建新的记忆记录")
            return suggestions
        }
        
        // 基于记忆类型的建议
        val memoryTypes = memories.groupBy { it.type }
        
        if (memoryTypes.containsKey(MemoryType.WORKING)) {
            suggestions.add("发现相关的工作记忆，建议及时整理和归档")
        }
        
        if (memoryTypes.containsKey(MemoryType.SHORT_TERM)) {
            suggestions.add("发现相关的短期记忆，可能需要进一步强化学习")
        }
        
        if (memoryTypes.containsKey(MemoryType.LONG_TERM)) {
            suggestions.add("发现相关的长期记忆，建议复习和更新")
        }
        
        // 基于分类的建议
        val categories = memories.groupBy { it.category }
        
        if (categories.containsKey("code")) {
            suggestions.add("发现相关代码记忆，建议查看相关实现和最佳实践")
        }
        
        if (categories.containsKey("issue")) {
            suggestions.add("发现相关问题记忆，建议查看解决方案和经验教训")
        }
        
        // 基于重要性的建议
        val highImportanceMemories = memories.filter { it.importance >= 4 }
        if (highImportanceMemories.isNotEmpty()) {
            suggestions.add("发现${highImportanceMemories.size}个高重要性记忆，建议重点关注")
        }
        
        return suggestions
    }
    
    /**
     * 与大模型集成的上下文丰富
     */
    suspend fun enrichContextWithAI(request: ContextEnrichmentRequest): ContextEnrichmentResult {
        try {
            // 首先使用基础方法丰富上下文
            val basicResult = enrichContext(request)
            
            // 如果有相关记忆，使用AI进一步优化
            if (basicResult.usedMemories.isNotEmpty()) {
                val aiEnhancedContext = enhanceContextWithLLM(basicResult)
                return basicResult.copy(
                    enrichedContext = aiEnhancedContext,
                    suggestions = basicResult.suggestions + generateAISuggestions(basicResult)
                )
            }
            
            return basicResult
            
        } catch (e: Exception) {
            logger.error("AI上下文丰富失败", e)
            return enrichContext(request) // 回退到基础方法
        }
    }
    
    /**
     * 使用大模型增强上下文
     */
    private suspend fun enhanceContextWithLLM(result: ContextEnrichmentResult): String {
        return withContext(Dispatchers.IO) {
            try {
                val llm = LlmFactory.create(project)

                val prompt = """
                请基于以下信息，生成一个更加连贯和有用的上下文描述：

                原始上下文：
                ${result.enrichedContext}

                请：
                1. 整合相关记忆中的关键信息
                2. 突出重要的技术细节和解决方案
                3. 保持信息的准确性和相关性
                4. 使用清晰的Markdown格式

                增强后的上下文：
                """.trimIndent()

                val responseBuilder = StringBuilder()
                val response = llm.stream(
                    prompt,
                    systemPrompt = "你是一个专业的技术文档整理助手，擅长整合和优化技术信息。",
                    keepHistory = false,
                    usePlanForFirst = false
                )

                // 收集流式响应
                response.collect { chunk ->
                    responseBuilder.append(chunk)
                }

                val enhancedContext = responseBuilder.toString().trim()
                if (enhancedContext.isNotBlank()) {
                    enhancedContext
                } else {
                    result.enrichedContext
                }

            } catch (e: Exception) {
                logger.warn("LLM增强失败，使用原始结果", e)
                result.enrichedContext
            }
        }
    }
    
    /**
     * 生成AI建议
     */
    private fun generateAISuggestions(result: ContextEnrichmentResult): List<String> {
        val aiSuggestions = mutableListOf<String>()
        
        // 基于使用的记忆生成建议
        val codeMemories = result.usedMemories.filter { it.category == "code" }
        if (codeMemories.isNotEmpty()) {
            aiSuggestions.add("建议结合相关代码记忆进行实现参考")
        }
        
        val issueMemories = result.usedMemories.filter { it.category == "issue" }
        if (issueMemories.isNotEmpty()) {
            aiSuggestions.add("发现相关问题记录，建议查看解决方案避免重复问题")
        }
        
        return aiSuggestions
    }
    
    /**
     * 简化的上下文丰富接口 - 用于快速集成
     */
    suspend fun quickEnrichContext(context: String, maxMemories: Int = 3): String {
        val request = ContextEnrichmentRequest(
            currentContext = context,
            maxMemories = maxMemories,
            relevanceThreshold = 0.3
        )
        
        val result = enrichContext(request)
        return result.enrichedContext
    }
    
    /**
     * 获取相关记忆摘要 - 用于UI显示
     */
    fun getRelatedMemoriesSummary(context: String, limit: Int = 5): List<MemoryAccessResult> {
        val keywords = extractKeywords(context)
        return findRelevantMemories(keywords, ContextEnrichmentRequest(context, limit))
            .take(limit)
    }
}
