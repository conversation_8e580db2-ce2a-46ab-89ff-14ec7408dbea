package cc.unitmesh.devti.gui.toolbar

import cc.unitmesh.devti.AutoDevBundle
import cc.unitmesh.devti.AutoDevIcons
import cc.unitmesh.devti.gui.memory.MemoryBankDialog
import cc.unitmesh.devti.sketch.SketchToolWindow
import com.intellij.icons.AllIcons
import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.project.Project
import com.intellij.openapi.wm.ToolWindowManager

/**
 * 记忆银行 Action - 打开记忆银行界面
 */
class MemoryBankAction : AnAction(
    AutoDevBundle.message("autodev.memory.bank.title", "记忆银行"),
    AutoDevBundle.message("autodev.memory.bank.description", "查看和管理记忆摘要"),
    AllIcons.General.Balloon
) {

    override fun getActionUpdateThread(): ActionUpdateThread = ActionUpdateThread.BGT

    override fun update(e: AnActionEvent) {
        val project = e.project
        e.presentation.isEnabled = project != null
    }

    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return
        
        // 获取当前的 SketchToolWindow 实例
        val sketchToolWindow = getSketchToolWindow(project)
        
        if (sketchToolWindow != null) {
            // 如果已经在显示记忆银行，则切换回原界面
            if (sketchToolWindow is MemoryBankAwareSketchToolWindow && 
                sketchToolWindow.isShowingMemoryBank()) {
                sketchToolWindow.hideMemoryBank()
            } else {
                // 显示记忆银行界面
                showMemoryBank(project, sketchToolWindow)
            }
        } else {
            // 如果没有 SketchToolWindow，创建独立的记忆银行对话框
            val dialog = MemoryBankDialog(project)
            dialog.show()
        }
    }

    private fun getSketchToolWindow(project: Project): SketchToolWindow? {
        val toolWindowManager = ToolWindowManager.getInstance(project)
        val toolWindow = toolWindowManager.getToolWindow("AutoDev")
        
        return toolWindow?.contentManager?.selectedContent?.component as? SketchToolWindow
    }

    private fun showMemoryBank(project: Project, sketchToolWindow: SketchToolWindow) {
        if (sketchToolWindow is MemoryBankAwareSketchToolWindow) {
            sketchToolWindow.showMemoryBank()
        } else {
            // 如果当前的 SketchToolWindow 不支持记忆银行，创建对话框
            val dialog = MemoryBankDialog(project)
            dialog.show()
        }
    }
}

/**
 * 支持记忆银行功能的 SketchToolWindow 接口
 */
interface MemoryBankAwareSketchToolWindow {
    fun showMemoryBank()
    fun hideMemoryBank()
    fun isShowingMemoryBank(): Boolean
}
