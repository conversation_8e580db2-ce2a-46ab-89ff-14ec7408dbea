package cc.unitmesh.devti.mcp.marketplace.service


import cc.unitmesh.devti.mcp.marketplace.model.*
import cc.unitmesh.devti.settings.customize.customizeSetting
import com.intellij.execution.configurations.GeneralCommandLine
import com.intellij.execution.process.ProcessHandler
import com.intellij.execution.process.ProcessHandlerFactory
import com.intellij.execution.process.ProcessListener
import com.intellij.execution.process.ProcessEvent
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.State
import com.intellij.openapi.components.Storage
import com.intellij.openapi.components.PersistentStateComponent
import com.intellij.openapi.diagnostic.logger
import com.intellij.openapi.progress.ProgressIndicator
import com.intellij.openapi.progress.ProgressManager
import com.intellij.openapi.progress.Task
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Key
import com.intellij.util.messages.Topic
import kotlinx.coroutines.*
import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.jsonObject
import java.io.File
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.concurrent.CompletableFuture

/**
 * MCP 包安装状态监听器
 */
interface McpPackageInstallListener {
    fun onInstallStarted(packageId: String)
    fun onInstallProgress(packageId: String, progress: Int, message: String)
    fun onInstallCompleted(packageId: String, success: Boolean, message: String)
    fun onUninstallCompleted(packageId: String, success: Boolean)
    
    companion object {
        val TOPIC = Topic.create("McpPackageInstall", McpPackageInstallListener::class.java)
    }
}

/**
 * MCP 包安装管理器
 */
@Service(Service.Level.PROJECT)
@State(
    name = "McpPackageInstaller",
    storages = [Storage("mcp-packages.xml")]
)
class McpPackageInstaller(private val project: Project) : PersistentStateComponent<McpPackageInstaller.State> {
    
    data class State(
        var installedPackages: MutableMap<String, McpPackageInstallation> = mutableMapOf()
    )
    
    private var state = State()
    private val logger = logger<McpPackageInstaller>()
    private val json = Json { ignoreUnknownKeys = true }
    
    companion object {
        fun getInstance(project: Project): McpPackageInstaller {
            return project.getService(McpPackageInstaller::class.java)
        }
    }
    
    override fun getState(): State = state
    override fun loadState(state: State) {
        this.state = state
    }
    
    /**
     * 安装 MCP 包
     */
    fun installPackage(
        mcpPackage: McpPackage,
        onProgress: ((Int, String) -> Unit)? = null
    ): CompletableFuture<Boolean> {
        val future = CompletableFuture<Boolean>()
        
        // 通知开始安装
        ApplicationManager.getApplication().messageBus
            .syncPublisher(McpPackageInstallListener.TOPIC)
            .onInstallStarted(mcpPackage.id)
        
        ProgressManager.getInstance().run(object : Task.Backgroundable(project, "Installing ${mcpPackage.displayName}", true) {
            override fun run(indicator: ProgressIndicator) {
                try {
                    indicator.text = "Installing ${mcpPackage.displayName}..."
                    indicator.fraction = 0.1
                    onProgress?.invoke(10, "Starting installation...")
                    
                    val success = when (mcpPackage.installType) {
                        InstallType.NPX -> installNpxPackage(mcpPackage, indicator, onProgress)
                        InstallType.NPM -> installNpmPackage(mcpPackage, indicator, onProgress)
                        InstallType.PYTHON -> installPythonPackage(mcpPackage, indicator, onProgress)
                        InstallType.DOCKER -> installDockerPackage(mcpPackage, indicator, onProgress)
                        InstallType.BINARY -> installBinaryPackage(mcpPackage, indicator, onProgress)
                        InstallType.GIT -> installGitPackage(mcpPackage, indicator, onProgress)
                        InstallType.CUSTOM -> installCustomPackage(mcpPackage, indicator, onProgress)
                    }
                    
                    if (success) {
                        // 记录安装信息
                        val installation = McpPackageInstallation(
                            packageId = mcpPackage.id,
                            installedVersion = mcpPackage.version,
                            installDate = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                            status = InstallStatus.INSTALLED
                        )
                        state.installedPackages[mcpPackage.id] = installation
                        
                        // 自动添加到 MCP 配置
                        addToMcpConfig(mcpPackage)
                        
                        indicator.fraction = 1.0
                        onProgress?.invoke(100, "Installation completed successfully")
                        
                        ApplicationManager.getApplication().messageBus
                            .syncPublisher(McpPackageInstallListener.TOPIC)
                            .onInstallCompleted(mcpPackage.id, true, "Installation completed successfully")
                    } else {
                        ApplicationManager.getApplication().messageBus
                            .syncPublisher(McpPackageInstallListener.TOPIC)
                            .onInstallCompleted(mcpPackage.id, false, "Installation failed")
                    }
                    
                    future.complete(success)
                } catch (e: Exception) {
                    logger.warn("Failed to install package ${mcpPackage.id}", e)
                    ApplicationManager.getApplication().messageBus
                        .syncPublisher(McpPackageInstallListener.TOPIC)
                        .onInstallCompleted(mcpPackage.id, false, "Installation failed: ${e.message}")
                    future.complete(false)
                }
            }
        })
        
        return future
    }
    
    /**
     * 卸载 MCP 包
     */
    fun uninstallPackage(packageId: String): CompletableFuture<Boolean> {
        val future = CompletableFuture<Boolean>()
        val installation = state.installedPackages[packageId]
        
        if (installation == null) {
            future.complete(false)
            return future
        }
        
        ProgressManager.getInstance().run(object : Task.Backgroundable(project, "Uninstalling package", true) {
            override fun run(indicator: ProgressIndicator) {
                try {
                    indicator.text = "Uninstalling package..."
                    
                    // 从 MCP 配置中移除
                    removeFromMcpConfig(packageId)
                    
                    // 移除安装记录
                    state.installedPackages.remove(packageId)
                    
                    ApplicationManager.getApplication().messageBus
                        .syncPublisher(McpPackageInstallListener.TOPIC)
                        .onUninstallCompleted(packageId, true)
                    
                    future.complete(true)
                } catch (e: Exception) {
                    logger.warn("Failed to uninstall package $packageId", e)
                    ApplicationManager.getApplication().messageBus
                        .syncPublisher(McpPackageInstallListener.TOPIC)
                        .onUninstallCompleted(packageId, false)
                    future.complete(false)
                }
            }
        })
        
        return future
    }
    
    /**
     * 检查包是否已安装
     */
    fun isPackageInstalled(packageId: String): Boolean {
        return state.installedPackages.containsKey(packageId) &&
                state.installedPackages[packageId]?.status == InstallStatus.INSTALLED
    }
    
    /**
     * 获取已安装的包列表
     */
    fun getInstalledPackages(): List<McpPackageInstallation> {
        return state.installedPackages.values.toList()
    }
    
    /**
     * 安装 NPX 包
     */
    private fun installNpxPackage(
        mcpPackage: McpPackage,
        indicator: ProgressIndicator,
        onProgress: ((Int, String) -> Unit)?
    ): Boolean {
        return try {
            indicator.fraction = 0.3
            onProgress?.invoke(30, "Checking Node.js availability...")
            
            // 检查 Node.js 是否可用
            if (!isNodeJsAvailable()) {
                throw Exception("Node.js is not available. Please install Node.js first.")
            }
            
            indicator.fraction = 0.5
            onProgress?.invoke(50, "Installing package via NPX...")
            
            // NPX 包通常不需要实际安装，只需要验证可用性
            val testCommand = GeneralCommandLine("npx", "--yes", mcpPackage.name, "--help")
            val process = ProcessHandlerFactory.getInstance().createProcessHandler(testCommand)
            
            val result = CompletableFuture<Boolean>()
            process.addProcessListener(object : ProcessListener {
                override fun processTerminated(event: ProcessEvent) {
                    result.complete(event.exitCode == 0)
                }
            })
            
            process.startNotify()
            indicator.fraction = 0.8
            onProgress?.invoke(80, "Verifying installation...")
            
            result.get() // 等待结果
        } catch (e: Exception) {
            logger.warn("Failed to install NPX package", e)
            false
        }
    }
    
    /**
     * 安装 NPM 包
     */
    private fun installNpmPackage(
        mcpPackage: McpPackage,
        indicator: ProgressIndicator,
        onProgress: ((Int, String) -> Unit)?
    ): Boolean {
        return try {
            indicator.fraction = 0.3
            onProgress?.invoke(30, "Installing via NPM...")
            
            val command = GeneralCommandLine("npm", "install", "-g", mcpPackage.name)
            val process = ProcessHandlerFactory.getInstance().createProcessHandler(command)
            
            val result = CompletableFuture<Boolean>()
            process.addProcessListener(object : ProcessListener {
                override fun processTerminated(event: ProcessEvent) {
                    result.complete(event.exitCode == 0)
                }
            })
            
            process.startNotify()
            indicator.fraction = 0.8
            onProgress?.invoke(80, "Verifying installation...")
            
            result.get()
        } catch (e: Exception) {
            logger.warn("Failed to install NPM package", e)
            false
        }
    }
    
    /**
     * 安装 Python 包
     */
    private fun installPythonPackage(
        mcpPackage: McpPackage,
        indicator: ProgressIndicator,
        onProgress: ((Int, String) -> Unit)?
    ): Boolean {
        return try {
            indicator.fraction = 0.3
            onProgress?.invoke(30, "Installing via pip...")
            
            val command = GeneralCommandLine("pip", "install", mcpPackage.name)
            val process = ProcessHandlerFactory.getInstance().createProcessHandler(command)
            
            val result = CompletableFuture<Boolean>()
            process.addProcessListener(object : ProcessListener {
                override fun processTerminated(event: ProcessEvent) {
                    result.complete(event.exitCode == 0)
                }
            })
            
            process.startNotify()
            result.get()
        } catch (e: Exception) {
            logger.warn("Failed to install Python package", e)
            false
        }
    }
    
    // 其他安装类型的实现...
    private fun installDockerPackage(mcpPackage: McpPackage, indicator: ProgressIndicator, onProgress: ((Int, String) -> Unit)?): Boolean = true
    private fun installBinaryPackage(mcpPackage: McpPackage, indicator: ProgressIndicator, onProgress: ((Int, String) -> Unit)?): Boolean = true
    private fun installGitPackage(mcpPackage: McpPackage, indicator: ProgressIndicator, onProgress: ((Int, String) -> Unit)?): Boolean = true
    private fun installCustomPackage(mcpPackage: McpPackage, indicator: ProgressIndicator, onProgress: ((Int, String) -> Unit)?): Boolean = true
    
    /**
     * 检查 Node.js 是否可用
     */
    private fun isNodeJsAvailable(): Boolean {
        return try {
            val command = GeneralCommandLine("node", "--version")
            val process = ProcessHandlerFactory.getInstance().createProcessHandler(command)
            process.startNotify()
            process.waitFor(5000) // 等待5秒
            process.exitCode == 0
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 添加到 MCP 配置
     */
    private fun addToMcpConfig(mcpPackage: McpPackage) {
        try {
            val currentConfig = project.customizeSetting.mcpServerConfig

            // 创建服务器配置的 JSON 字符串
            val argsJson = mcpPackage.args.joinToString(",") { "\"$it\"" }
            val envJson = mcpPackage.env.entries.joinToString(",") { "\"${it.key}\":\"${it.value}\"" }

            val serverConfigJson = """
                {
                    "command": "${mcpPackage.installCommand}",
                    "args": [$argsJson],
                    "env": {$envJson},
                    "disabled": false
                }
            """.trimIndent()

            val newConfig = if (currentConfig.isBlank()) {
                """
                {
                    "mcpServers": {
                        "${mcpPackage.name}": $serverConfigJson
                    }
                }
                """.trimIndent()
            } else {
                // 简单的字符串替换方式添加新服务器
                val configWithoutClosing = currentConfig.trimEnd().removeSuffix("}")
                val hasExistingServers = configWithoutClosing.contains("\"mcpServers\"")

                if (hasExistingServers) {
                    val serversEnd = configWithoutClosing.lastIndexOf("}")
                    val beforeServers = configWithoutClosing.substring(0, serversEnd)
                    val afterServers = configWithoutClosing.substring(serversEnd)
                    "$beforeServers,\"${mcpPackage.name}\":$serverConfigJson$afterServers}"
                } else {
                    configWithoutClosing + ",\"mcpServers\":{\"${mcpPackage.name}\":$serverConfigJson}}"
                }
            }

            project.customizeSetting.modify {
                it.mcpServerConfig = newConfig
            }

            // 更新安装记录中的配置名称
            state.installedPackages[mcpPackage.id]?.let { installation ->
                state.installedPackages[mcpPackage.id] = installation.copy(configName = mcpPackage.name)
            }

        } catch (e: Exception) {
            logger.warn("Failed to add package to MCP config", e)
        }
    }
    
    /**
     * 从 MCP 配置中移除
     */
    private fun removeFromMcpConfig(packageId: String) {
        try {
            val installation = state.installedPackages[packageId] ?: return
            val configName = installation.configName ?: return

            val currentConfig = project.customizeSetting.mcpServerConfig
            if (currentConfig.isBlank()) return

            // 使用简单的字符串操作移除服务器配置
            val serverPattern = "\"$configName\"\\s*:\\s*\\{[^}]*\\}".toRegex()
            var newConfig = currentConfig.replace(serverPattern, "")

            // 清理多余的逗号
            newConfig = newConfig.replace(",\\s*,".toRegex(), ",")
            newConfig = newConfig.replace(",\\s*}".toRegex(), "}")
            newConfig = newConfig.replace("\\{\\s*,".toRegex(), "{")

            project.customizeSetting.modify {
                it.mcpServerConfig = newConfig
            }
        } catch (e: Exception) {
            logger.warn("Failed to remove package from MCP config", e)
        }
    }
}
