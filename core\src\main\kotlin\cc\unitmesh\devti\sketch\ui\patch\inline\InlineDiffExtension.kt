package cc.unitmesh.devti.sketch.ui.patch.inline

import cc.unitmesh.devti.AutoDevBundle
import cc.unitmesh.devti.AutoDevIcons
import cc.unitmesh.devti.sketch.ui.patch.SingleFileDiffSketch
import com.intellij.icons.AllIcons
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Disposer
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.patch.TextFilePatch
import com.intellij.util.ui.JBUI
import java.awt.BorderLayout
import java.awt.Dimension
import java.awt.event.ActionEvent
import java.awt.event.ActionListener
import javax.swing.JButton
import javax.swing.JComponent
import javax.swing.JPanel

/**
 * SingleFileDiffSketch的行内差异视图扩展
 * 这是一个外挂独立模块，不修改原有逻辑
 */
class InlineDiffExtension(
    private val project: Project,
    private val currentFile: VirtualFile,
    private val patch: TextFilePatch,
    private val oldCode: String,
    private val newCode: String
) {
    
    private var inlineDiffViewer: InlineDiffViewer? = null
    private var isInlineMode = false
    private var toggleButton: JButton? = null
    
    /**
     * 创建行内差异视图
     */
    fun createInlineDiffViewer(): JComponent {
        if (inlineDiffViewer == null) {
            // 确定文件语言
            val language = currentFile.fileType.language
            
            inlineDiffViewer = InlineDiffViewer.create(
                project = project,
                oldCode = oldCode,
                newCode = newCode,
                language = language,
                fileName = currentFile.name
            )
            
            // 当扩展被销毁时，也销毁查看器
            Disposer.register(inlineDiffViewer!!) {
                inlineDiffViewer = null
            }
        }
        
        return inlineDiffViewer!!.getComponent()
    }
    
    /**
     * 创建切换按钮
     */
    fun createToggleButton(): JButton {
        if (toggleButton == null) {
            toggleButton = JButton().apply {
                icon = AllIcons.Actions.InlayGlobe
                preferredSize = Dimension(24, 24)
                margin = JBUI.emptyInsets()
                isBorderPainted = false
                isContentAreaFilled = false
                isOpaque = false
                toolTipText = "Toggle Inline Diff View"
                
                addActionListener { 
                    toggleDiffMode()
                }
            }
        }
        
        return toggleButton!!
    }
    
    /**
     * 切换差异显示模式
     */
    private fun toggleDiffMode() {
        isInlineMode = !isInlineMode
        updateToggleButtonIcon()
    }
    
    /**
     * 更新切换按钮图标
     */
    private fun updateToggleButtonIcon() {
        toggleButton?.apply {
            icon = if (isInlineMode) {
                AllIcons.Actions.Diff
            } else {
                AllIcons.Actions.InlayGlobe
            }
            
            toolTipText = if (isInlineMode) {
                "Switch to Side-by-Side Diff"
            } else {
                "Switch to Inline Diff"
            }
        }
    }
    
    /**
     * 检查是否处于行内模式
     */
    fun isInlineMode(): Boolean = isInlineMode
    
    /**
     * 设置差异显示模式
     */
    fun setInlineMode(inline: Boolean) {
        if (isInlineMode != inline) {
            isInlineMode = inline
            updateToggleButtonIcon()
        }
    }
    
    /**
     * 获取差异统计信息
     */
    fun getDiffStats(): DiffHighlightRenderer.DiffStats? {
        return inlineDiffViewer?.getDiffStats()
    }
    
    /**
     * 刷新差异显示
     */
    fun refreshDiffs() {
        inlineDiffViewer?.refreshDiffs()
    }
    
    /**
     * 检查是否有差异
     */
    fun hasDifferences(): Boolean {
        return InlineDiffViewer.hasDifferences(oldCode, newCode)
    }
    
    /**
     * 清理资源
     */
    fun dispose() {
        inlineDiffViewer?.let { viewer ->
            Disposer.dispose(viewer)
        }
        inlineDiffViewer = null
        toggleButton = null
    }
    
    companion object {
        /**
         * 为SingleFileDiffSketch创建扩展
         */
        fun createForSketch(
            sketch: SingleFileDiffSketch,
            project: Project,
            currentFile: VirtualFile,
            patch: TextFilePatch,
            oldCode: String,
            newCode: String
        ): InlineDiffExtension {
            return InlineDiffExtension(project, currentFile, patch, oldCode, newCode)
        }
        
        /**
         * 检查是否支持行内差异视图
         */
        fun isSupported(oldCode: String, newCode: String): Boolean {
            // 检查代码长度，避免处理过大的文件
            val maxLines = 1000
            val oldLines = oldCode.lines().size
            val newLines = newCode.lines().size
            
            return oldLines <= maxLines && newLines <= maxLines
        }
    }
}

/**
 * 行内差异视图的动作类
 */
class ToggleInlineDiffAction(
    private val extension: InlineDiffExtension,
    private val onToggle: (Boolean) -> Unit
) : AnAction("Toggle Inline Diff", "Toggle between inline and side-by-side diff view", AllIcons.Actions.InlayGlobe) {
    
    override fun actionPerformed(e: AnActionEvent) {
        val newMode = !extension.isInlineMode()
        extension.setInlineMode(newMode)
        onToggle(newMode)
        
        // 更新动作图标
        templatePresentation.icon = if (newMode) {
            AllIcons.Actions.Diff
        } else {
            AllIcons.Actions.InlayGlobe
        }
    }
    
    override fun update(e: AnActionEvent) {
        super.update(e)
        e.presentation.isEnabled = extension.hasDifferences()
    }
}

/**
 * 差异视图容器，用于在传统视图和行内视图之间切换
 */
class DiffViewContainer(
    private val traditionalView: JComponent,
    private val inlineView: JComponent
) : JPanel(BorderLayout()) {

    private var isShowingInline = false

    init {
        // 默认显示传统视图
        add(traditionalView, BorderLayout.CENTER)
    }

    /**
     * 切换视图
     */
    fun toggleView() {
        removeAll()

        if (isShowingInline) {
            add(traditionalView, BorderLayout.CENTER)
        } else {
            add(inlineView, BorderLayout.CENTER)
        }

        isShowingInline = !isShowingInline
        revalidate()
        repaint()
    }

    /**
     * 设置显示模式
     */
    fun setShowInline(showInline: Boolean) {
        if (isShowingInline != showInline) {
            toggleView()
        }
    }

    /**
     * 检查当前是否显示行内视图
     */
    fun isShowingInline(): Boolean = isShowingInline
}

/**
 * SingleFileDiffSketch的扩展包装器
 * 提供行内差异视图功能而不修改原有类
 */
class EnhancedSingleFileDiffSketch(
    private val originalSketch: SingleFileDiffSketch,
    private val project: Project,
    private val currentFile: VirtualFile,
    private val patch: TextFilePatch,
    private val oldCode: String,
    private val newCode: String
) {

    private val inlineExtension = InlineDiffExtension(project, currentFile, patch, oldCode, newCode)
    private var diffContainer: DiffViewContainer? = null
    private var enhancedComponent: JComponent? = null

    /**
     * 获取增强后的组件
     */
    fun getEnhancedComponent(): JComponent {
        if (enhancedComponent == null) {
            createEnhancedComponent()
        }
        return enhancedComponent!!
    }

    /**
     * 创建增强组件
     */
    private fun createEnhancedComponent() {
        val originalComponent = originalSketch.getComponent()
        val inlineComponent = inlineExtension.createInlineDiffViewer()

        // 创建容器
        diffContainer = DiffViewContainer(originalComponent, inlineComponent)

        // 创建包装面板
        val wrapperPanel = JPanel(BorderLayout())

        // 添加切换按钮到原有的操作面板
        addToggleButtonToOriginalSketch()

        wrapperPanel.add(diffContainer, BorderLayout.CENTER)
        enhancedComponent = wrapperPanel
    }

    /**
     * 向原有sketch添加切换按钮
     */
    private fun addToggleButtonToOriginalSketch() {
        val toggleButton = inlineExtension.createToggleButton()

        // 这里需要访问原有sketch的action panel
        // 由于原有类的限制，我们通过反射或其他方式添加按钮
        // 或者提供一个回调给原有类来添加按钮

        toggleButton.addActionListener {
            diffContainer?.toggleView()
        }
    }

    /**
     * 切换到行内视图
     */
    fun switchToInlineView() {
        diffContainer?.setShowInline(true)
        inlineExtension.setInlineMode(true)
    }

    /**
     * 切换到传统视图
     */
    fun switchToTraditionalView() {
        diffContainer?.setShowInline(false)
        inlineExtension.setInlineMode(false)
    }

    /**
     * 检查是否支持行内视图
     */
    fun isInlineViewSupported(): Boolean {
        return InlineDiffExtension.isSupported(oldCode, newCode)
    }

    /**
     * 获取差异统计
     */
    fun getDiffStats(): DiffHighlightRenderer.DiffStats? {
        return inlineExtension.getDiffStats()
    }

    /**
     * 清理资源
     */
    fun dispose() {
        inlineExtension.dispose()
    }
}
