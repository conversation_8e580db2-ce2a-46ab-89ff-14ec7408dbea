package cc.unitmesh.devti.mcp.marketplace.service

import cc.unitmesh.devti.mcp.marketplace.model.*
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.logger
import com.intellij.openapi.project.Project
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import kotlinx.serialization.decodeFromString
import java.net.URI
import java.net.http.HttpClient
import java.net.http.HttpRequest
import java.net.http.HttpResponse
import java.time.Duration

/**
 * MCP 市场服务 - 负责从市场获取包信息
 */
@Service(Service.Level.PROJECT)
class McpMarketplaceService(private val project: Project) {
    
    companion object {
        private const val DEFAULT_MARKETPLACE_URL = "https://api.mcp-marketplace.dev"
        private const val PACKAGES_ENDPOINT = "/packages"
        private const val PACKAGE_DETAIL_ENDPOINT = "/packages/{id}"
        
        fun getInstance(project: Project): McpMarketplaceService {
            return project.getService(McpMarketplaceService::class.java)
        }
    }
    
    private val httpClient = HttpClient.newBuilder()
        .connectTimeout(Duration.ofSeconds(10))
        .build()
    
    private val json = Json {
        ignoreUnknownKeys = true
        coerceInputValues = true
    }
    
    private val logger = logger<McpMarketplaceService>()
    
    /**
     * 搜索 MCP 包
     */
    suspend fun searchPackages(
        filter: MarketplaceFilter,
        page: Int = 1,
        pageSize: Int = 20
    ): Result<MarketplaceResponse> = withContext(Dispatchers.IO) {
        try {
            val url = buildSearchUrl(filter, page, pageSize)
            val request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .timeout(Duration.ofSeconds(30))
                .header("Accept", "application/json")
                .header("User-Agent", "AutoDev-IDE/1.0")
                .GET()
                .build()
            
            val response = httpClient.send(request, HttpResponse.BodyHandlers.ofString())
            
            if (response.statusCode() == 200) {
                val marketplaceResponse = Json.decodeFromString<MarketplaceResponse>(response.body())
                Result.success(marketplaceResponse)
            } else {
                logger.warn("Failed to search packages: HTTP ${response.statusCode()}")
                Result.failure(Exception("HTTP ${response.statusCode()}: ${response.body()}"))
            }
        } catch (e: Exception) {
            logger.warn("Error searching packages", e)
            // 返回模拟数据作为后备
            Result.success(getMockPackages(filter))
        }
    }
    
    /**
     * 获取包详情
     */
    suspend fun getPackageDetail(packageId: String): Result<McpPackage> = withContext(Dispatchers.IO) {
        try {
            val url = "$DEFAULT_MARKETPLACE_URL$PACKAGE_DETAIL_ENDPOINT".replace("{id}", packageId)
            val request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .timeout(Duration.ofSeconds(30))
                .header("Accept", "application/json")
                .header("User-Agent", "AutoDev-IDE/1.0")
                .GET()
                .build()
            
            val response = httpClient.send(request, HttpResponse.BodyHandlers.ofString())
            
            if (response.statusCode() == 200) {
                val mcpPackage = Json.decodeFromString<McpPackage>(response.body())
                Result.success(mcpPackage)
            } else {
                logger.warn("Failed to get package detail: HTTP ${response.statusCode()}")
                Result.failure(Exception("HTTP ${response.statusCode()}: ${response.body()}"))
            }
        } catch (e: Exception) {
            logger.warn("Error getting package detail", e)
            // 返回模拟数据作为后备
            Result.success(getMockPackageDetail(packageId))
        }
    }
    
    /**
     * 获取热门包
     */
    suspend fun getPopularPackages(limit: Int = 10): Result<List<McpPackage>> = withContext(Dispatchers.IO) {
        try {
            val filter = MarketplaceFilter(sortBy = SortBy.DOWNLOADS)
            val response = searchPackages(filter, 1, limit)
            response.map { it.packages }
        } catch (e: Exception) {
            logger.warn("Error getting popular packages", e)
            Result.success(getMockPopularPackages())
        }
    }
    
    /**
     * 获取官方包
     */
    suspend fun getOfficialPackages(): Result<List<McpPackage>> = withContext(Dispatchers.IO) {
        try {
            val filter = MarketplaceFilter(isOfficial = true, sortBy = SortBy.NAME)
            val response = searchPackages(filter, 1, 50)
            response.map { it.packages }
        } catch (e: Exception) {
            logger.warn("Error getting official packages", e)
            Result.success(getMockOfficialPackages())
        }
    }
    
    private fun buildSearchUrl(filter: MarketplaceFilter, page: Int, pageSize: Int): String {
        val baseUrl = "$DEFAULT_MARKETPLACE_URL$PACKAGES_ENDPOINT"
        val params = mutableListOf<String>()
        
        if (filter.query.isNotBlank()) {
            params.add("q=${filter.query}")
        }
        filter.category?.let { params.add("category=${it.name}") }
        filter.installType?.let { params.add("type=${it.name}") }
        filter.isOfficial?.let { params.add("official=$it") }
        filter.isVerified?.let { params.add("verified=$it") }
        params.add("sort=${filter.sortBy.name}")
        params.add("page=$page")
        params.add("size=$pageSize")
        
        return if (params.isNotEmpty()) {
            "$baseUrl?${params.joinToString("&")}"
        } else {
            baseUrl
        }
    }
    
    // 模拟数据 - 用于开发和测试
    private fun getMockPackages(filter: MarketplaceFilter): MarketplaceResponse {
        val allPackages = getMockAllPackages()
        val filteredPackages = allPackages.filter { pkg ->
            (filter.query.isBlank() || 
             pkg.name.contains(filter.query, ignoreCase = true) ||
             pkg.description.contains(filter.query, ignoreCase = true)) &&
            (filter.category == null || pkg.category == filter.category) &&
            (filter.installType == null || pkg.installType == filter.installType) &&
            (filter.isOfficial == null || pkg.isOfficial == filter.isOfficial) &&
            (filter.isVerified == null || pkg.isVerified == filter.isVerified)
        }
        
        return MarketplaceResponse(
            packages = filteredPackages.take(20),
            total = filteredPackages.size,
            page = 1,
            pageSize = 20,
            hasMore = filteredPackages.size > 20
        )
    }
    
    private fun getMockPackageDetail(packageId: String): McpPackage {
        return getMockAllPackages().find { it.id == packageId } 
            ?: getMockAllPackages().first()
    }
    
    private fun getMockPopularPackages(): List<McpPackage> {
        return getMockAllPackages().sortedByDescending { it.downloads }.take(10)
    }
    
    private fun getMockOfficialPackages(): List<McpPackage> {
        return getMockAllPackages().filter { it.isOfficial }
    }
    
    private fun getMockAllPackages(): List<McpPackage> {
        return listOf(
            McpPackage(
                id = "filesystem",
                name = "@modelcontextprotocol/server-filesystem",
                displayName = "Filesystem Server",
                description = "MCP server for filesystem operations - read, write, and manage files and directories",
                version = "0.5.0",
                author = "Anthropic",
                repository = "https://github.com/modelcontextprotocol/servers",
                license = "MIT",
                keywords = listOf("filesystem", "files", "directories", "io"),
                category = McpCategory.SYSTEM,
                installType = InstallType.NPX,
                installCommand = "npx @modelcontextprotocol/server-filesystem",
                args = listOf("/path/to/allowed/directory"),
                tools = listOf(
                    McpToolInfo("read_file", "Read contents of a file"),
                    McpToolInfo("write_file", "Write contents to a file"),
                    McpToolInfo("list_directory", "List contents of a directory")
                ),
                rating = 4.8,
                downloads = 15420,
                lastUpdated = "2024-01-15",
                isOfficial = true,
                isVerified = true
            ),
            McpPackage(
                id = "git",
                name = "@modelcontextprotocol/server-git",
                displayName = "Git Server",
                description = "MCP server for Git operations - commit, branch, merge, and repository management",
                version = "0.4.2",
                author = "Anthropic",
                repository = "https://github.com/modelcontextprotocol/servers",
                license = "MIT",
                keywords = listOf("git", "version-control", "repository", "commit"),
                category = McpCategory.DEVELOPMENT,
                installType = InstallType.NPX,
                installCommand = "npx @modelcontextprotocol/server-git",
                args = listOf("--repository", "/path/to/git/repo"),
                tools = listOf(
                    McpToolInfo("git_status", "Get repository status"),
                    McpToolInfo("git_commit", "Create a commit"),
                    McpToolInfo("git_branch", "Manage branches")
                ),
                rating = 4.6,
                downloads = 12350,
                lastUpdated = "2024-01-10",
                isOfficial = true,
                isVerified = true
            ),
            McpPackage(
                id = "postgres",
                name = "@modelcontextprotocol/server-postgres",
                displayName = "PostgreSQL Server",
                description = "MCP server for PostgreSQL database operations - query, insert, update, and schema management",
                version = "0.3.1",
                author = "Anthropic",
                repository = "https://github.com/modelcontextprotocol/servers",
                license = "MIT",
                keywords = listOf("postgresql", "database", "sql", "query"),
                category = McpCategory.DATABASE,
                installType = InstallType.NPX,
                installCommand = "npx @modelcontextprotocol/server-postgres",
                env = mapOf("DATABASE_URL" to "postgresql://user:pass@localhost/db"),
                tools = listOf(
                    McpToolInfo("query", "Execute SQL query"),
                    McpToolInfo("list_tables", "List database tables"),
                    McpToolInfo("describe_table", "Get table schema")
                ),
                rating = 4.5,
                downloads = 8920,
                lastUpdated = "2024-01-08",
                isOfficial = true,
                isVerified = true
            )
        )
    }
}
