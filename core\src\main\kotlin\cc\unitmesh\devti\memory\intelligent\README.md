# 智能记忆银行系统

## 🧠 系统概述

智能记忆银行系统是基于认知科学和艾宾浩斯遗忘曲线理论设计的智能记忆管理系统。它模拟人类记忆的形成、强化和遗忘过程，提供自动化的记忆生命周期管理。

## 🎯 核心特性

### 1. 智能记忆处理流程
- **新信息输入** → **工作记忆** → **周期性评估** → **AI智能分析** → **重要性评估**
- **短期记忆** → **强化学习** → **长期记忆** → **Markdown导出**
- **遗忘衰减** → **自动清理**

### 2. 多维度重要性评估
- **周期性评估**: 基于内容长度、关键词、代码特征等
- **AI智能评估**: 集成大模型进行语义分析
- **使用频率评估**: 基于访问次数和时间间隔
- **强化学习**: 根据用户行为动态调整重要性

### 3. 上下文丰富服务
- **智能检索**: 基于关键词和语义相似度
- **相关性计算**: 多维度相关性评分算法
- **大模型集成**: 使用AI增强上下文理解
- **实时建议**: 基于记忆内容提供智能建议

### 4. Markdown文件导出
- **结构化导出**: 按分类组织文件结构
- **元数据支持**: 包含完整的记忆元信息
- **索引生成**: 自动生成导航和统计报告
- **增量导出**: 支持增量更新和版本管理

## 🏗️ 系统架构

### 核心组件

#### 1. MemoryProcessingEngine (记忆处理引擎)
```kotlin
// 添加新记忆
val result = engine.processNewInformation(title, content, source, context)

// 访问记忆
val memory = engine.accessMemory(memoryId)

// 搜索相关记忆
val related = engine.getRelatedMemories(query, limit)
```

#### 2. ContextEnrichmentService (上下文丰富服务)
```kotlin
// 快速上下文丰富
val enriched = service.quickEnrichContext(context)

// 高级上下文丰富
val result = service.enrichContext(request)

// AI增强上下文
val aiEnriched = service.enrichContextWithAI(request)
```

#### 3. MarkdownExportService (Markdown导出服务)
```kotlin
// 导出所有记忆
val result = service.exportAllMemories(config)

// 增量导出
val result = service.incrementalExport(config, lastExportTime)

// 导出单个记忆
val result = service.exportSingleMemory(memoryId, outputPath)
```

#### 4. IntelligentMemoryFacade (统一门面)
```kotlin
// 获取门面实例
val facade = IntelligentMemoryFacade.getInstance(project)

// 简化API
facade.addMemory(title, content)
facade.enrichContext(context)
facade.exportToMarkdown()
```

## 🚀 使用指南

### 1. 基本使用

#### 添加记忆
```kotlin
// 简单添加
val result = facade.addMemory("Kotlin协程", "协程是轻量级线程...")

// 带上下文添加
val result = facade.addMemoryWithContext(
    title = "Bug修复记录",
    content = "修复了内存泄漏问题...",
    source = "bug_report",
    context = mapOf("priority" to "high", "module" to "core")
)
```

#### 上下文丰富
```kotlin
// 快速丰富
val enriched = facade.enrichContext("如何优化数据库查询性能？")

// 高级丰富
val request = ContextEnrichmentRequest(
    currentContext = "需要实现用户认证功能",
    maxMemories = 5,
    relevanceThreshold = 0.3
)
val result = facade.enrichContextAdvanced(request)
```

#### 导出记忆
```kotlin
// 默认导出
val result = facade.exportToMarkdown()

// 自定义导出
val config = MemoryExportConfig(
    groupByCategory = true,
    includeMetadata = true,
    outputPath = "/path/to/export"
)
val result = facade.exportToMarkdown(config)
```

### 2. 异步使用

```kotlin
// 异步添加记忆
facade.addMemoryAsync("异步记忆", "内容") { result ->
    if (result.success) {
        println("记忆添加成功: ${result.memoryId}")
    }
}

// 异步上下文丰富
facade.enrichContextAsync("查询内容") { enrichedContext ->
    println("丰富后的上下文: $enrichedContext")
}

// 异步导出
facade.exportAsync() { result ->
    if (result.success) {
        println("导出完成: ${result.outputDirectory}")
    }
}
```

### 3. 扩展函数使用

```kotlin
// 字符串扩展
val content = "这是要保存的内容"
val result = content.saveAsMemory(project, "自动记忆")

// 上下文丰富扩展
val enriched = content.enrichWithMemories(project)
```

### 4. 工具类使用

```kotlin
// 创建配置
val config = MemoryUtils.createConfig(
    enableAI = true,
    autoExport = true,
    minImportance = 2
)

val exportConfig = MemoryUtils.createExportConfig(
    outputPath = "/custom/path",
    groupByCategory = true
)

val enrichRequest = MemoryUtils.createEnrichmentRequest(
    context = "查询内容",
    maxMemories = 10,
    threshold = 0.5
)
```

## 🎮 Action使用

### 通过IDE Action使用

1. **打开Actions**: `Ctrl+Shift+A` (Windows/Linux) 或 `Cmd+Shift+A` (Mac)
2. **搜索**: "智能记忆处理"
3. **选择操作**:
   - 添加智能记忆
   - 导出记忆到Markdown
   - 测试上下文丰富
   - 查看记忆统计
   - 健康检查

### Action功能详解

#### 1. 添加智能记忆
- 输入标题和内容
- 自动进行重要性评估
- 支持Markdown格式
- 异步处理，不阻塞UI

#### 2. 导出记忆到Markdown
- 支持全量导出和自定义导出
- 按分类组织文件结构
- 生成索引和统计报告
- 可选择是否包含元数据

#### 3. 测试上下文丰富
- 输入查询内容
- 显示相关记忆
- 展示丰富后的上下文
- 提供智能建议

#### 4. 查看记忆统计
- 显示各类型记忆数量
- 平均重要性统计
- 分类分布信息
- 处理量统计

#### 5. 健康检查
- 检查所有服务状态
- 显示系统健康状况
- 诊断潜在问题

## 🔧 配置选项

### MemoryProcessingConfig
```kotlin
data class MemoryProcessingConfig(
    val enableAIEvaluation: Boolean = true,        // 启用AI评估
    val aiEvaluationThreshold: Int = 3,            // AI评估阈值
    val workingMemoryLifetime: Long = 300000L,     // 工作记忆生命周期
    val shortTermMemoryLifetime: Long = 3600000L,  // 短期记忆生命周期
    val forgettingCurveEnabled: Boolean = true,    // 启用遗忘曲线
    val autoExportEnabled: Boolean = true,         // 自动导出
    val reinforcementThreshold: Int = 3,           // 强化学习阈值
    val accessCountThreshold: Long = 3L,           // 访问次数阈值
    val importanceDecayRate: Double = 0.1          // 重要性衰减率
)
```

### MemoryExportConfig
```kotlin
data class MemoryExportConfig(
    val format: ExportFormat = ExportFormat.MARKDOWN,  // 导出格式
    val includeMetadata: Boolean = true,               // 包含元数据
    val groupByCategory: Boolean = true,               // 按分类分组
    val sortBy: MemorySortBy = MemorySortBy.IMPORTANCE, // 排序方式
    val minImportance: Int = 1,                        // 最小重要性
    val outputPath: String? = null                     // 输出路径
)
```

## 📊 数据模型

### 记忆类型
- **WorkingMemory**: 工作记忆，临时存储新输入信息
- **ShortTermMemory**: 短期记忆，经过初步筛选的重要信息
- **LongTermMemory**: 长期记忆，存储在记忆银行中的持久化记忆

### 记忆状态转换
```
新信息 → 工作记忆 → 短期记忆 → 长期记忆
                ↓         ↓
              遗忘处理 → 自动清理
```

## 🔍 集成示例

### 与现有聊天系统集成
```kotlin
class ChatService {
    private val memoryFacade = IntelligentMemoryFacade.getInstance(project)
    
    suspend fun processUserMessage(message: String): String {
        // 1. 丰富上下文
        val enrichedContext = memoryFacade.enrichContext(message)
        
        // 2. 调用大模型
        val response = llm.completion(enrichedContext)
        
        // 3. 保存重要对话
        if (isImportantConversation(message, response)) {
            memoryFacade.addMemoryAsync(
                title = "对话记录 - ${extractTitle(message)}",
                content = "用户: $message\n\n助手: ${response.text}"
            )
        }
        
        return response.text
    }
}
```

### 与代码分析集成
```kotlin
class CodeAnalysisService {
    private val memoryFacade = IntelligentMemoryFacade.getInstance(project)
    
    fun analyzeCode(code: String) {
        // 分析代码
        val analysis = performAnalysis(code)
        
        // 保存分析结果
        if (analysis.hasIssues()) {
            memoryFacade.addMemoryAsync(
                title = "代码分析 - ${analysis.fileName}",
                content = analysis.toMarkdown(),
                source = "code_analysis",
                context = mapOf(
                    "file" to analysis.fileName,
                    "issues" to analysis.issues.size.toString()
                )
            )
        }
    }
}
```

## 🧪 测试

### 运行测试
```bash
./gradlew :core:test --tests "IntelligentMemoryIntegrationTest"
```

### 测试覆盖
- ✅ 完整记忆生命周期测试
- ✅ 上下文丰富功能测试
- ✅ Markdown导出测试
- ✅ 批量处理测试
- ✅ 异步操作测试
- ✅ 健康检查测试
- ✅ 配置管理测试

## 🚨 注意事项

### 性能考虑
- 记忆处理是异步的，不会阻塞主线程
- 大量记忆可能影响搜索性能，建议定期清理
- AI评估会消耗额外资源，可根据需要禁用

### 存储考虑
- 工作记忆和短期记忆存储在内存中
- 长期记忆持久化到磁盘
- 导出的Markdown文件需要足够的磁盘空间

### 隐私考虑
- 记忆内容可能包含敏感信息
- 导出文件应妥善保管
- AI评估可能将数据发送到外部服务

## 🔮 未来规划

### 短期目标
- [ ] 支持图片和附件记忆
- [ ] 增强AI评估算法
- [ ] 添加记忆标签自动生成
- [ ] 支持记忆合并和拆分

### 长期目标
- [ ] 分布式记忆存储
- [ ] 团队记忆共享
- [ ] 个性化遗忘曲线
- [ ] 多模态记忆支持

## 📞 支持

如有问题或建议，请：
1. 查看测试用例了解使用方法
2. 运行健康检查诊断问题
3. 查看日志获取详细错误信息
4. 提交Issue或Pull Request

---

*智能记忆银行系统 - 让知识管理更智能，让记忆永不丢失！*
