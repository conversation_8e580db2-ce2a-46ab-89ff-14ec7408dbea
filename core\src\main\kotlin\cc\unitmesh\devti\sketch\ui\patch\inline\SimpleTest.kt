package cc.unitmesh.devti.sketch.ui.patch.inline

/**
 * 简化的测试类，用于验证基本语法
 */
object SimpleTest {
    
    fun testDiffCalculator() {
        val oldCode = "line1\nline2"
        val newCode = "line1\nline3"
        
        val lineDiffs = DiffCalculator.calculateLineDiffs(oldCode, newCode)
        println("Line diffs calculated: ${lineDiffs.size}")
        
        val charDiffs = DiffCalculator.calculateCharDiffs("old", "new")
        println("Char diffs calculated: ${charDiffs.size}")
    }
    
    @JvmStatic
    fun main(args: Array<String>) {
        println("Starting simple test...")
        testDiffCalculator()
        println("Simple test completed!")
    }
}
