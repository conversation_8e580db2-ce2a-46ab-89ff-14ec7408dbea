package cc.unitmesh.devti.sketch.ui.patch.inline

/**
 * 简单的测试类，用于验证行内差异视图的功能
 */
object InlineDiffTest {
    
    /**
     * 测试差异计算功能
     */
    fun testDiffCalculation() {
        val oldCode = """
            class TestClass {
                private String name;
                
                public String getName() {
                    return name;
                }
                
                public void setName(String name) {
                    this.name = name;
                }
            }
        """.trimIndent()
        
        val newCode = """
            class TestClass {
                private String name;
                private int age;
                
                public String getName() {
                    return this.name;
                }
                
                public void setName(String name) {
                    this.name = name;
                }
                
                public int getAge() {
                    return age;
                }
                
                public void setAge(int age) {
                    this.age = age;
                }
            }
        """.trimIndent()
        
        // 计算行级差异
        val lineDiffs = DiffCalculator.calculateLineDiffs(oldCode, newCode)
        
        println("=== 行级差异分析 ===")
        lineDiffs.forEach { diff ->
            when (diff) {
                is DiffCalculator.LineDiff.Added -> 
                    println("+ 添加行 ${diff.lineNumber}: ${diff.line}")
                is DiffCalculator.LineDiff.Deleted -> 
                    println("- 删除行 ${diff.lineNumber}: ${diff.line}")
                is DiffCalculator.LineDiff.Modified -> 
                    println("~ 修改行 ${diff.oldLineNumber}->${diff.newLineNumber}: ${diff.oldLine} -> ${diff.newLine}")
                is DiffCalculator.LineDiff.Unchanged -> 
                    println("  未变行 ${diff.lineNumber}: ${diff.line}")
            }
        }
        
        // 测试字符级差异
        val oldLine = "return name;"
        val newLine = "return this.name;"
        val charDiffs = DiffCalculator.calculateCharDiffs(oldLine, newLine)
        
        println("\n=== 字符级差异分析 ===")
        println("原行: $oldLine")
        println("新行: $newLine")
        charDiffs.forEach { diff ->
            when (diff) {
                is DiffCalculator.CharDiff.Added -> 
                    println("+ 添加字符 [${diff.startIndex}-${diff.endIndex}]: '${diff.text}'")
                is DiffCalculator.CharDiff.Deleted -> 
                    println("- 删除字符 [${diff.startIndex}-${diff.endIndex}]: '${diff.text}'")
                is DiffCalculator.CharDiff.Unchanged -> 
                    println("  未变字符 [${diff.startIndex}-${diff.endIndex}]: '${diff.text}'")
            }
        }
    }
    
    /**
     * 测试差异统计功能
     */
    fun testDiffStats() {
        val oldCode = """
            function hello() {
                console.log("Hello");
            }
        """.trimIndent()
        
        val newCode = """
            function hello(name) {
                console.log("Hello " + name);
                console.log("Welcome!");
            }
        """.trimIndent()
        
        val lineDiffs = DiffCalculator.calculateLineDiffs(oldCode, newCode)
        
        var addedLines = 0
        var deletedLines = 0
        var modifiedLines = 0
        
        for (lineDiff in lineDiffs) {
            when (lineDiff) {
                is DiffCalculator.LineDiff.Added -> addedLines++
                is DiffCalculator.LineDiff.Deleted -> deletedLines++
                is DiffCalculator.LineDiff.Modified -> modifiedLines++
                is DiffCalculator.LineDiff.Unchanged -> { /* 不计算 */ }
            }
        }
        
        println("=== 差异统计 ===")
        println("添加行数: $addedLines")
        println("删除行数: $deletedLines")
        println("修改行数: $modifiedLines")
        println("总变更数: ${addedLines + deletedLines + modifiedLines}")
    }
    
    /**
     * 测试支持性检查
     */
    fun testSupportCheck() {
        val smallCode = "function test() { return 'hello'; }"
        val largeCode = (1..2000).joinToString("\n") { "line $it" }
        
        println("=== 支持性检查 ===")
        println("小文件支持: ${InlineDiffExtension.isSupported(smallCode, smallCode)}")
        println("大文件支持: ${InlineDiffExtension.isSupported(largeCode, largeCode)}")
    }
    
    /**
     * 运行所有测试
     */
    @JvmStatic
    fun main(args: Array<String>) {
        println("开始测试行内差异视图功能...\n")
        
        try {
            testDiffCalculation()
            println("\n" + "=".repeat(50) + "\n")
            
            testDiffStats()
            println("\n" + "=".repeat(50) + "\n")
            
            testSupportCheck()
            
            println("\n✅ 所有测试完成！")
        } catch (e: Exception) {
            println("\n❌ 测试失败: ${e.message}")
            e.printStackTrace()
        }
    }
}
