package cc.unitmesh.devti.mcp.marketplace.service

import cc.unitmesh.devti.mcp.marketplace.model.*
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.logger
import com.intellij.openapi.project.Project
import java.util.concurrent.CompletableFuture
import java.util.concurrent.Executors

/**
 * 简化的 MCP 市场服务 - 避免协程问题，使用 CompletableFuture
 */
@Service(Service.Level.PROJECT)
class SimpleMcpMarketplaceService(private val project: Project) {
    
    companion object {
        fun getInstance(project: Project): SimpleMcpMarketplaceService {
            return project.getService(SimpleMcpMarketplaceService::class.java)
        }
    }
    
    private val executor = Executors.newCachedThreadPool()
    private val logger = logger<SimpleMcpMarketplaceService>()
    
    /**
     * 搜索 MCP 包 - 使用 CompletableFuture 替代协程
     */
    fun searchPackages(
        filter: MarketplaceFilter,
        page: Int = 1,
        pageSize: Int = 20
    ): CompletableFuture<Result<MarketplaceResponse>> {
        return CompletableFuture.supplyAsync({
            try {
                // 使用模拟数据，避免网络请求的复杂性
                val mockPackages = getMockPackages(filter)
                Result.success(mockPackages)
            } catch (e: Exception) {
                logger.warn("Error searching packages", e)
                Result.failure(e)
            }
        }, executor)
    }
    
    /**
     * 获取包详情
     */
    fun getPackageDetail(packageId: String): CompletableFuture<Result<McpPackage>> {
        return CompletableFuture.supplyAsync({
            try {
                val mockPackage = getMockPackageDetail(packageId)
                Result.success(mockPackage)
            } catch (e: Exception) {
                logger.warn("Error getting package detail", e)
                Result.failure(e)
            }
        }, executor)
    }
    
    /**
     * 获取热门包
     */
    fun getPopularPackages(limit: Int = 10): CompletableFuture<Result<List<McpPackage>>> {
        return CompletableFuture.supplyAsync({
            try {
                val popularPackages = getMockPopularPackages().take(limit)
                Result.success(popularPackages)
            } catch (e: Exception) {
                logger.warn("Error getting popular packages", e)
                Result.failure(e)
            }
        }, executor)
    }
    
    /**
     * 获取官方包
     */
    fun getOfficialPackages(): CompletableFuture<Result<List<McpPackage>>> {
        return CompletableFuture.supplyAsync({
            try {
                val officialPackages = getMockOfficialPackages()
                Result.success(officialPackages)
            } catch (e: Exception) {
                logger.warn("Error getting official packages", e)
                Result.failure(e)
            }
        }, executor)
    }
    
    // 模拟数据方法
    private fun getMockPackages(filter: MarketplaceFilter): MarketplaceResponse {
        val allPackages = getMockAllPackages()
        val filteredPackages = allPackages.filter { pkg ->
            (filter.query.isBlank() || 
             pkg.name.contains(filter.query, ignoreCase = true) ||
             pkg.description.contains(filter.query, ignoreCase = true)) &&
            (filter.category == null || pkg.category == filter.category) &&
            (filter.installType == null || pkg.installType == filter.installType) &&
            (filter.isOfficial == null || pkg.isOfficial == filter.isOfficial) &&
            (filter.isVerified == null || pkg.isVerified == filter.isVerified)
        }
        
        return MarketplaceResponse(
            packages = filteredPackages.take(20),
            total = filteredPackages.size,
            page = 1,
            pageSize = 20,
            hasMore = filteredPackages.size > 20
        )
    }
    
    private fun getMockPackageDetail(packageId: String): McpPackage {
        return getMockAllPackages().find { it.id == packageId } 
            ?: getMockAllPackages().first()
    }
    
    private fun getMockPopularPackages(): List<McpPackage> {
        return getMockAllPackages().sortedByDescending { it.downloads }
    }
    
    private fun getMockOfficialPackages(): List<McpPackage> {
        return getMockAllPackages().filter { it.isOfficial }
    }
    
    private fun getMockAllPackages(): List<McpPackage> {
        return listOf(
            McpPackage(
                id = "filesystem",
                name = "@modelcontextprotocol/server-filesystem",
                displayName = "Filesystem Server",
                description = "MCP server for filesystem operations - read, write, and manage files and directories",
                version = "0.5.0",
                author = "Anthropic",
                repository = "https://github.com/modelcontextprotocol/servers",
                license = "MIT",
                keywords = listOf("filesystem", "files", "directories", "io"),
                category = McpCategory.SYSTEM,
                installType = InstallType.NPX,
                installCommand = "npx @modelcontextprotocol/server-filesystem",
                args = listOf("/path/to/allowed/directory"),
                tools = listOf(
                    McpToolInfo("read_file", "Read contents of a file"),
                    McpToolInfo("write_file", "Write contents to a file"),
                    McpToolInfo("list_directory", "List contents of a directory")
                ),
                rating = 4.8,
                downloads = 15420,
                lastUpdated = "2024-01-15",
                isOfficial = true,
                isVerified = true
            ),
            McpPackage(
                id = "git",
                name = "@modelcontextprotocol/server-git",
                displayName = "Git Server",
                description = "MCP server for Git operations - commit, branch, merge, and repository management",
                version = "0.4.2",
                author = "Anthropic",
                repository = "https://github.com/modelcontextprotocol/servers",
                license = "MIT",
                keywords = listOf("git", "version-control", "repository", "commit"),
                category = McpCategory.DEVELOPMENT,
                installType = InstallType.NPX,
                installCommand = "npx @modelcontextprotocol/server-git",
                args = listOf("--repository", "/path/to/git/repo"),
                tools = listOf(
                    McpToolInfo("git_status", "Get repository status"),
                    McpToolInfo("git_commit", "Create a commit"),
                    McpToolInfo("git_branch", "Manage branches")
                ),
                rating = 4.6,
                downloads = 12350,
                lastUpdated = "2024-01-10",
                isOfficial = true,
                isVerified = true
            ),
            McpPackage(
                id = "postgres",
                name = "@modelcontextprotocol/server-postgres",
                displayName = "PostgreSQL Server",
                description = "MCP server for PostgreSQL database operations - query, insert, update, and schema management",
                version = "0.3.1",
                author = "Anthropic",
                repository = "https://github.com/modelcontextprotocol/servers",
                license = "MIT",
                keywords = listOf("postgresql", "database", "sql", "query"),
                category = McpCategory.DATABASE,
                installType = InstallType.NPX,
                installCommand = "npx @modelcontextprotocol/server-postgres",
                env = mapOf("DATABASE_URL" to "postgresql://user:pass@localhost/db"),
                tools = listOf(
                    McpToolInfo("query", "Execute SQL query"),
                    McpToolInfo("list_tables", "List database tables"),
                    McpToolInfo("describe_table", "Get table schema")
                ),
                rating = 4.5,
                downloads = 8920,
                lastUpdated = "2024-01-08",
                isOfficial = true,
                isVerified = true
            ),
            McpPackage(
                id = "web-search",
                name = "@modelcontextprotocol/server-web-search",
                displayName = "Web Search Server",
                description = "MCP server for web search operations using various search engines",
                version = "0.2.1",
                author = "Community",
                repository = "https://github.com/community/mcp-web-search",
                license = "Apache-2.0",
                keywords = listOf("web", "search", "google", "bing"),
                category = McpCategory.WEB,
                installType = InstallType.NPX,
                installCommand = "npx @modelcontextprotocol/server-web-search",
                env = mapOf("SEARCH_API_KEY" to "your-api-key"),
                tools = listOf(
                    McpToolInfo("web_search", "Search the web"),
                    McpToolInfo("get_page_content", "Get page content")
                ),
                rating = 4.2,
                downloads = 5670,
                lastUpdated = "2024-01-05",
                isOfficial = false,
                isVerified = true
            )
        )
    }
}
