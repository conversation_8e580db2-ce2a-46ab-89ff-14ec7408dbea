package cc.unitmesh.devti.gui.toolbar

import cc.unitmesh.devti.gui.memory.MemoryBankDialog
import cc.unitmesh.devti.memory.MemoryBankService
import com.intellij.icons.AllIcons
import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent

/**
 * 测试简化记忆银行 Action
 */
class TestSimpleMemoryBankAction : AnAction(
    "测试记忆银行",
    "测试简化的记忆银行 Markdown 渲染功能",
    AllIcons.Actions.Preview
) {

    override fun getActionUpdateThread(): ActionUpdateThread = ActionUpdateThread.BGT

    override fun update(e: AnActionEvent) {
        val project = e.project
        e.presentation.isEnabled = project != null
    }

    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return
        
        // 添加一些测试数据
        val memoryBankService = MemoryBankService.getInstance(project)
        
        // 清空现有数据并添加测试数据
        memoryBankService.clearAllMemories()
        
        // 添加测试记忆
        memoryBankService.addMemory(
            title = "Kotlin 协程测试",
            content = """
                # Kotlin 协程基础
                
                ## 什么是协程？
                协程是一种**轻量级线程**，可以在不阻塞线程的情况下暂停和恢复执行。
                
                ## 基本用法
                ```kotlin
                import kotlinx.coroutines.*
                
                fun main() = runBlocking {
                    launch {
                        delay(1000L)
                        println("World!")
                    }
                    println("Hello,")
                }
                ```
                
                ## 关键概念
                - `suspend` 函数：可以暂停的函数
                - `CoroutineScope`：协程作用域
                - `Job`：协程的生命周期管理
                
                > **注意**：协程需要在协程作用域中启动
            """.trimIndent(),
            category = "code",
            tags = listOf("kotlin", "协程", "并发"),
            importance = 4
        )
        
        memoryBankService.addMemory(
            title = "表格测试",
            content = """
                # 表格示例
                
                ## 功能对比表
                
                | 功能 | 原版本 | 新版本 |
                |------|--------|--------|
                | Markdown 渲染 | ❌ | ✅ |
                | 代码高亮 | ❌ | ✅ |
                | 表格支持 | ❌ | ✅ |
                | 折叠展开 | ✅ | ✅ |
                
                ## 测试结果
                - [x] 基本 Markdown 渲染
                - [x] 代码块显示
                - [x] 表格显示
                - [ ] 图片显示（待实现）
            """.trimIndent(),
            category = "test",
            tags = listOf("表格", "测试", "功能"),
            importance = 3
        )
        
        memoryBankService.addMemory(
            title = "代码示例集合",
            content = """
                # 多语言代码示例
                
                ## Python
                ```python
                def fibonacci(n):
                    if n <= 1:
                        return n
                    return fibonacci(n-1) + fibonacci(n-2)
                
                print(fibonacci(10))
                ```
                
                ## JavaScript
                ```javascript
                const fibonacci = (n) => {
                    if (n <= 1) return n;
                    return fibonacci(n-1) + fibonacci(n-2);
                };
                
                console.log(fibonacci(10));
                ```
                
                ## Java
                ```java
                public class Fibonacci {
                    public static int fibonacci(int n) {
                        if (n <= 1) return n;
                        return fibonacci(n-1) + fibonacci(n-2);
                    }
                    
                    public static void main(String[] args) {
                        System.out.println(fibonacci(10));
                    }
                }
                ```
                
                > 这些都是计算斐波那契数列的不同实现
            """.trimIndent(),
            category = "code",
            tags = listOf("算法", "多语言", "示例"),
            importance = 5
        )
        
        // 显示记忆银行对话框
        val dialog = MemoryBankDialog(project)
        dialog.show()
    }
}
