package cc.unitmesh.devti.gui.memory

import cc.unitmesh.devti.memory.MemoryBankService
import cc.unitmesh.devti.memory.MemorySummary
import cc.unitmesh.devti.sketch.ui.MarkdownPreviewHighlightSketch
import com.intellij.icons.AllIcons
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.components.*
import com.intellij.util.ui.JBUI
import com.intellij.util.ui.UIUtil
import java.awt.*
import java.awt.event.ActionEvent
import java.util.Hashtable
import javax.swing.*

/**
 * 添加记忆对话框
 */
class AddMemoryDialog(
    private val project: Project,
    private val onSave: (String, String, String, List<String>, Int) -> Unit
) : DialogWrapper(project, true) {
    
    private val memoryBankService = MemoryBankService.getInstance(project)
    private val titleField = JBTextField()
    private val contentArea = JBTextArea(8, 40)
    private val categoryCombo = JComboBox<String>()
    private val tagsField = JBTextField()
    private val importanceSlider = JSlider(1, 5, 1)
    private var markdownPreview: MarkdownPreviewHighlightSketch? = null
    private var isPreviewMode = false
    
    init {
        title = "添加记忆摘要"
        init()
        setupUI()
    }
    
    override fun createCenterPanel(): JComponent {
        val panel = JPanel(GridBagLayout())
        val gbc = GridBagConstraints()
        gbc.insets = JBUI.insets(5)
        gbc.anchor = GridBagConstraints.WEST
        
        // 标题
        gbc.gridx = 0; gbc.gridy = 0
        panel.add(JLabel("标题:"), gbc)
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0
        panel.add(titleField, gbc)
        
        // 分类
        gbc.gridx = 0; gbc.gridy = 1; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0.0
        panel.add(JLabel("分类:"), gbc)
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0
        panel.add(categoryCombo, gbc)
        
        // 重要性
        gbc.gridx = 0; gbc.gridy = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0.0
        panel.add(JLabel("重要性:"), gbc)
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0
        panel.add(createImportancePanel(), gbc)
        
        // 标签
        gbc.gridx = 0; gbc.gridy = 3; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0.0
        panel.add(JLabel("标签:"), gbc)
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0
        panel.add(tagsField, gbc)
        
        // 内容
        gbc.gridx = 0; gbc.gridy = 4; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0.0
        panel.add(JLabel("内容:"), gbc)
        gbc.gridx = 1; gbc.fill = GridBagConstraints.BOTH; gbc.weightx = 1.0; gbc.weighty = 1.0
        panel.add(createContentPanel(), gbc)
        
        return panel
    }
    
    override fun createActions(): Array<Action> {
        return arrayOf(
            object : AbstractAction("保存") {
                override fun actionPerformed(e: ActionEvent) {
                    if (validateInput()) {
                        saveMemory()
                        close(OK_EXIT_CODE)
                    }
                }
            },
            cancelAction
        )
    }
    
    private fun createContentPanel(): JComponent {
        val panel = JPanel(BorderLayout())

        // 顶部工具栏
        val toolbar = JPanel(FlowLayout(FlowLayout.LEFT))
        val previewButton = JButton("预览", AllIcons.Actions.Preview).apply {
            addActionListener { togglePreview() }
        }
        val editButton = JButton("编辑", AllIcons.Actions.Edit).apply {
            addActionListener { showEditMode() }
        }

        toolbar.add(JLabel("Markdown 内容:"))
        toolbar.add(Box.createHorizontalStrut(10))
        toolbar.add(editButton)
        toolbar.add(previewButton)

        panel.add(toolbar, BorderLayout.NORTH)

        // 内容区域（默认显示编辑模式）
        val contentScrollPane = JBScrollPane(contentArea)
        panel.add(contentScrollPane, BorderLayout.CENTER)

        return panel
    }

    private fun togglePreview() {
        val contentPanel = (createCenterPanel() as JPanel)
            .components.find { it is JPanel }?.let { it as JPanel }
            ?.components?.get(4) as? JPanel ?: return

        if (isPreviewMode) {
            showEditMode()
        } else {
            showPreviewMode()
        }
    }

    private fun showEditMode() {
        isPreviewMode = false
        val contentPanel = (createCenterPanel() as JPanel)
            .components.find { it is JPanel }?.let { it as JPanel }
            ?.components?.get(4) as? JPanel ?: return

        contentPanel.removeAll()
        contentPanel.add(createContentPanel(), BorderLayout.CENTER)
        contentPanel.revalidate()
        contentPanel.repaint()
    }

    private fun showPreviewMode() {
        isPreviewMode = true
        val content = contentArea.text
        if (content.isBlank()) {
            JOptionPane.showMessageDialog(this.contentPane, "请先输入内容", "提示", JOptionPane.INFORMATION_MESSAGE)
            return
        }

        markdownPreview = MarkdownPreviewHighlightSketch(project, content)

        val contentPanel = (createCenterPanel() as JPanel)
            .components.find { it is JPanel }?.let { it as JPanel }
            ?.components?.get(4) as? JPanel ?: return

        contentPanel.removeAll()

        val previewPanel = JPanel(BorderLayout())
        val toolbar = JPanel(FlowLayout(FlowLayout.LEFT))
        toolbar.add(JLabel("Markdown 预览:"))
        toolbar.add(Box.createHorizontalStrut(10))
        toolbar.add(JButton("编辑", AllIcons.Actions.Edit).apply {
            addActionListener { showEditMode() }
        })

        previewPanel.add(toolbar, BorderLayout.NORTH)
        previewPanel.add(JBScrollPane(markdownPreview?.getComponent()), BorderLayout.CENTER)

        contentPanel.add(previewPanel, BorderLayout.CENTER)
        contentPanel.revalidate()
        contentPanel.repaint()
    }

    private fun setupUI() {
        // 设置分类下拉框
        categoryCombo.isEditable = true
        categoryCombo.addItem("general")
        memoryBankService.getAllCategories().forEach { category ->
            if (category != "general") {
                categoryCombo.addItem(category)
            }
        }

        // 设置标签提示
        tagsField.emptyText.text = "用逗号分隔多个标签"

        // 设置内容区域
        contentArea.lineWrap = true
        contentArea.wrapStyleWord = true
        contentArea.emptyText.text = "支持 Markdown 格式，如：**粗体**、*斜体*、`代码`、```代码块```等"
    }
    
    private fun createImportancePanel(): JComponent {
        val panel = JPanel(FlowLayout(FlowLayout.LEFT, 0, 0))
        
        importanceSlider.majorTickSpacing = 1
        importanceSlider.paintTicks = true
        importanceSlider.paintLabels = true
        
        val labels = Hashtable<Int, JLabel>()
        for (i in 1..5) {
            labels[i] = JLabel("★".repeat(i))
        }
        importanceSlider.labelTable = labels
        
        panel.add(importanceSlider)
        return panel
    }
    
    private fun validateInput(): Boolean {
        if (titleField.text.isBlank()) {
            JOptionPane.showMessageDialog(this.contentPane, "请输入标题", "验证错误", JOptionPane.ERROR_MESSAGE)
            return false
        }
        
        if (contentArea.text.isBlank()) {
            JOptionPane.showMessageDialog(this.contentPane, "请输入内容", "验证错误", JOptionPane.ERROR_MESSAGE)
            return false
        }
        
        return true
    }
    
    private fun saveMemory() {
        val title = titleField.text.trim()
        val content = contentArea.text.trim()
        val category = categoryCombo.selectedItem.toString().trim()
        val tags = tagsField.text.split(",").map { it.trim() }.filter { it.isNotEmpty() }
        val importance = importanceSlider.value
        
        onSave(title, content, category, tags, importance)
    }
}

