package cc.unitmesh.devti.sketch.ui.patch.inline

import cc.unitmesh.devti.sketch.ui.patch.SingleFileDiffSketch
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.patch.TextFilePatch
import java.awt.BorderLayout
import javax.swing.JButton
import javax.swing.JPanel

/**
 * 行内差异视图使用示例
 * 
 * 这个文件展示了如何将行内差异视图集成到现有的SingleFileDiffSketch中
 * 而不需要修改原有的代码逻辑
 */
class InlineDiffUsageExample {
    
    /**
     * 示例1: 增强现有的SingleFileDiffSketch
     */
    fun enhanceExistingSketch(
        project: Project,
        currentFile: VirtualFile,
        patch: TextFilePatch,
        oldCode: String,
        newCode: String,
        viewDiffAction: () -> Unit
    ): JPanel {
        // 创建原有的SingleFileDiffSketch
        val originalSketch = SingleFileDiffSketch(project, currentFile, patch, viewDiffAction)
        
        // 使用扩展方法增强
        val enhancedSketch = originalSketch.withInlineDiffSupport(
            project, currentFile, patch, oldCode, newCode
        )
        
        // 创建包含切换按钮的面板
        val panel = JPanel(BorderLayout())
        
        // 添加增强后的组件
        panel.add(enhancedSketch.getEnhancedComponent(), BorderLayout.CENTER)
        
        // 可选：添加工具栏
        val toolbar = createToolbar(enhancedSketch)
        panel.add(toolbar, BorderLayout.NORTH)
        
        return panel
    }
    
    /**
     * 示例2: 创建独立的行内差异视图
     */
    fun createStandaloneInlineViewer(
        project: Project,
        oldCode: String,
        newCode: String,
        language: com.intellij.lang.Language? = null,
        fileName: String? = null
    ): JPanel {
        val panel = JPanel(BorderLayout())
        
        // 检查是否支持行内视图
        if (!InlineDiffIntegration.isSupported(oldCode, newCode)) {
            // 如果不支持，可以显示错误信息或回退到其他视图
            panel.add(javax.swing.JLabel("File too large for inline diff view"), BorderLayout.CENTER)
            return panel
        }
        
        // 创建行内差异视图
        val inlineViewer = InlineDiffIntegration.createStandaloneViewer(
            project, oldCode, newCode, language, fileName
        )
        
        panel.add(inlineViewer, BorderLayout.CENTER)
        
        // 添加统计信息
        val stats = InlineDiffIntegration.calculateDiffStats(oldCode, newCode)
        val statsLabel = javax.swing.JLabel(
            "Changes: +${stats.addedLines} -${stats.deletedLines} ~${stats.modifiedLines}"
        )
        panel.add(statsLabel, BorderLayout.SOUTH)
        
        return panel
    }
    
    /**
     * 示例3: 在现有UI中添加切换功能
     */
    fun addToggleFunctionToExistingUI(
        existingPanel: JPanel,
        traditionalDiffComponent: javax.swing.JComponent,
        project: Project,
        oldCode: String,
        newCode: String
    ) {
        // 创建行内差异视图
        val inlineViewer = InlineDiffIntegration.createStandaloneViewer(
            project, oldCode, newCode
        )
        
        // 创建容器来切换视图
        val container = DiffViewContainer(traditionalDiffComponent, inlineViewer)
        
        // 创建切换按钮
        val toggleButton = InlineDiffIntegration.createToggleButton { isInlineMode ->
            container.setShowInline(isInlineMode)
        }
        
        // 将容器和按钮添加到现有面板
        existingPanel.removeAll()
        existingPanel.add(container, BorderLayout.CENTER)
        
        // 添加按钮到工具栏（假设存在）
        val toolbar = JPanel()
        toolbar.add(toggleButton)
        existingPanel.add(toolbar, BorderLayout.NORTH)
        
        existingPanel.revalidate()
        existingPanel.repaint()
    }
    
    /**
     * 创建工具栏
     */
    private fun createToolbar(enhancedSketch: EnhancedSingleFileDiffSketch): JPanel {
        val toolbar = JPanel()
        
        // 添加切换按钮
        val toggleButton = InlineDiffIntegration.createToggleButton { isInlineMode ->
            if (isInlineMode) {
                enhancedSketch.switchToInlineView()
            } else {
                enhancedSketch.switchToTraditionalView()
            }
        }
        toolbar.add(toggleButton)
        
        // 添加统计信息按钮
        val statsButton = JButton("Stats").apply {
            addActionListener {
                val stats = enhancedSketch.getDiffStats()
                if (stats != null) {
                    javax.swing.JOptionPane.showMessageDialog(
                        this,
                        "Changes: +${stats.addedLines} -${stats.deletedLines} ~${stats.modifiedLines}",
                        "Diff Statistics",
                        javax.swing.JOptionPane.INFORMATION_MESSAGE
                    )
                }
            }
        }
        toolbar.add(statsButton)
        
        return toolbar
    }
}

/**
 * 集成到现有SingleFileDiffSketch的最简单方式
 * 
 * 只需要在创建SingleFileDiffSketch的地方做如下修改：
 * 
 * 原来的代码：
 * ```kotlin
 * val diffSketch = SingleFileDiffSketch(project, file, patch, viewDiffAction)
 * panel.add(diffSketch.getComponent())
 * ```
 * 
 * 修改后的代码：
 * ```kotlin
 * val diffSketch = SingleFileDiffSketch(project, file, patch, viewDiffAction)
 * val enhancedSketch = diffSketch.withInlineDiffSupport(project, file, patch, oldCode, newCode)
 * panel.add(enhancedSketch.getEnhancedComponent())
 * ```
 */
object SimpleIntegrationExample {
    
    fun integrateIntoExistingCode(
        project: Project,
        currentFile: VirtualFile,
        patch: TextFilePatch,
        oldCode: String,
        newCode: String,
        viewDiffAction: () -> Unit,
        parentPanel: JPanel
    ) {
        // 原有的创建方式
        val originalSketch = SingleFileDiffSketch(project, currentFile, patch, viewDiffAction)
        
        // 检查是否支持行内视图
        if (InlineDiffIntegration.isSupported(oldCode, newCode)) {
            // 如果支持，使用增强版本
            val enhancedSketch = originalSketch.withInlineDiffSupport(
                project, currentFile, patch, oldCode, newCode
            )
            
            // 添加到父面板
            parentPanel.add(enhancedSketch.getEnhancedComponent())
            
            // 可选：添加切换按钮到现有的操作面板
            // 这需要根据具体的UI结构来实现
            
        } else {
            // 如果不支持，使用原有版本
            parentPanel.add(originalSketch.getComponent())
        }
    }
}

/**
 * 配置示例
 */
object ConfigurationExample {
    
    fun createWithCustomConfig(): InlineDiffViewerFactory {
        val config = InlineDiffConfig(
            maxLinesForInlineView = 500,  // 限制最大行数
            showLineNumbers = true,       // 显示行号
            showDiffStats = true,         // 显示统计信息
            enableCharLevelDiff = true,   // 启用字符级差异
            defaultToInlineView = false   // 默认不使用行内视图
        )
        
        return InlineDiffViewerFactory(config)
    }
}
