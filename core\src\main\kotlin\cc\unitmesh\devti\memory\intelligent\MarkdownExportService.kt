package cc.unitmesh.devti.memory.intelligent

import cc.unitmesh.devti.memory.MemoryBankService
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.logger
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFileManager
import kotlinx.coroutines.*
import java.io.File
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 * Markdown 导出服务 - 将记忆导出为 Markdown 文件
 * 
 * 功能：
 * 1. 导出长期记忆为 Markdown 文件
 * 2. 按分类组织文件结构
 * 3. 生成索引和目录
 * 4. 支持增量导出
 */
@Service(Service.Level.PROJECT)
class MarkdownExportService(private val project: Project) {
    
    companion object {
        fun getInstance(project: Project): MarkdownExportService {
            return project.getService(MarkdownExportService::class.java)
        }
    }
    
    private val logger = logger<MarkdownExportService>()
    private val memoryBankService = MemoryBankService.getInstance(project)
    private val memoryEngine = MemoryProcessingEngine.getInstance(project)
    private val exportScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    /**
     * 导出所有长期记忆为 Markdown 文件
     */
    suspend fun exportAllMemories(config: MemoryExportConfig): ExportResult {
        logger.info("开始导出记忆到 Markdown 文件")
        
        try {
            val outputDir = getOutputDirectory(config.outputPath)
            val memories = getMemoriesToExport(config)
            
            if (memories.isEmpty()) {
                return ExportResult.failure("没有找到符合条件的记忆")
            }
            
            // 创建输出目录结构
            createDirectoryStructure(outputDir, memories)
            
            // 按分类导出记忆
            val exportedFiles = if (config.groupByCategory) {
                exportByCategory(outputDir, memories, config)
            } else {
                exportAsFlat(outputDir, memories, config)
            }
            
            // 生成索引文件
            generateIndexFile(outputDir, memories, exportedFiles, config)
            
            // 生成统计报告
            generateStatisticsReport(outputDir, memories)
            
            logger.info("成功导出 ${memories.size} 个记忆到 ${exportedFiles.size} 个文件")
            
            return ExportResult.success(
                message = "成功导出 ${memories.size} 个记忆",
                exportedFiles = exportedFiles,
                outputDirectory = outputDir.absolutePath
            )
            
        } catch (e: Exception) {
            logger.error("导出记忆失败", e)
            return ExportResult.failure("导出失败: ${e.message}")
        }
    }
    
    /**
     * 增量导出 - 只导出新增或修改的记忆
     */
    suspend fun incrementalExport(config: MemoryExportConfig, lastExportTime: String): ExportResult {
        logger.info("开始增量导出记忆")
        
        try {
            val memories = getMemoriesToExport(config)
                .filter { it.updatedAt > lastExportTime }
            
            if (memories.isEmpty()) {
                return ExportResult.success("没有新的记忆需要导出", emptyList(), "")
            }
            
            val outputDir = getOutputDirectory(config.outputPath)
            val exportedFiles = exportMemories(outputDir, memories, config)
            
            // 更新索引文件
            updateIndexFile(outputDir, memories, config)
            
            return ExportResult.success(
                message = "增量导出 ${memories.size} 个记忆",
                exportedFiles = exportedFiles,
                outputDirectory = outputDir.absolutePath
            )
            
        } catch (e: Exception) {
            logger.error("增量导出失败", e)
            return ExportResult.failure("增量导出失败: ${e.message}")
        }
    }
    
    /**
     * 导出单个记忆
     */
    suspend fun exportSingleMemory(memoryId: String, outputPath: String? = null): ExportResult {
        try {
            val memory = memoryBankService.getAllMemories().find { it.id == memoryId }
                ?: return ExportResult.failure("记忆不存在: $memoryId")
            
            val outputDir = getOutputDirectory(outputPath)
            val fileName = sanitizeFileName("${memory.title}.md")
            val file = File(outputDir, fileName)
            
            val markdownContent = generateMemoryMarkdown(memory, true)
            file.writeText(markdownContent, Charsets.UTF_8)
            
            return ExportResult.success(
                message = "成功导出记忆: ${memory.title}",
                exportedFiles = listOf(file.absolutePath),
                outputDirectory = outputDir.absolutePath
            )
            
        } catch (e: Exception) {
            logger.error("导出单个记忆失败", e)
            return ExportResult.failure("导出失败: ${e.message}")
        }
    }
    
    /**
     * 获取要导出的记忆列表
     */
    private fun getMemoriesToExport(config: MemoryExportConfig): List<cc.unitmesh.devti.memory.MemorySummary> {
        return memoryBankService.getAllMemories()
            .filter { memory ->
                memory.importance >= config.minImportance &&
                (config.includeTypes.isEmpty() || config.includeTypes.contains(MemoryType.LONG_TERM))
            }
            .let { memories ->
                when (config.sortBy) {
                    MemorySortBy.IMPORTANCE -> memories.sortedByDescending { it.importance }
                    MemorySortBy.CREATED_TIME -> memories.sortedByDescending { it.createdAt }
                    MemorySortBy.ACCESS_TIME -> memories.sortedByDescending { it.updatedAt }
                    else -> memories.sortedBy { it.title }
                }
            }
    }
    
    /**
     * 按分类导出记忆
     */
    private suspend fun exportByCategory(
        outputDir: File,
        memories: List<cc.unitmesh.devti.memory.MemorySummary>,
        config: MemoryExportConfig
    ): List<String> {
        val exportedFiles = mutableListOf<String>()
        val memoriesByCategory = memories.groupBy { it.category }
        
        memoriesByCategory.forEach { (category, categoryMemories) ->
            val categoryDir = File(outputDir, sanitizeFileName(category))
            categoryDir.mkdirs()
            
            // 为每个分类生成一个汇总文件
            val categoryFile = File(categoryDir, "README.md")
            val categoryContent = generateCategoryMarkdown(category, categoryMemories, config)
            categoryFile.writeText(categoryContent, Charsets.UTF_8)
            exportedFiles.add(categoryFile.absolutePath)
            
            // 导出分类下的每个记忆
            categoryMemories.forEach { memory ->
                val fileName = sanitizeFileName("${memory.title}.md")
                val memoryFile = File(categoryDir, fileName)
                val memoryContent = generateMemoryMarkdown(memory, config.includeMetadata)
                memoryFile.writeText(memoryContent, Charsets.UTF_8)
                exportedFiles.add(memoryFile.absolutePath)
            }
        }
        
        return exportedFiles
    }
    
    /**
     * 平铺导出记忆
     */
    private suspend fun exportAsFlat(
        outputDir: File,
        memories: List<cc.unitmesh.devti.memory.MemorySummary>,
        config: MemoryExportConfig
    ): List<String> {
        return exportMemories(outputDir, memories, config)
    }
    
    /**
     * 导出记忆文件
     */
    private suspend fun exportMemories(
        outputDir: File,
        memories: List<cc.unitmesh.devti.memory.MemorySummary>,
        config: MemoryExportConfig
    ): List<String> {
        val exportedFiles = mutableListOf<String>()
        
        memories.forEach { memory ->
            val fileName = sanitizeFileName("${memory.title}.md")
            val file = File(outputDir, fileName)
            val content = generateMemoryMarkdown(memory, config.includeMetadata)
            file.writeText(content, Charsets.UTF_8)
            exportedFiles.add(file.absolutePath)
        }
        
        return exportedFiles
    }
    
    /**
     * 生成记忆的 Markdown 内容
     */
    private fun generateMemoryMarkdown(
        memory: cc.unitmesh.devti.memory.MemorySummary,
        includeMetadata: Boolean
    ): String {
        val builder = StringBuilder()
        
        // 标题
        builder.append("# ${memory.title}\n\n")
        
        // 元数据
        if (includeMetadata) {
            builder.append("## 元数据\n\n")
            builder.append("- **ID**: ${memory.id}\n")
            builder.append("- **分类**: ${memory.category}\n")
            builder.append("- **重要性**: ${"★".repeat(memory.importance)}\n")
            builder.append("- **创建时间**: ${formatDateTime(memory.createdAt)}\n")
            builder.append("- **更新时间**: ${formatDateTime(memory.updatedAt)}\n")
            
            if (memory.tags.isNotEmpty()) {
                builder.append("- **标签**: ${memory.tags.joinToString(", ")}\n")
            }
            
            builder.append("\n---\n\n")
        }
        
        // 内容
        builder.append("## 内容\n\n")
        builder.append(memory.content)
        builder.append("\n\n")
        
        // 导出信息
        builder.append("---\n")
        builder.append("*导出时间: ${LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))}*\n")
        
        return builder.toString()
    }
    
    /**
     * 生成分类汇总 Markdown
     */
    private fun generateCategoryMarkdown(
        category: String,
        memories: List<cc.unitmesh.devti.memory.MemorySummary>,
        config: MemoryExportConfig
    ): String {
        val builder = StringBuilder()
        
        builder.append("# ${category.uppercase()} 分类记忆汇总\n\n")
        builder.append("本分类包含 ${memories.size} 个记忆。\n\n")
        
        // 统计信息
        builder.append("## 统计信息\n\n")
        val avgImportance = memories.map { it.importance }.average()
        builder.append("- **记忆数量**: ${memories.size}\n")
        builder.append("- **平均重要性**: ${"%.1f".format(avgImportance)}\n")
        builder.append("- **最高重要性**: ${memories.maxOfOrNull { it.importance } ?: 0}\n")
        
        val tagCounts = memories.flatMap { it.tags }.groupingBy { it }.eachCount()
        if (tagCounts.isNotEmpty()) {
            builder.append("- **常用标签**: ${tagCounts.entries.sortedByDescending { it.value }.take(5).joinToString(", ") { "${it.key}(${it.value})" }}\n")
        }
        
        builder.append("\n")
        
        // 记忆列表
        builder.append("## 记忆列表\n\n")
        memories.forEachIndexed { index, memory ->
            builder.append("${index + 1}. **[${memory.title}](${sanitizeFileName(memory.title)}.md)**\n")
            builder.append("   - 重要性: ${"★".repeat(memory.importance)}\n")
            builder.append("   - 创建时间: ${formatDateTime(memory.createdAt)}\n")
            if (memory.tags.isNotEmpty()) {
                builder.append("   - 标签: ${memory.tags.joinToString(", ")}\n")
            }
            builder.append("\n")
        }
        
        return builder.toString()
    }
    
    /**
     * 生成索引文件
     */
    private suspend fun generateIndexFile(
        outputDir: File,
        memories: List<cc.unitmesh.devti.memory.MemorySummary>,
        exportedFiles: List<String>,
        config: MemoryExportConfig
    ) {
        val indexFile = File(outputDir, "INDEX.md")
        val builder = StringBuilder()
        
        builder.append("# 记忆银行导出索引\n\n")
        builder.append("导出时间: ${LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))}\n\n")
        
        // 统计信息
        builder.append("## 导出统计\n\n")
        builder.append("- **总记忆数**: ${memories.size}\n")
        builder.append("- **导出文件数**: ${exportedFiles.size}\n")
        builder.append("- **平均重要性**: ${"%.1f".format(memories.map { it.importance }.average())}\n")
        
        val categories = memories.groupBy { it.category }
        builder.append("- **分类分布**:\n")
        categories.forEach { (category, categoryMemories) ->
            builder.append("  - ${category}: ${categoryMemories.size} 个\n")
        }
        
        builder.append("\n")
        
        // 按分类组织的目录
        if (config.groupByCategory) {
            builder.append("## 分类目录\n\n")
            categories.forEach { (category, categoryMemories) ->
                builder.append("### ${category.uppercase()}\n\n")
                categoryMemories.forEach { memory ->
                    val relativePath = "${sanitizeFileName(category)}/${sanitizeFileName(memory.title)}.md"
                    builder.append("- [${memory.title}]($relativePath) ${"★".repeat(memory.importance)}\n")
                }
                builder.append("\n")
            }
        } else {
            builder.append("## 记忆目录\n\n")
            memories.forEach { memory ->
                val fileName = "${sanitizeFileName(memory.title)}.md"
                builder.append("- [${memory.title}]($fileName) - ${memory.category} ${"★".repeat(memory.importance)}\n")
            }
        }
        
        indexFile.writeText(builder.toString(), Charsets.UTF_8)
    }
    
    /**
     * 更新索引文件
     */
    private suspend fun updateIndexFile(
        outputDir: File,
        newMemories: List<cc.unitmesh.devti.memory.MemorySummary>,
        config: MemoryExportConfig
    ) {
        // 重新生成完整的索引文件
        val allMemories = getMemoriesToExport(config)
        val allFiles = getAllExportedFiles(outputDir)
        generateIndexFile(outputDir, allMemories, allFiles, config)
    }
    
    /**
     * 生成统计报告
     */
    private suspend fun generateStatisticsReport(
        outputDir: File,
        memories: List<cc.unitmesh.devti.memory.MemorySummary>
    ) {
        val reportFile = File(outputDir, "STATISTICS.md")
        val builder = StringBuilder()
        
        builder.append("# 记忆银行统计报告\n\n")
        builder.append("生成时间: ${LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))}\n\n")
        
        // 基本统计
        builder.append("## 基本统计\n\n")
        builder.append("| 指标 | 数值 |\n")
        builder.append("|------|------|\n")
        builder.append("| 总记忆数 | ${memories.size} |\n")
        builder.append("| 平均重要性 | ${"%.2f".format(memories.map { it.importance }.average())} |\n")
        builder.append("| 最高重要性 | ${memories.maxOfOrNull { it.importance } ?: 0} |\n")
        builder.append("| 最低重要性 | ${memories.minOfOrNull { it.importance } ?: 0} |\n")
        
        // 分类分布
        val categories = memories.groupBy { it.category }
        builder.append("\n## 分类分布\n\n")
        builder.append("| 分类 | 数量 | 占比 |\n")
        builder.append("|------|------|------|\n")
        categories.forEach { (category, categoryMemories) ->
            val percentage = (categoryMemories.size.toDouble() / memories.size * 100)
            builder.append("| $category | ${categoryMemories.size} | ${"%.1f".format(percentage)}% |\n")
        }
        
        // 重要性分布
        val importanceDistribution = memories.groupBy { it.importance }
        builder.append("\n## 重要性分布\n\n")
        builder.append("| 重要性 | 数量 | 占比 |\n")
        builder.append("|--------|------|------|\n")
        (1..5).forEach { importance ->
            val count = importanceDistribution[importance]?.size ?: 0
            val percentage = (count.toDouble() / memories.size * 100)
            builder.append("| ${"★".repeat(importance)} | $count | ${"%.1f".format(percentage)}% |\n")
        }
        
        reportFile.writeText(builder.toString(), Charsets.UTF_8)
    }
    
    // 辅助方法
    private fun getOutputDirectory(outputPath: String?): File {
        val defaultPath = File(project.basePath ?: ".", "memory-export")
        val dir = if (outputPath != null) File(outputPath) else defaultPath
        dir.mkdirs()
        return dir
    }
    
    private fun createDirectoryStructure(outputDir: File, memories: List<cc.unitmesh.devti.memory.MemorySummary>) {
        outputDir.mkdirs()
        
        // 为每个分类创建目录
        memories.map { it.category }.distinct().forEach { category ->
            File(outputDir, sanitizeFileName(category)).mkdirs()
        }
    }
    
    private fun sanitizeFileName(name: String): String {
        return name.replace(Regex("[^\\w\\u4e00-\\u9fa5\\s-]"), "")
            .replace(Regex("\\s+"), "-")
            .trim('-')
            .take(100) // 限制文件名长度
    }
    
    private fun formatDateTime(dateTimeString: String): String {
        return try {
            dateTimeString.substring(0, 16).replace("T", " ")
        } catch (e: Exception) {
            dateTimeString
        }
    }
    
    private fun getAllExportedFiles(outputDir: File): List<String> {
        return outputDir.walkTopDown()
            .filter { it.isFile && it.extension == "md" }
            .map { it.absolutePath }
            .toList()
    }
}

/**
 * 导出结果
 */
data class ExportResult(
    val success: Boolean,
    val message: String,
    val exportedFiles: List<String> = emptyList(),
    val outputDirectory: String = "",
    val error: String? = null
) {
    companion object {
        fun success(message: String, exportedFiles: List<String>, outputDirectory: String): ExportResult {
            return ExportResult(true, message, exportedFiles, outputDirectory)
        }
        
        fun failure(message: String): ExportResult {
            return ExportResult(false, message, error = message)
        }
    }
}
