package cc.unitmesh.devti.gui.example

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.project.DumbAware

/**
 * 用于展示可折叠面板示例的动作类
 */
class ShowCollapsiblePanelExampleAction : AnAction("Show Collapsible Panel Example"), DumbAware {
    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return
        val example = CollapsiblePanelExample(project)
        example.show() // 这会显示对话框
    }
}