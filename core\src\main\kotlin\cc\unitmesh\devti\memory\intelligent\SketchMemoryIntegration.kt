package cc.unitmesh.devti.memory.intelligent

import cc.unitmesh.devti.gui.memory.MemoryBankDialog
import cc.unitmesh.devti.gui.memory.MemorySaveDialog
import cc.unitmesh.devti.sketch.SketchToolWindow
import com.intellij.notification.NotificationGroupManager
import com.intellij.notification.NotificationType
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.logger
import com.intellij.openapi.project.Project
import kotlinx.coroutines.*
import java.awt.BorderLayout
import java.awt.FlowLayout
import java.awt.event.ActionEvent
import java.awt.event.ActionListener
import javax.swing.*

/**
 * SketchWindow 记忆集成服务
 * 
 * 功能：
 * 1. 监听 SketchWindow 的问答对话
 * 2. 自动识别重要对话并触发记忆添加
 * 3. 提供手动添加记忆的界面
 * 4. 集成AI摘要生成
 */
@Service(Service.Level.PROJECT)
class SketchMemoryIntegration(private val project: Project) {
    
    companion object {
        fun getInstance(project: Project): SketchMemoryIntegration {
            return project.getService(SketchMemoryIntegration::class.java)
        }
    }
    
    private val logger = logger<SketchMemoryIntegration>()
    private val memoryFacade = IntelligentMemoryFacade.getInstance(project)
    private val integrationScope = CoroutineScope(Dispatchers.Default + SupervisorJob())
    
    // 对话历史缓存
    private val conversationHistory = mutableListOf<ConversationEntry>()
    private val maxHistorySize = 100

    // 对话监听器管理
    private val conversationListeners = mutableMapOf<SketchToolWindow, SketchConversationListener>()
    
    /**
     * 集成到 SketchWindow
     */
    fun integrateWithSketchWindow(sketchWindow: SketchToolWindow) {
        logger.info("开始集成 SketchWindow 记忆功能")
        
        try {
            // 添加记忆按钮到 SketchWindow
            addMemoryButtonToSketch(sketchWindow)
            
            // 监听对话事件（如果SketchWindow支持）
            setupConversationListener(sketchWindow)
            
            logger.info("SketchWindow 记忆集成完成")
            
        } catch (e: Exception) {
            logger.error("SketchWindow 记忆集成失败", e)
        }
    }
    
    /**
     * 添加记忆按钮到 SketchWindow
     */
    private fun addMemoryButtonToSketch(sketchWindow: SketchToolWindow) {
        try {
            // 获取 SketchWindow 的工具栏或按钮面板
            val toolbarPanel = findToolbarPanel(sketchWindow)
            
            if (toolbarPanel != null) {
                // 创建记忆按钮
                val memoryButton = JButton("💾 保存记忆").apply {
                    toolTipText = "将当前对话保存为记忆"
                    addActionListener { showSaveMemoryDialog(sketchWindow) }
                }
                
                // 创建记忆银行按钮
                val bankButton = JButton("🧠 记忆银行").apply {
                    toolTipText = "打开记忆银行"
                    addActionListener { openMemoryBank() }
                }
                
                // 添加到工具栏
                toolbarPanel.add(Box.createHorizontalStrut(10))
                toolbarPanel.add(memoryButton)
                toolbarPanel.add(Box.createHorizontalStrut(5))
                toolbarPanel.add(bankButton)
                
                // 刷新界面
                toolbarPanel.revalidate()
                toolbarPanel.repaint()
                
                logger.info("记忆按钮已添加到 SketchWindow")
            } else {
                logger.warn("无法找到 SketchWindow 的工具栏面板")
            }
            
        } catch (e: Exception) {
            logger.error("添加记忆按钮失败", e)
        }
    }
    
    /**
     * 查找 SketchWindow 的工具栏面板
     */
    private fun findToolbarPanel(sketchWindow: SketchToolWindow): JPanel? {
        return try {
            // 通过反射或组件遍历查找工具栏
            // 这里需要根据 SketchWindow 的实际结构来实现
            findPanelByName(sketchWindow, "toolbar") ?: 
            findPanelByName(sketchWindow, "buttonPanel") ?: 
            createNewToolbarPanel(sketchWindow)
        } catch (e: Exception) {
            logger.warn("查找工具栏面板失败", e)
            null
        }
    }
    
    /**
     * 根据名称查找面板
     */
    private fun findPanelByName(container: java.awt.Container, name: String): JPanel? {
        for (component in container.components) {
            if (component is JPanel && component.name == name) {
                return component
            }
            if (component is java.awt.Container) {
                val found = findPanelByName(component, name)
                if (found != null) return found
            }
        }
        return null
    }
    
    /**
     * 创建新的工具栏面板
     */
    private fun createNewToolbarPanel(sketchWindow: SketchToolWindow): JPanel? {
        return try {
            val toolbarPanel = JPanel(FlowLayout(FlowLayout.RIGHT))
            toolbarPanel.name = "memoryToolbar"
            
            // 尝试添加到 SketchWindow 的顶部
            if (sketchWindow.layout is BorderLayout) {
                sketchWindow.add(toolbarPanel, BorderLayout.NORTH)
                toolbarPanel
            } else {
                // 如果不是 BorderLayout，尝试其他方式
                sketchWindow.add(toolbarPanel)
                toolbarPanel
            }
        } catch (e: Exception) {
            logger.error("创建工具栏面板失败", e)
            null
        }
    }
    
    /**
     * 设置对话监听器
     */
    private fun setupConversationListener(sketchWindow: SketchToolWindow) {
        // 这里需要根据 SketchWindow 的实际API来实现
        // 假设 SketchWindow 有对话事件监听机制
        try {
            // 示例：监听文本变化或消息发送事件
            // sketchWindow.addConversationListener { userMessage, aiResponse ->
            //     recordConversation(userMessage, aiResponse)
            // }
            
            logger.info("对话监听器设置完成")
        } catch (e: Exception) {
            logger.warn("设置对话监听器失败", e)
        }
    }
    
    /**
     * 显示保存记忆对话框
     */
    private fun showSaveMemoryDialog(sketchWindow: SketchToolWindow) {
        try {
            // 获取当前对话内容
            val conversationContent = extractConversationContent(sketchWindow)
            
            if (conversationContent.isBlank()) {
                showNotification("没有找到对话内容", NotificationType.WARNING)
                return
            }
            
            // 显示保存对话框
            val dialog = MemorySaveDialog(project, conversationContent) { title, content, category ->
                saveConversationAsMemory(title, content, category)
            }
            dialog.show()
            
        } catch (e: Exception) {
            logger.error("显示保存记忆对话框失败", e)
            showNotification("保存记忆失败: ${e.message}", NotificationType.ERROR)
        }
    }
    
    /**
     * 提取对话内容
     */
    private fun extractConversationContent(sketchWindow: SketchToolWindow): String {
        return try {
            // 这里需要根据 SketchWindow 的实际结构来提取对话内容
            // 可能需要查找文本组件、聊天历史等
            
            val content = StringBuilder()
            
            // 方法1：从对话历史缓存获取
            if (conversationHistory.isNotEmpty()) {
                val recentConversations = conversationHistory.takeLast(5)
                recentConversations.forEach { entry ->
                    content.append("**用户**: ${entry.userMessage}\n\n")
                    content.append("**助手**: ${entry.aiResponse}\n\n")
                    content.append("---\n\n")
                }
            }
            
            // 方法2：尝试从UI组件提取
            if (content.isEmpty()) {
                val extractedText = extractTextFromComponents(sketchWindow)
                if (extractedText.isNotEmpty()) {
                    content.append(extractedText)
                }
            }
            
            // 方法3：提供默认模板
            if (content.isEmpty()) {
                content.append("# 对话记录\n\n")
                content.append("**时间**: ${java.time.LocalDateTime.now()}\n\n")
                content.append("**内容**: [请手动输入对话内容]\n\n")
            }
            
            content.toString()
            
        } catch (e: Exception) {
            logger.error("提取对话内容失败", e)
            "提取对话内容失败: ${e.message}"
        }
    }
    
    /**
     * 从UI组件提取文本
     */
    private fun extractTextFromComponents(container: java.awt.Container): String {
        val textBuilder = StringBuilder()
        
        fun extractFromComponent(component: java.awt.Component) {
            when (component) {
                is JTextArea -> {
                    if (component.text.isNotBlank()) {
                        textBuilder.append(component.text).append("\n\n")
                    }
                }
                is JTextPane -> {
                    if (component.text.isNotBlank()) {
                        textBuilder.append(component.text).append("\n\n")
                    }
                }
                is JEditorPane -> {
                    if (component.text.isNotBlank()) {
                        textBuilder.append(component.text).append("\n\n")
                    }
                }
                is java.awt.Container -> {
                    for (child in component.components) {
                        extractFromComponent(child)
                    }
                }
            }
        }
        
        extractFromComponent(container)
        return textBuilder.toString().trim()
    }
    
    /**
     * 保存对话为记忆
     */
    private fun saveConversationAsMemory(title: String, content: String, category: String) {
        integrationScope.launch {
            try {
                val result = memoryFacade.addMemoryWithContext(
                    title = title,
                    content = content,
                    source = "sketch_conversation",
                    context = mapOf(
                        "timestamp" to System.currentTimeMillis().toString(),
                        "source" to "SketchWindow",
                        "category" to category,
                        "auto_generated" to "false"
                    )
                )
                
                SwingUtilities.invokeLater {
                    if (result.success) {
                        showNotification(
                            "对话已保存为记忆: $title",
                            NotificationType.INFORMATION
                        )
                    } else {
                        showNotification(
                            "保存记忆失败: ${result.message}",
                            NotificationType.ERROR
                        )
                    }
                }
                
            } catch (e: Exception) {
                logger.error("保存对话记忆失败", e)
                SwingUtilities.invokeLater {
                    showNotification(
                        "保存记忆异常: ${e.message}",
                        NotificationType.ERROR
                    )
                }
            }
        }
    }
    
    /**
     * 记录对话
     */
    fun recordConversation(userMessage: String, aiResponse: String) {
        val entry = ConversationEntry(
            userMessage = userMessage,
            aiResponse = aiResponse,
            timestamp = System.currentTimeMillis()
        )
        
        conversationHistory.add(entry)
        
        // 限制历史记录大小
        if (conversationHistory.size > maxHistorySize) {
            conversationHistory.removeAt(0)
        }
        
        // 自动判断是否需要保存为记忆
        checkAutoSaveConditions(entry)
    }
    
    /**
     * 检查自动保存条件
     */
    private fun checkAutoSaveConditions(entry: ConversationEntry) {
        integrationScope.launch {
            try {
                // 简单的自动保存条件判断
                val shouldAutoSave = when {
                    entry.userMessage.length > 100 && entry.aiResponse.length > 200 -> true
                    entry.userMessage.contains("重要") || entry.aiResponse.contains("重要") -> true
                    entry.userMessage.contains("保存") || entry.userMessage.contains("记住") -> true
                    entry.aiResponse.contains("解决方案") || entry.aiResponse.contains("建议") -> true
                    else -> false
                }
                
                if (shouldAutoSave) {
                    val title = "自动保存 - ${entry.userMessage.take(20)}..."
                    val content = buildString {
                        append("# 自动保存的对话\n\n")
                        append("**用户**: ${entry.userMessage}\n\n")
                        append("**助手**: ${entry.aiResponse}\n\n")
                        append("**时间**: ${java.time.Instant.ofEpochMilli(entry.timestamp)}\n")
                    }
                    
                    val result = memoryFacade.addMemoryWithContext(
                        title = title,
                        content = content,
                        source = "auto_save",
                        context = mapOf(
                            "auto_generated" to "true",
                            "trigger" to "conversation_analysis"
                        )
                    )
                    
                    if (result.success) {
                        SwingUtilities.invokeLater {
                            showNotification(
                                "重要对话已自动保存为记忆",
                                NotificationType.INFORMATION
                            )
                        }
                    }
                }
                
            } catch (e: Exception) {
                logger.error("自动保存检查失败", e)
            }
        }
    }
    
    /**
     * 打开记忆银行
     */
    private fun openMemoryBank() {
        try {
            val dialog = MemoryBankDialog(project)
            dialog.show()
        } catch (e: Exception) {
            logger.error("打开记忆银行失败", e)
            showNotification("打开记忆银行失败: ${e.message}", NotificationType.ERROR)
        }
    }
    
    /**
     * 显示通知
     */
    private fun showNotification(message: String, type: NotificationType) {
        NotificationGroupManager.getInstance()
            .getNotificationGroup("AutoDev.Memory")
            .createNotification("记忆集成", message, type)
            .notify(project)
    }
    
    /**
     * 获取对话历史
     */
    fun getConversationHistory(): List<ConversationEntry> {
        return conversationHistory.toList()
    }
    
    /**
     * 清空对话历史
     */
    fun clearConversationHistory() {
        conversationHistory.clear()
    }
}

/**
 * 对话条目
 */
data class ConversationEntry(
    val userMessage: String,
    val aiResponse: String,
    val timestamp: Long
)
