package cc.unitmesh.devti.memory.intelligent

import cc.unitmesh.devti.sketch.SketchToolWindow
import com.intellij.openapi.diagnostic.logger
import com.intellij.openapi.project.Project
import kotlinx.coroutines.*
import java.awt.event.ComponentAdapter
import java.awt.event.ComponentEvent
import javax.swing.*
import javax.swing.event.DocumentEvent
import javax.swing.event.DocumentListener
import javax.swing.text.JTextComponent

/**
 * SketchWindow 对话监听器
 * 
 * 监听SketchWindow中的对话变化，自动捕获问答内容并传入记忆处理模块
 */
class SketchConversationListener(
    private val project: Project,
    private val sketchWindow: SketchToolWindow
) {
    
    private val logger = logger<SketchConversationListener>()
    private val memoryIntegration = SketchMemoryIntegration.getInstance(project)
    private val listenerScope = CoroutineScope(Dispatchers.Default + SupervisorJob())
    
    // 对话状态跟踪
    private var lastUserMessage = ""
    private var lastAiResponse = ""
    private var isMonitoring = false
    
    // UI组件引用
    private var inputComponent: JTextComponent? = null
    private var outputComponent: JTextComponent? = null
    
    /**
     * 开始监听对话
     */
    fun startListening() {
        if (isMonitoring) {
            logger.warn("对话监听已经在运行")
            return
        }
        
        logger.info("开始监听 SketchWindow 对话")
        isMonitoring = true
        
        try {
            // 查找输入和输出组件
            findConversationComponents()
            
            // 设置监听器
            setupInputListener()
            setupOutputListener()
            setupWindowListener()
            
            logger.info("SketchWindow 对话监听器设置完成")
            
        } catch (e: Exception) {
            logger.error("设置对话监听器失败", e)
            isMonitoring = false
        }
    }
    
    /**
     * 停止监听对话
     */
    fun stopListening() {
        if (!isMonitoring) return
        
        logger.info("停止监听 SketchWindow 对话")
        isMonitoring = false
        
        // 清理监听器
        inputComponent?.document?.removeDocumentListener(inputDocumentListener)
        outputComponent?.document?.removeDocumentListener(outputDocumentListener)
        
        listenerScope.cancel()
    }
    
    /**
     * 查找对话相关的UI组件
     */
    private fun findConversationComponents() {
        // 递归查找文本组件
        val textComponents = findTextComponents(sketchWindow)
        
        logger.info("找到 ${textComponents.size} 个文本组件")
        
        // 启发式识别输入和输出组件
        textComponents.forEach { component ->
            when {
                // 输入组件通常是可编辑的，较小的
                component.isEditable && component.preferredSize.height < 100 -> {
                    if (inputComponent == null) {
                        inputComponent = component
                        logger.info("识别输入组件: ${component.javaClass.simpleName}")
                    }
                }
                // 输出组件通常是不可编辑的，较大的
                !component.isEditable && component.preferredSize.height > 100 -> {
                    if (outputComponent == null) {
                        outputComponent = component
                        logger.info("识别输出组件: ${component.javaClass.simpleName}")
                    }
                }
                // 如果都是可编辑的，选择较大的作为输出
                component.isEditable && component.preferredSize.height > 200 -> {
                    outputComponent = component
                    logger.info("识别大型输出组件: ${component.javaClass.simpleName}")
                }
            }
        }
        
        // 如果没有找到明确的组件，使用第一个和最后一个
        if (inputComponent == null && textComponents.isNotEmpty()) {
            inputComponent = textComponents.first()
            logger.info("使用第一个组件作为输入: ${inputComponent!!.javaClass.simpleName}")
        }
        
        if (outputComponent == null && textComponents.size > 1) {
            outputComponent = textComponents.last()
            logger.info("使用最后一个组件作为输出: ${outputComponent!!.javaClass.simpleName}")
        }
    }
    
    /**
     * 递归查找所有文本组件
     */
    private fun findTextComponents(container: java.awt.Container): List<JTextComponent> {
        val components = mutableListOf<JTextComponent>()
        
        fun searchComponents(comp: java.awt.Component) {
            when (comp) {
                is JTextComponent -> components.add(comp)
                is java.awt.Container -> {
                    for (child in comp.components) {
                        searchComponents(child)
                    }
                }
            }
        }
        
        searchComponents(container)
        return components
    }
    
    /**
     * 设置输入监听器
     */
    private fun setupInputListener() {
        inputComponent?.document?.addDocumentListener(inputDocumentListener)
    }
    
    /**
     * 设置输出监听器
     */
    private fun setupOutputListener() {
        outputComponent?.document?.addDocumentListener(outputDocumentListener)
    }
    
    /**
     * 设置窗口监听器
     */
    private fun setupWindowListener() {
        sketchWindow.addComponentListener(object : ComponentAdapter() {
            override fun componentHidden(e: ComponentEvent?) {
                // 窗口隐藏时处理未保存的对话
                processCurrentConversation()
            }
        })
    }
    
    /**
     * 输入文档监听器
     */
    private val inputDocumentListener = object : DocumentListener {
        override fun insertUpdate(e: DocumentEvent?) = handleInputChange()
        override fun removeUpdate(e: DocumentEvent?) = handleInputChange()
        override fun changedUpdate(e: DocumentEvent?) = handleInputChange()
        
        private fun handleInputChange() {
            if (!isMonitoring) return
            
            listenerScope.launch {
                delay(500) // 防抖动
                
                val currentText = inputComponent?.text ?: ""
                if (currentText != lastUserMessage && currentText.isNotBlank()) {
                    logger.debug("检测到用户输入变化: ${currentText.take(50)}...")
                    lastUserMessage = currentText
                }
            }
        }
    }
    
    /**
     * 输出文档监听器
     */
    private val outputDocumentListener = object : DocumentListener {
        override fun insertUpdate(e: DocumentEvent?) = handleOutputChange()
        override fun removeUpdate(e: DocumentEvent?) = handleOutputChange()
        override fun changedUpdate(e: DocumentEvent?) = handleOutputChange()
        
        private fun handleOutputChange() {
            if (!isMonitoring) return
            
            listenerScope.launch {
                delay(1000) // 等待AI响应完成
                
                val currentText = outputComponent?.text ?: ""
                if (currentText != lastAiResponse && currentText.isNotBlank()) {
                    logger.debug("检测到AI响应变化: ${currentText.take(50)}...")
                    
                    // 检查是否是新的完整对话
                    if (lastUserMessage.isNotBlank() && isCompleteResponse(currentText)) {
                        lastAiResponse = currentText
                        processNewConversation(lastUserMessage, lastAiResponse)
                    }
                }
            }
        }
    }
    
    /**
     * 判断是否是完整的AI响应
     */
    private fun isCompleteResponse(response: String): Boolean {
        // 简单的启发式判断
        return when {
            response.length < 10 -> false // 太短
            response.endsWith("...") -> false // 可能还在生成
            response.contains("正在思考") || response.contains("生成中") -> false // 明确的生成状态
            response.split("\n").size > 1 -> true // 多行通常表示完整
            response.length > 50 -> true // 足够长度
            else -> false
        }
    }
    
    /**
     * 处理新对话
     */
    private fun processNewConversation(userMessage: String, aiResponse: String) {
        logger.info("处理新对话 - 用户: ${userMessage.take(30)}... AI: ${aiResponse.take(30)}...")
        
        try {
            // 记录到对话历史
            memoryIntegration.recordConversation(userMessage, aiResponse)
            
        } catch (e: Exception) {
            logger.error("处理新对话失败", e)
        }
    }
    
    /**
     * 处理当前对话（窗口关闭时）
     */
    private fun processCurrentConversation() {
        if (lastUserMessage.isNotBlank() && lastAiResponse.isNotBlank()) {
            processNewConversation(lastUserMessage, lastAiResponse)
        }
    }
    
    /**
     * 获取监听状态
     */
    fun isListening(): Boolean = isMonitoring
    
    /**
     * 获取最后的对话
     */
    fun getLastConversation(): Pair<String, String>? {
        return if (lastUserMessage.isNotBlank() && lastAiResponse.isNotBlank()) {
            Pair(lastUserMessage, lastAiResponse)
        } else null
    }
}
