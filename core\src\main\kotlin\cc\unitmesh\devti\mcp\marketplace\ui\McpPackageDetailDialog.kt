package cc.unitmesh.devti.mcp.marketplace.ui

import cc.unitmesh.devti.mcp.marketplace.model.McpPackage
import cc.unitmesh.devti.mcp.marketplace.service.McpPackageInstaller
import com.intellij.icons.AllIcons
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.JBColor
import com.intellij.ui.components.*
import com.intellij.util.ui.JBUI
import com.intellij.util.ui.UIUtil
import java.awt.*
import java.awt.event.ActionEvent
import javax.swing.*

/**
 * MCP 包详情对话框
 */
class McpPackageDetailDialog(
    private val project: Project,
    private val mcpPackage: McpPackage,
    private val packageInstaller: McpPackageInstaller
) : DialogWrapper(project, true) {
    
    private val isInstalled = packageInstaller.isPackageInstalled(mcpPackage.id)
    
    init {
        title = mcpPackage.displayName
        setSize(600, 500)
        init()
    }
    
    override fun createCenterPanel(): JComponent {
        val mainPanel = JPanel(BorderLayout())
        mainPanel.preferredSize = Dimension(600, 500)
        
        val tabbedPane = JBTabbedPane()
        
        // 概览标签页
        tabbedPane.addTab("Overview", AllIcons.General.Information, createOverviewPanel())
        
        // 工具标签页
        if (mcpPackage.tools.isNotEmpty()) {
            tabbedPane.addTab("Tools", AllIcons.General.Settings, createToolsPanel())
        }
        
        // 安装标签页
        tabbedPane.addTab("Installation", AllIcons.Actions.Download, createInstallationPanel())
        
        mainPanel.add(tabbedPane, BorderLayout.CENTER)
        
        return mainPanel
    }
    
    override fun createActions(): Array<Action> {
        val actions = mutableListOf<Action>()
        
        if (isInstalled) {
            actions.add(object : AbstractAction("Uninstall") {
                override fun actionPerformed(e: ActionEvent) {
                    uninstallPackage()
                }
            })
        } else {
            actions.add(object : AbstractAction("Install") {
                override fun actionPerformed(e: ActionEvent) {
                    installPackage()
                }
            })
        }
        
        actions.add(cancelAction)
        return actions.toTypedArray()
    }
    
    private fun createOverviewPanel(): JComponent {
        val panel = JPanel(BorderLayout())
        panel.border = JBUI.Borders.empty(16)
        
        val contentPanel = JPanel()
        contentPanel.layout = BoxLayout(contentPanel, BoxLayout.Y_AXIS)
        
        // 包信息
        val infoPanel = createInfoSection()
        contentPanel.add(infoPanel)
        contentPanel.add(Box.createVerticalStrut(16))
        
        // 描述
        val descriptionPanel = createDescriptionSection()
        contentPanel.add(descriptionPanel)
        contentPanel.add(Box.createVerticalStrut(16))
        
        // 关键词
        if (mcpPackage.keywords.isNotEmpty()) {
            val keywordsPanel = createKeywordsSection()
            contentPanel.add(keywordsPanel)
            contentPanel.add(Box.createVerticalStrut(16))
        }
        
        // 链接
        val linksPanel = createLinksSection()
        contentPanel.add(linksPanel)
        
        val scrollPane = JBScrollPane(contentPanel)
        scrollPane.border = JBUI.Borders.empty()
        panel.add(scrollPane, BorderLayout.CENTER)
        
        return panel
    }
    
    private fun createInfoSection(): JComponent {
        val panel = JPanel()
        panel.layout = BoxLayout(panel, BoxLayout.Y_AXIS)
        panel.alignmentX = Component.LEFT_ALIGNMENT
        
        // 标题行
        val titlePanel = JPanel(FlowLayout(FlowLayout.LEFT, 0, 0))
        titlePanel.add(JBLabel(mcpPackage.displayName).apply {
            font = JBUI.Fonts.label(18f).asBold()
        })
        
        if (mcpPackage.isOfficial) {
            titlePanel.add(Box.createHorizontalStrut(8))
            titlePanel.add(createBadge("Official", JBColor.BLUE))
        }
        
        if (mcpPackage.isVerified) {
            titlePanel.add(Box.createHorizontalStrut(4))
            titlePanel.add(createBadge("Verified", JBColor.GREEN))
        }
        
        panel.add(titlePanel)
        panel.add(Box.createVerticalStrut(8))
        
        // 基本信息
        val infoGrid = JPanel(GridBagLayout())
        val gbc = GridBagConstraints()
        gbc.anchor = GridBagConstraints.WEST
        gbc.insets = JBUI.insets(2, 0)
        
        addInfoRow(infoGrid, gbc, 0, "Version:", mcpPackage.version)
        addInfoRow(infoGrid, gbc, 1, "Author:", mcpPackage.author)
        addInfoRow(infoGrid, gbc, 2, "Category:", mcpPackage.category.displayName)
        addInfoRow(infoGrid, gbc, 3, "Install Type:", mcpPackage.installType.displayName)
        addInfoRow(infoGrid, gbc, 4, "License:", mcpPackage.license ?: "Unknown")
        addInfoRow(infoGrid, gbc, 5, "Downloads:", mcpPackage.downloads.toString())
        
        if (mcpPackage.rating > 0) {
            addInfoRow(infoGrid, gbc, 6, "Rating:", String.format("%.1f ★", mcpPackage.rating))
        }
        
        panel.add(infoGrid)
        
        return panel
    }
    
    private fun createDescriptionSection(): JComponent {
        val panel = JPanel(BorderLayout())
        panel.alignmentX = Component.LEFT_ALIGNMENT
        
        val titleLabel = JBLabel("Description").apply {
            font = JBUI.Fonts.label(14f).asBold()
        }
        panel.add(titleLabel, BorderLayout.NORTH)
        
        val descriptionArea = JTextArea(mcpPackage.description).apply {
            isEditable = false
            isOpaque = false
            lineWrap = true
            wrapStyleWord = true
            border = JBUI.Borders.emptyTop(8)
        }
        panel.add(descriptionArea, BorderLayout.CENTER)
        
        return panel
    }
    
    private fun createKeywordsSection(): JComponent {
        val panel = JPanel(BorderLayout())
        panel.alignmentX = Component.LEFT_ALIGNMENT
        
        val titleLabel = JBLabel("Keywords").apply {
            font = JBUI.Fonts.label(14f).asBold()
        }
        panel.add(titleLabel, BorderLayout.NORTH)
        
        val keywordsPanel = JPanel(FlowLayout(FlowLayout.LEFT, 4, 4))
        keywordsPanel.border = JBUI.Borders.emptyTop(8)
        
        mcpPackage.keywords.forEach { keyword ->
            val keywordLabel = JBLabel(keyword).apply {
                font = JBUI.Fonts.smallFont()
                foreground = UIUtil.getContextHelpForeground()
                border = JBUI.Borders.compound(
                    JBUI.Borders.customLine(JBColor.border()),
                    JBUI.Borders.empty(2, 6)
                )
            }
            keywordsPanel.add(keywordLabel)
        }
        
        panel.add(keywordsPanel, BorderLayout.CENTER)
        
        return panel
    }
    
    private fun createLinksSection(): JComponent {
        val panel = JPanel(BorderLayout())
        panel.alignmentX = Component.LEFT_ALIGNMENT
        
        val titleLabel = JBLabel("Links").apply {
            font = JBUI.Fonts.label(14f).asBold()
        }
        panel.add(titleLabel, BorderLayout.NORTH)
        
        val linksPanel = JPanel()
        linksPanel.layout = BoxLayout(linksPanel, BoxLayout.Y_AXIS)
        linksPanel.border = JBUI.Borders.emptyTop(8)
        
        mcpPackage.repository?.let { repo ->
            val repoLink = JBLabel("<html><a href='$repo'>Repository</a></html>")
            linksPanel.add(repoLink)
        }
        
        mcpPackage.homepage?.let { homepage ->
            val homepageLink = JBLabel("<html><a href='$homepage'>Homepage</a></html>")
            linksPanel.add(homepageLink)
        }
        
        panel.add(linksPanel, BorderLayout.CENTER)
        
        return panel
    }
    
    private fun createToolsPanel(): JComponent {
        val panel = JPanel(BorderLayout())
        panel.border = JBUI.Borders.empty(16)
        
        val toolsPanel = JPanel()
        toolsPanel.layout = BoxLayout(toolsPanel, BoxLayout.Y_AXIS)
        
        mcpPackage.tools.forEach { tool ->
            val toolCard = createToolCard(tool.name, tool.description)
            toolsPanel.add(toolCard)
            toolsPanel.add(Box.createVerticalStrut(8))
        }
        
        val scrollPane = JBScrollPane(toolsPanel)
        scrollPane.border = JBUI.Borders.empty()
        panel.add(scrollPane, BorderLayout.CENTER)
        
        return panel
    }
    
    private fun createInstallationPanel(): JComponent {
        val panel = JPanel(BorderLayout())
        panel.border = JBUI.Borders.empty(16)
        
        val contentPanel = JPanel()
        contentPanel.layout = BoxLayout(contentPanel, BoxLayout.Y_AXIS)
        
        // 安装命令
        val commandPanel = JPanel(BorderLayout())
        commandPanel.add(JBLabel("Install Command:").apply {
            font = JBUI.Fonts.label(14f).asBold()
        }, BorderLayout.NORTH)
        
        val commandArea = JTextArea(mcpPackage.installCommand).apply {
            isEditable = false
            background = UIUtil.getTextFieldBackground()
            border = JBUI.Borders.compound(
                JBUI.Borders.customLine(JBColor.border()),
                JBUI.Borders.empty(8)
            )
            font = Font(Font.MONOSPACED, Font.PLAIN, 12)
        }
        commandPanel.add(commandArea, BorderLayout.CENTER)
        
        contentPanel.add(commandPanel)
        contentPanel.add(Box.createVerticalStrut(16))
        
        // 参数
        if (mcpPackage.args.isNotEmpty()) {
            val argsPanel = createArgsSection()
            contentPanel.add(argsPanel)
            contentPanel.add(Box.createVerticalStrut(16))
        }
        
        // 环境变量
        if (mcpPackage.env.isNotEmpty()) {
            val envPanel = createEnvSection()
            contentPanel.add(envPanel)
        }
        
        panel.add(contentPanel, BorderLayout.NORTH)
        
        return panel
    }
    
    private fun createToolCard(name: String, description: String): JComponent {
        val panel = JPanel(BorderLayout())
        panel.border = JBUI.Borders.compound(
            JBUI.Borders.customLine(JBColor.border()),
            JBUI.Borders.empty(8)
        )
        
        val nameLabel = JBLabel(name).apply {
            font = JBUI.Fonts.label(12f).asBold()
        }
        panel.add(nameLabel, BorderLayout.NORTH)
        
        val descLabel = JBLabel("<html>$description</html>").apply {
            foreground = UIUtil.getContextHelpForeground()
            border = JBUI.Borders.emptyTop(4)
        }
        panel.add(descLabel, BorderLayout.CENTER)
        
        return panel
    }
    
    private fun createArgsSection(): JComponent {
        val panel = JPanel(BorderLayout())
        panel.add(JBLabel("Arguments:").apply {
            font = JBUI.Fonts.label(14f).asBold()
        }, BorderLayout.NORTH)
        
        val argsList = JBList(mcpPackage.args.toTypedArray())
        argsList.border = JBUI.Borders.emptyTop(8)
        panel.add(argsList, BorderLayout.CENTER)
        
        return panel
    }
    
    private fun createEnvSection(): JComponent {
        val panel = JPanel(BorderLayout())
        panel.add(JBLabel("Environment Variables:").apply {
            font = JBUI.Fonts.label(14f).asBold()
        }, BorderLayout.NORTH)
        
        val envPanel = JPanel()
        envPanel.layout = BoxLayout(envPanel, BoxLayout.Y_AXIS)
        envPanel.border = JBUI.Borders.emptyTop(8)
        
        mcpPackage.env.forEach { (key, value) ->
            val envLabel = JBLabel("$key = $value").apply {
                font = Font(Font.MONOSPACED, Font.PLAIN, 12)
            }
            envPanel.add(envLabel)
        }
        
        panel.add(envPanel, BorderLayout.CENTER)
        
        return panel
    }
    
    private fun addInfoRow(panel: JPanel, gbc: GridBagConstraints, row: Int, label: String, value: String) {
        gbc.gridx = 0
        gbc.gridy = row
        gbc.weightx = 0.0
        panel.add(JBLabel("$label ").apply {
            font = JBUI.Fonts.label().asBold()
        }, gbc)
        
        gbc.gridx = 1
        gbc.weightx = 1.0
        panel.add(JBLabel(value), gbc)
    }
    
    private fun createBadge(text: String, color: JBColor): JComponent {
        return JBLabel(text).apply {
            font = JBUI.Fonts.smallFont()
            foreground = Color.WHITE
            isOpaque = true
            background = color
            border = JBUI.Borders.empty(2, 6)
        }
    }
    
    private fun installPackage() {
        packageInstaller.installPackage(mcpPackage).thenAccept { success ->
            SwingUtilities.invokeLater {
                if (success) {
                    close(OK_EXIT_CODE)
                }
            }
        }
    }
    
    private fun uninstallPackage() {
        packageInstaller.uninstallPackage(mcpPackage.id).thenAccept { success ->
            SwingUtilities.invokeLater {
                if (success) {
                    close(OK_EXIT_CODE)
                }
            }
        }
    }
}
