package cc.unitmesh.devti.gui.toolbar

import cc.unitmesh.devti.memory.intelligent.SketchMemoryIntegration
import cc.unitmesh.devti.sketch.SketchToolWindow
import com.intellij.icons.AllIcons
import com.intellij.notification.NotificationGroupManager
import com.intellij.notification.NotificationType
import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.ui.Messages
import com.intellij.openapi.wm.ToolWindowManager
import java.awt.Window
import javax.swing.SwingUtilities

/**
 * SketchWindow 记忆集成 Action
 */
class SketchMemoryIntegrationAction : AnAction(
    "集成记忆功能",
    "为SketchWindow集成智能记忆功能",
    AllIcons.Actions.IntentionBulb
) {

    override fun getActionUpdateThread(): ActionUpdateThread = ActionUpdateThread.BGT

    override fun update(e: AnActionEvent) {
        val project = e.project
        e.presentation.isEnabled = project != null
    }

    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return
        
        val options = arrayOf(
            "自动集成到所有SketchWindow",
            "手动选择SketchWindow集成",
            "查看集成状态",
            "测试记忆功能"
        )
        
        val choice = Messages.showChooseDialog(
            project,
            "请选择SketchWindow记忆集成操作：",
            "SketchWindow记忆集成",
            AllIcons.Actions.IntentionBulb,
            options,
            options[0]
        )
        
        when (choice) {
            0 -> autoIntegrateAllSketchWindows(project)
            1 -> manualIntegrateSketchWindow(project)
            2 -> showIntegrationStatus(project)
            3 -> testMemoryFeatures(project)
        }
    }
    
    private fun autoIntegrateAllSketchWindows(project: com.intellij.openapi.project.Project) {
        try {
            val integration = SketchMemoryIntegration.getInstance(project)
            val sketchWindows = findAllSketchWindows()
            
            if (sketchWindows.isEmpty()) {
                showNotification(
                    project,
                    "没有找到打开的SketchWindow",
                    "请先打开SketchWindow再进行集成",
                    NotificationType.WARNING
                )
                return
            }
            
            var successCount = 0
            sketchWindows.forEach { sketchWindow ->
                try {
                    integration.integrateWithSketchWindow(sketchWindow)
                    successCount++
                } catch (e: Exception) {
                    showNotification(
                        project,
                        "集成失败",
                        "SketchWindow集成失败: ${e.message}",
                        NotificationType.ERROR
                    )
                }
            }
            
            showNotification(
                project,
                "集成完成",
                "成功为 $successCount 个SketchWindow集成了记忆功能",
                NotificationType.INFORMATION
            )
            
        } catch (e: Exception) {
            showNotification(
                project,
                "自动集成失败",
                "自动集成过程中发生错误: ${e.message}",
                NotificationType.ERROR
            )
        }
    }
    
    private fun manualIntegrateSketchWindow(project: com.intellij.openapi.project.Project) {
        try {
            val sketchWindows = findAllSketchWindows()
            
            if (sketchWindows.isEmpty()) {
                showNotification(
                    project,
                    "没有找到SketchWindow",
                    "请先打开SketchWindow",
                    NotificationType.WARNING
                )
                return
            }
            
            val windowTitles = sketchWindows.mapIndexed { index, window ->
                "SketchWindow ${index + 1}: ${getWindowTitle(window)}"
            }.toTypedArray()
            
            val choice = Messages.showChooseDialog(
                project,
                "请选择要集成记忆功能的SketchWindow：",
                "选择SketchWindow",
                AllIcons.Actions.Find,
                windowTitles,
                windowTitles[0]
            )
            
            if (choice >= 0 && choice < sketchWindows.size) {
                val selectedWindow = sketchWindows[choice]
                val integration = SketchMemoryIntegration.getInstance(project)
                
                integration.integrateWithSketchWindow(selectedWindow)
                
                showNotification(
                    project,
                    "集成成功",
                    "已为选定的SketchWindow集成记忆功能",
                    NotificationType.INFORMATION
                )
            }
            
        } catch (e: Exception) {
            showNotification(
                project,
                "手动集成失败",
                "手动集成过程中发生错误: ${e.message}",
                NotificationType.ERROR
            )
        }
    }
    
    private fun showIntegrationStatus(project: com.intellij.openapi.project.Project) {
        try {
            val integration = SketchMemoryIntegration.getInstance(project)
            val conversationHistory = integration.getConversationHistory()
            val sketchWindows = findAllSketchWindows()
            
            val statusMessage = buildString {
                append("SketchWindow记忆集成状态\n")
                append("=".repeat(30) + "\n")
                append("发现的SketchWindow数量: ${sketchWindows.size}\n")
                append("对话历史记录数量: ${conversationHistory.size}\n\n")
                
                if (sketchWindows.isNotEmpty()) {
                    append("SketchWindow列表:\n")
                    sketchWindows.forEachIndexed { index, window ->
                        append("${index + 1}. ${getWindowTitle(window)}\n")
                    }
                    append("\n")
                }
                
                if (conversationHistory.isNotEmpty()) {
                    append("最近的对话记录:\n")
                    conversationHistory.takeLast(3).forEach { entry ->
                        val timestamp = java.time.Instant.ofEpochMilli(entry.timestamp)
                        append("- ${timestamp}: ${entry.userMessage.take(50)}...\n")
                    }
                }
            }
            
            Messages.showInfoMessage(project, statusMessage, "集成状态")
            
        } catch (e: Exception) {
            showNotification(
                project,
                "获取状态失败",
                "获取集成状态时发生错误: ${e.message}",
                NotificationType.ERROR
            )
        }
    }
    
    private fun testMemoryFeatures(project: com.intellij.openapi.project.Project) {
        try {
            val integration = SketchMemoryIntegration.getInstance(project)
            
            // 模拟一个对话
            val testUserMessage = "这是一个测试用户消息，用于验证记忆功能是否正常工作。"
            val testAiResponse = "这是一个测试AI响应，包含了一些重要的信息和建议，应该被保存为记忆。"
            
            integration.recordConversation(testUserMessage, testAiResponse)
            
            val conversationHistory = integration.getConversationHistory()
            val lastEntry = conversationHistory.lastOrNull()
            
            if (lastEntry != null) {
                val testResult = buildString {
                    append("记忆功能测试结果\n")
                    append("=".repeat(20) + "\n")
                    append("✅ 对话记录功能正常\n")
                    append("✅ 历史记录存储正常\n")
                    append("✅ 时间戳记录正常\n\n")
                    append("测试对话内容:\n")
                    append("用户: ${lastEntry.userMessage}\n")
                    append("AI: ${lastEntry.aiResponse}\n")
                    append("时间: ${java.time.Instant.ofEpochMilli(lastEntry.timestamp)}\n\n")
                    append("总对话记录数: ${conversationHistory.size}")
                }
                
                Messages.showInfoMessage(project, testResult, "测试结果")
                
                showNotification(
                    project,
                    "测试完成",
                    "记忆功能测试通过，所有功能正常",
                    NotificationType.INFORMATION
                )
            } else {
                showNotification(
                    project,
                    "测试失败",
                    "对话记录功能异常",
                    NotificationType.ERROR
                )
            }
            
        } catch (e: Exception) {
            showNotification(
                project,
                "测试异常",
                "测试过程中发生错误: ${e.message}",
                NotificationType.ERROR
            )
        }
    }
    
    private fun findAllSketchWindows(): List<SketchToolWindow> {
        val sketchWindows = mutableListOf<SketchToolWindow>()
        
        try {
            // 方法1: 通过Window.getWindows()查找
            Window.getWindows().forEach { window ->
                if (window is SketchToolWindow) {
                    sketchWindows.add(window)
                }
            }
            
            // 方法2: 通过组件层次查找
            SwingUtilities.getWindowAncestor(null)?.let { rootWindow ->
                findSketchWindowsInHierarchy(rootWindow, sketchWindows)
            }
            
        } catch (e: Exception) {
            // 忽略查找过程中的异常
        }
        
        return sketchWindows.distinct()
    }
    
    private fun findSketchWindowsInHierarchy(container: java.awt.Container, result: MutableList<SketchToolWindow>) {
        try {
            if (container is SketchToolWindow) {
                result.add(container)
                return
            }
            
            for (component in container.components) {
                if (component is java.awt.Container) {
                    findSketchWindowsInHierarchy(component, result)
                }
            }
        } catch (e: Exception) {
            // 忽略层次遍历中的异常
        }
    }
    
    private fun getWindowTitle(window: SketchToolWindow): String {
        return try {
            window.getComponent()?.name ?: "未命名SketchWindow"
        } catch (e: Exception) {
            "SketchWindow"
        }
    }
    
    private fun showNotification(
        project: com.intellij.openapi.project.Project,
        title: String,
        content: String,
        type: NotificationType
    ) {
        ApplicationManager.getApplication().invokeLater {
            NotificationGroupManager.getInstance()
                .getNotificationGroup("AutoDev.Memory")
                .createNotification(title, content, type)
                .notify(project)
        }
    }
}
