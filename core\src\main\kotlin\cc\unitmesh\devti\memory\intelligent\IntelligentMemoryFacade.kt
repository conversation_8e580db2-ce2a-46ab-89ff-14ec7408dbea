package cc.unitmesh.devti.memory.intelligent

import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.logger
import com.intellij.openapi.project.Project
import kotlinx.coroutines.*

/**
 * 智能记忆门面 - 统一的记忆处理接口
 * 
 * 这是一个门面类，提供简化的API来访问所有智能记忆功能
 * 设计原则：低侵入性，独立模块，易于集成
 */
@Service(Service.Level.PROJECT)
class IntelligentMemoryFacade(private val project: Project) {
    
    companion object {
        fun getInstance(project: Project): IntelligentMemoryFacade {
            return project.getService(IntelligentMemoryFacade::class.java)
        }
    }
    
    private val logger = logger<IntelligentMemoryFacade>()
    
    // 延迟初始化服务，减少启动时间
    private val memoryEngine by lazy { MemoryProcessingEngine.getInstance(project) }
    private val contextService by lazy { ContextEnrichmentService.getInstance(project) }
    private val exportService by lazy { MarkdownExportService.getInstance(project) }
    private val summaryGenerator by lazy { MemorySummaryGenerator.getInstance(project) }
    private val sketchIntegration by lazy { SketchMemoryIntegration.getInstance(project) }
    
    private val facadeScope = CoroutineScope(Dispatchers.Default + SupervisorJob())
    
    // ==================== 记忆输入接口 ====================
    
    /**
     * 简单的记忆输入接口 - 最常用的API
     */
    suspend fun addMemory(title: String, content: String): ProcessingResult {
        return memoryEngine.processNewInformation(title, content)
    }
    
    /**
     * 带上下文的记忆输入
     */
    suspend fun addMemoryWithContext(
        title: String, 
        content: String, 
        source: String = "user_input",
        context: Map<String, Any> = emptyMap()
    ): ProcessingResult {
        return memoryEngine.processNewInformation(title, content, source, context)
    }
    
    /**
     * 批量添加记忆
     */
    suspend fun addMemories(memories: List<Pair<String, String>>): List<ProcessingResult> {
        return memories.map { (title, content) ->
            memoryEngine.processNewInformation(title, content)
        }
    }
    
    // ==================== 上下文丰富接口 ====================
    
    /**
     * 快速上下文丰富 - 最简单的API
     */
    suspend fun enrichContext(context: String): String {
        return contextService.quickEnrichContext(context)
    }
    
    /**
     * 高级上下文丰富
     */
    suspend fun enrichContextAdvanced(request: ContextEnrichmentRequest): ContextEnrichmentResult {
        return contextService.enrichContext(request)
    }
    
    /**
     * AI增强的上下文丰富
     */
    suspend fun enrichContextWithAI(context: String, maxMemories: Int = 3): String {
        val request = ContextEnrichmentRequest(
            currentContext = context,
            maxMemories = maxMemories,
            relevanceThreshold = 0.3
        )
        val result = contextService.enrichContextWithAI(request)
        return result.enrichedContext
    }
    
    /**
     * 获取相关记忆摘要 - 用于UI显示
     */
    fun getRelatedMemories(context: String, limit: Int = 5): List<MemoryAccessResult> {
        return contextService.getRelatedMemoriesSummary(context, limit)
    }
    
    // ==================== 记忆访问接口 ====================
    
    /**
     * 访问特定记忆
     */
    fun accessMemory(memoryId: String): MemoryAccessResult? {
        return memoryEngine.accessMemory(memoryId)
    }
    
    /**
     * 搜索记忆
     */
    fun searchMemories(query: String, limit: Int = 10): List<MemoryAccessResult> {
        return memoryEngine.getRelatedMemories(query, limit)
    }
    
    // ==================== AI摘要接口 ====================

    /**
     * 生成记忆摘要
     */
    suspend fun generateSummary(title: String, content: String, context: Map<String, Any> = emptyMap()): MemorySummaryResult {
        return summaryGenerator.generateSummary(title, content, context)
    }

    /**
     * 获取记忆的AI摘要
     */
    fun getMemorySummary(memoryId: String): GeneratedSummary? {
        return memoryEngine.getGeneratedSummary(memoryId)
    }

    /**
     * 获取所有AI摘要
     */
    fun getAllMemorySummaries(): Map<String, GeneratedSummary> {
        return memoryEngine.getAllGeneratedSummaries()
    }

    // ==================== SketchWindow集成接口 ====================

    /**
     * 集成SketchWindow
     */
    fun integrateSketchWindow(sketchWindow: cc.unitmesh.devti.sketch.SketchToolWindow) {
        sketchIntegration.integrateWithSketchWindow(sketchWindow)
    }

    /**
     * 记录对话
     */
    fun recordConversation(userMessage: String, aiResponse: String) {
        sketchIntegration.recordConversation(userMessage, aiResponse)
    }

    /**
     * 获取对话历史
     */
    fun getConversationHistory(): List<ConversationEntry> {
        return sketchIntegration.getConversationHistory()
    }

    // ==================== 导出接口 ====================

    /**
     * 简单导出 - 导出所有记忆到默认位置
     */
    suspend fun exportToMarkdown(): ExportResult {
        val config = MemoryExportConfig()
        return exportService.exportAllMemories(config)
    }
    
    /**
     * 自定义导出
     */
    suspend fun exportToMarkdown(config: MemoryExportConfig): ExportResult {
        return exportService.exportAllMemories(config)
    }
    
    /**
     * 导出单个记忆
     */
    suspend fun exportSingleMemory(memoryId: String, outputPath: String? = null): ExportResult {
        return exportService.exportSingleMemory(memoryId, outputPath)
    }
    
    /**
     * 增量导出
     */
    suspend fun incrementalExport(lastExportTime: String): ExportResult {
        val config = MemoryExportConfig()
        return exportService.incrementalExport(config, lastExportTime)
    }
    
    // ==================== 统计和监控接口 ====================
    
    /**
     * 获取记忆统计信息
     */
    fun getMemoryStatistics(): MemoryStatistics {
        // 这里可以实现统计逻辑
        return MemoryStatistics(
            workingMemoryCount = 0,
            shortTermMemoryCount = 0,
            longTermMemoryCount = 0,
            totalProcessed = 0,
            averageImportance = 0.0,
            mostAccessedMemories = emptyList(),
            recentlyCreated = emptyList(),
            categoryDistribution = emptyMap(),
            sourceDistribution = emptyMap()
        )
    }
    
    // ==================== 配置接口 ====================
    
    /**
     * 获取默认配置
     */
    fun getDefaultConfig(): MemoryProcessingConfig {
        return MemoryProcessingConfig()
    }
    
    /**
     * 更新配置
     */
    fun updateConfig(config: MemoryProcessingConfig) {
        // 这里可以实现配置更新逻辑
        logger.info("更新记忆处理配置")
    }
    
    // ==================== 便捷方法 ====================
    
    /**
     * 一键式记忆处理 - 输入信息并立即获取相关上下文
     */
    suspend fun processAndEnrich(title: String, content: String, query: String): Pair<ProcessingResult, String> {
        val processingResult = addMemory(title, content)
        val enrichedContext = enrichContext(query)
        return Pair(processingResult, enrichedContext)
    }
    
    /**
     * 智能建议 - 基于当前上下文提供记忆建议
     */
    suspend fun getSmartSuggestions(context: String): List<String> {
        val relatedMemories = getRelatedMemories(context, 3)
        val suggestions = mutableListOf<String>()
        
        if (relatedMemories.isEmpty()) {
            suggestions.add("建议将当前内容保存为新记忆")
        } else {
            suggestions.add("发现 ${relatedMemories.size} 个相关记忆")
            relatedMemories.forEach { memory ->
                suggestions.add("参考记忆: ${memory.title}")
            }
        }
        
        return suggestions
    }
    
    /**
     * 健康检查 - 检查所有服务是否正常
     */
    fun healthCheck(): Map<String, Boolean> {
        return mapOf(
            "memoryEngine" to try { memoryEngine; true } catch (e: Exception) { false },
            "contextService" to try { contextService; true } catch (e: Exception) { false },
            "exportService" to try { exportService; true } catch (e: Exception) { false }
        )
    }
    
    // ==================== 异步处理接口 ====================
    
    /**
     * 异步添加记忆 - 不阻塞调用者
     */
    fun addMemoryAsync(title: String, content: String, callback: (ProcessingResult) -> Unit = {}) {
        facadeScope.launch {
            try {
                val result = addMemory(title, content)
                callback(result)
            } catch (e: Exception) {
                logger.error("异步添加记忆失败", e)
                callback(ProcessingResult.failure("异步处理失败: ${e.message}"))
            }
        }
    }
    
    /**
     * 异步上下文丰富
     */
    fun enrichContextAsync(context: String, callback: (String) -> Unit) {
        facadeScope.launch {
            try {
                val enrichedContext = enrichContext(context)
                callback(enrichedContext)
            } catch (e: Exception) {
                logger.error("异步上下文丰富失败", e)
                callback(context) // 返回原始上下文
            }
        }
    }
    
    /**
     * 异步导出
     */
    fun exportAsync(config: MemoryExportConfig = MemoryExportConfig(), callback: (ExportResult) -> Unit) {
        facadeScope.launch {
            try {
                val result = exportToMarkdown(config)
                callback(result)
            } catch (e: Exception) {
                logger.error("异步导出失败", e)
                callback(ExportResult.failure("异步导出失败: ${e.message}"))
            }
        }
    }
    
    // ==================== 生命周期管理 ====================
    
    /**
     * 初始化 - 可选的预热操作
     */
    fun initialize() {
        logger.info("初始化智能记忆系统")
        // 预热服务
        facadeScope.launch {
            try {
                memoryEngine
                contextService
                exportService
                logger.info("智能记忆系统初始化完成")
            } catch (e: Exception) {
                logger.error("智能记忆系统初始化失败", e)
            }
        }
    }
    
    /**
     * 清理资源
     */
    fun dispose() {
        logger.info("清理智能记忆系统资源")
        facadeScope.cancel()
    }
}

/**
 * 扩展函数 - 为现有类添加智能记忆功能
 */

/**
 * 为字符串添加记忆处理扩展
 */
suspend fun String.saveAsMemory(project: Project, title: String = "自动生成记忆"): ProcessingResult {
    val facade = IntelligentMemoryFacade.getInstance(project)
    return facade.addMemory(title, this)
}

/**
 * 为字符串添加上下文丰富扩展
 */
suspend fun String.enrichWithMemories(project: Project): String {
    val facade = IntelligentMemoryFacade.getInstance(project)
    return facade.enrichContext(this)
}

/**
 * 静态工具方法
 */
object MemoryUtils {
    
    /**
     * 快速创建记忆处理配置
     */
    fun createConfig(
        enableAI: Boolean = true,
        autoExport: Boolean = true,
        minImportance: Int = 1
    ): MemoryProcessingConfig {
        return MemoryProcessingConfig(
            enableAIEvaluation = enableAI,
            autoExportEnabled = autoExport,
            importanceDecayRate = 0.1
        )
    }
    
    /**
     * 快速创建导出配置
     */
    fun createExportConfig(
        outputPath: String? = null,
        groupByCategory: Boolean = true,
        includeMetadata: Boolean = true
    ): MemoryExportConfig {
        return MemoryExportConfig(
            outputPath = outputPath,
            groupByCategory = groupByCategory,
            includeMetadata = includeMetadata
        )
    }
    
    /**
     * 快速创建上下文丰富请求
     */
    fun createEnrichmentRequest(
        context: String,
        maxMemories: Int = 5,
        threshold: Double = 0.3
    ): ContextEnrichmentRequest {
        return ContextEnrichmentRequest(
            currentContext = context,
            maxMemories = maxMemories,
            relevanceThreshold = threshold
        )
    }
}
