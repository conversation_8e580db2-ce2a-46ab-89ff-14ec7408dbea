package cc.unitmesh.devti.gui.memory

import cc.unitmesh.devti.AutoDevBundle
import cc.unitmesh.devti.memory.MemoryBankService
import cc.unitmesh.devti.memory.MemorySummary
import cc.unitmesh.devti.sketch.ui.MarkdownPreviewHighlightSketch
import com.intellij.icons.AllIcons
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.components.*
import com.intellij.ui.SearchTextField
import com.intellij.util.ui.JBUI
import com.intellij.util.ui.UIUtil
import java.awt.*
import java.awt.event.ActionEvent
import javax.swing.*

/**
 * 记忆银行对话框 - 主界面
 */
class MemoryBankDialog(private val project: Project) : DialogWrapper(project, true) {
    
    private val memoryBankService = MemoryBankService.getInstance(project)
    private val searchField = SearchTextField()
    private val categoryCombo = JComboBox<String>()
    private val memoriesPanel = JPanel()
    private val scrollPane = JBScrollPane()
    private val addButton = JButton("添加记忆", AllIcons.General.Add)
    private val importButton = JButton("导入", AllIcons.Actions.Download)
    private val exportButton = JButton("导出", AllIcons.Actions.Upload)
    
    private var currentMemories = listOf<MemorySummary>()
    
    init {
        title = "记忆银行"
        setSize(800, 600)
        init()
        setupUI()
        loadMemories()
    }
    
    public override fun createCenterPanel(): JComponent {
        val mainPanel = JPanel(BorderLayout())
        mainPanel.preferredSize = Dimension(800, 600)
        
        // 顶部工具栏
        val toolbarPanel = createToolbarPanel()
        mainPanel.add(toolbarPanel, BorderLayout.NORTH)
        
        // 记忆列表面板
        memoriesPanel.layout = BoxLayout(memoriesPanel, BoxLayout.Y_AXIS)
        scrollPane.setViewportView(memoriesPanel)
        scrollPane.border = JBUI.Borders.empty()
        
        mainPanel.add(scrollPane, BorderLayout.CENTER)
        
        // 底部统计信息
        val statsPanel = createStatsPanel()
        mainPanel.add(statsPanel, BorderLayout.SOUTH)
        
        return mainPanel
    }
    
    override fun createActions(): Array<Action> {
        return arrayOf(
            object : AbstractAction("返回") {
                override fun actionPerformed(e: ActionEvent) {
                    close(CANCEL_EXIT_CODE)
                }
            }
        )
    }
    
    private fun setupUI() {
        // 搜索框设置
        searchField.textEditor.emptyText.text = "搜索记忆摘要..."
        searchField.addDocumentListener(object : javax.swing.event.DocumentListener {
            override fun insertUpdate(e: javax.swing.event.DocumentEvent?) = performSearch()
            override fun removeUpdate(e: javax.swing.event.DocumentEvent?) = performSearch()
            override fun changedUpdate(e: javax.swing.event.DocumentEvent?) = performSearch()
        })
        
        // 分类下拉框设置
        updateCategoryCombo()
        categoryCombo.addActionListener { performSearch() }
        
        // 按钮事件
        addButton.addActionListener { showAddMemoryDialog() }
        importButton.addActionListener { showImportDialog() }
        exportButton.addActionListener { showExportDialog() }
    }
    
    private fun createToolbarPanel(): JComponent {
        val panel = JPanel(BorderLayout())
        panel.border = JBUI.Borders.empty(10)
        
        // 搜索和过滤区域
        val searchPanel = JPanel(FlowLayout(FlowLayout.LEFT))
        searchPanel.add(JLabel("搜索:"))
        searchPanel.add(searchField)
        searchPanel.add(Box.createHorizontalStrut(10))
        searchPanel.add(JLabel("分类:"))
        searchPanel.add(categoryCombo)
        
        // 操作按钮区域
        val buttonPanel = JPanel(FlowLayout(FlowLayout.RIGHT))
        buttonPanel.add(addButton)
        buttonPanel.add(importButton)
        buttonPanel.add(exportButton)
        
        panel.add(searchPanel, BorderLayout.WEST)
        panel.add(buttonPanel, BorderLayout.EAST)
        
        return panel
    }
    
    private fun createStatsPanel(): JComponent {
        val panel = JPanel(FlowLayout(FlowLayout.LEFT))
        panel.border = JBUI.Borders.empty(5, 10)
        
        val stats = memoryBankService.getStatistics()
        val statsText = "总计: ${stats["totalMemories"]} 条记忆 | " +
                       "分类: ${stats["categories"]} 个 | " +
                       "最后更新: ${stats["lastUpdated"]}"
        
        val statsLabel = JBLabel(statsText).apply {
            foreground = UIUtil.getContextHelpForeground()
            font = JBUI.Fonts.smallFont()
        }
        
        panel.add(statsLabel)
        return panel
    }
    
    private fun updateCategoryCombo() {
        categoryCombo.removeAllItems()
        categoryCombo.addItem("全部分类")
        memoryBankService.getAllCategories().forEach { category ->
            categoryCombo.addItem(category)
        }
    }
    
    private fun performSearch() {
        val query = searchField.text.trim()
        val selectedCategory = categoryCombo.selectedItem as? String
        
        currentMemories = when {
            query.isNotEmpty() -> memoryBankService.searchMemories(query)
            selectedCategory != null && selectedCategory != "全部分类" -> 
                memoryBankService.getMemoriesByCategory(selectedCategory)
            else -> memoryBankService.getAllMemories()
        }
        
        updateMemoriesDisplay()
    }
    
    private fun loadMemories() {
        // 首次使用时初始化示例数据
        memoryBankService.initializeSampleMemories()

        currentMemories = memoryBankService.getAllMemories()
        updateMemoriesDisplay()
    }
    
    private fun updateMemoriesDisplay() {
        memoriesPanel.removeAll()
        
        if (currentMemories.isEmpty()) {
            val noMemoriesLabel = JBLabel("暂无记忆摘要").apply {
                horizontalAlignment = SwingConstants.CENTER
                foreground = UIUtil.getInactiveTextColor()
            }
            memoriesPanel.add(noMemoriesLabel)
        } else {
            currentMemories.forEach { memory ->
                val memoryCard = EnhancedMemoryCard(project, memory) { updatedMemory ->
                    // 刷新显示
                    loadMemories()
                    updateCategoryCombo()
                }
                memoriesPanel.add(memoryCard)
                memoriesPanel.add(Box.createVerticalStrut(8))
            }
        }
        
        memoriesPanel.revalidate()
        memoriesPanel.repaint()
    }
    
    private fun showAddMemoryDialog() {
        val dialog = AddMemoryDialog(project) { title, content, category, tags, importance ->
            memoryBankService.addMemory(title, content, category, tags, importance)
            loadMemories()
            updateCategoryCombo()
        }
        dialog.show()
    }
    
    private fun showImportDialog() {
        val fileChooser = JFileChooser()
        fileChooser.fileFilter = javax.swing.filechooser.FileNameExtensionFilter("JSON files", "json")
        
        if (fileChooser.showOpenDialog(this.contentPane) == JFileChooser.APPROVE_OPTION) {
            try {
                val file = fileChooser.selectedFile
                val jsonContent = file.readText()
                if (memoryBankService.importFromJson(jsonContent)) {
                    loadMemories()
                    updateCategoryCombo()
                    JOptionPane.showMessageDialog(this.contentPane, "导入成功！", "成功", JOptionPane.INFORMATION_MESSAGE)
                } else {
                    JOptionPane.showMessageDialog(this.contentPane, "导入失败，请检查文件格式！", "错误", JOptionPane.ERROR_MESSAGE)
                }
            } catch (e: Exception) {
                JOptionPane.showMessageDialog(this.contentPane, "导入失败：${e.message}", "错误", JOptionPane.ERROR_MESSAGE)
            }
        }
    }
    
    private fun showExportDialog() {
        val fileChooser = JFileChooser()
        fileChooser.fileFilter = javax.swing.filechooser.FileNameExtensionFilter("JSON files", "json")
        fileChooser.selectedFile = java.io.File("memory-bank-export.json")
        
        if (fileChooser.showSaveDialog(this.contentPane) == JFileChooser.APPROVE_OPTION) {
            try {
                val file = fileChooser.selectedFile
                val jsonContent = memoryBankService.exportToJson()
                file.writeText(jsonContent)
                JOptionPane.showMessageDialog(this.contentPane, "导出成功！", "成功", JOptionPane.INFORMATION_MESSAGE)
            } catch (e: Exception) {
                JOptionPane.showMessageDialog(this.contentPane, "导出失败：${e.message}", "错误", JOptionPane.ERROR_MESSAGE)
            }
        }
    }
}

// 旧的 MemoryCard 类已移除，现在使用 MarkdownMemoryCard
