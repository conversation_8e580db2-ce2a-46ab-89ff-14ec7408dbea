# MCP Marketplace 设计文档

## 概述

MCP Marketplace 是一个集成在 AutoDev IDE 插件中的包管理市场，专门用于浏览、搜索和安装 MCP (Model Context Protocol) 包。它支持多种安装方式，特别针对 NPX 模式进行了优化，可以自动安装依赖并配置 MCP 服务器。

## 功能特性

### 🎯 核心功能
- **包浏览**: 浏览官方和社区 MCP 包
- **智能搜索**: 按名称、描述、分类、作者等搜索
- **一键安装**: 支持 NPX、NPM、Python、Docker 等多种安装方式
- **自动配置**: 安装后自动添加到 MCP 配置中
- **包管理**: 查看已安装包，支持卸载操作

### 🔧 技术特性
- **异步安装**: 后台安装，不阻塞 IDE 操作
- **进度跟踪**: 实时显示安装进度和状态
- **错误处理**: 完善的错误处理和用户反馈
- **缓存机制**: 智能缓存，提升用户体验

## 架构设计

### 📁 目录结构
```
cc.unitmesh.devti.mcp.marketplace/
├── model/                    # 数据模型
│   └── McpPackage.kt        # 包信息、分类、安装状态等
├── service/                 # 服务层
│   ├── McpMarketplaceService.kt    # 市场 API 服务
│   └── McpPackageInstaller.kt      # 包安装管理器
└── ui/                      # UI 组件
    ├── McpMarketplaceDialog.kt     # 主对话框
    ├── McpPackageCard.kt           # 包卡片组件
    └── McpPackageDetailDialog.kt   # 包详情对话框
```

### 🏗️ 核心组件

#### 1. McpMarketplaceService
- 负责与市场 API 通信
- 提供搜索、获取包详情等功能
- 支持离线模式（模拟数据）

#### 2. McpPackageInstaller
- 管理包的安装、卸载
- 支持多种安装类型
- 自动更新 MCP 配置
- 持久化安装状态

#### 3. UI 组件
- **McpMarketplaceDialog**: 主界面，包含搜索、过滤、包列表
- **McpPackageCard**: 包卡片，显示包信息和操作按钮
- **McpPackageDetailDialog**: 包详情页，显示完整信息

## 使用方式

### 🚀 启动市场

#### 方式一：工具栏按钮
1. 在 IDE 主工具栏找到 "MCP Marketplace" 按钮
2. 点击按钮打开市场对话框

#### 方式二：MCP 配置中的按钮
1. 打开 MCP 配置弹窗
2. 点击 "Browse Marketplace" 按钮

#### 方式三：菜单操作
1. 通过 Actions 搜索 "MCP Marketplace"
2. 执行对应的 Action

### 🔍 搜索和浏览

#### 搜索功能
- **文本搜索**: 在搜索框输入关键词
- **分类过滤**: 选择特定分类（开发工具、数据处理等）
- **类型过滤**: 按安装类型过滤（NPX、NPM、Python 等）
- **标签过滤**: 仅显示官方或验证过的包
- **排序选项**: 按相关性、评分、下载量等排序

#### 浏览界面
- **包卡片**: 显示包名、描述、评分、版本等信息
- **状态指示**: 显示包的安装状态
- **快速操作**: 直接在卡片上进行安装/卸载操作

### 📦 包安装

#### NPX 模式安装（推荐）
```bash
# 自动执行的命令示例
npx @modelcontextprotocol/server-filesystem /path/to/directory
```

#### 安装流程
1. 点击包卡片上的 "Install" 按钮
2. 系统检查依赖环境（如 Node.js）
3. 后台执行安装命令
4. 自动添加到 MCP 配置
5. 显示安装结果

#### 自动配置
安装成功后，系统会自动：
- 将包添加到 `mcpServerConfig` 中
- 设置正确的命令和参数
- 配置环境变量（如果需要）
- 启用服务器

### 🛠️ 包管理

#### 查看已安装包
- 在市场中已安装的包会显示 "Installed" 状态
- 可以通过过滤器只显示已安装的包

#### 卸载包
1. 找到已安装的包
2. 点击 "Uninstall" 按钮
3. 确认卸载操作
4. 系统自动从 MCP 配置中移除

## 支持的安装类型

### 🟢 NPX (推荐)
- **优势**: 无需全局安装，始终使用最新版本
- **适用**: Node.js 包
- **命令**: `npx package-name [args]`

### 🔵 NPM
- **优势**: 全局安装，启动速度快
- **适用**: Node.js 包
- **命令**: `npm install -g package-name`

### 🟡 Python
- **优势**: 丰富的 Python 生态
- **适用**: Python 包
- **命令**: `pip install package-name`

### 🟠 Docker
- **优势**: 环境隔离，无依赖冲突
- **适用**: 任何语言的包
- **命令**: `docker run package-name`

### 🔴 其他类型
- **Binary**: 直接下载可执行文件
- **Git**: 从 Git 仓库克隆
- **Custom**: 自定义安装命令

## 配置示例

### 安装后的 MCP 配置
```json
{
  "mcpServers": {
    "@modelcontextprotocol/server-filesystem": {
      "command": "npx",
      "args": ["@modelcontextprotocol/server-filesystem", "/path/to/directory"],
      "env": {},
      "disabled": false
    },
    "@modelcontextprotocol/server-git": {
      "command": "npx",
      "args": ["@modelcontextprotocol/server-git", "--repository", "/path/to/repo"],
      "env": {},
      "disabled": false
    }
  }
}
```

## 开发指南

### 🔧 扩展市场

#### 添加新的包源
1. 修改 `McpMarketplaceService` 中的 API 端点
2. 实现对应的数据格式转换
3. 更新模拟数据（用于离线模式）

#### 支持新的安装类型
1. 在 `InstallType` 枚举中添加新类型
2. 在 `McpPackageInstaller` 中实现安装逻辑
3. 更新 UI 中的图标和显示

#### 自定义 UI 组件
1. 继承现有的卡片组件
2. 实现自定义的显示逻辑
3. 注册到对应的工厂类中

### 🧪 测试

#### 单元测试
- 测试服务层的 API 调用
- 测试安装器的各种安装类型
- 测试数据模型的序列化

#### 集成测试
- 测试完整的安装流程
- 测试 UI 交互
- 测试配置文件的更新

#### 手动测试
- 使用模拟数据测试 UI
- 测试各种网络条件下的行为
- 测试错误处理和恢复

## 最佳实践

### 🎯 用户体验
- **快速响应**: 使用异步操作，避免阻塞 UI
- **清晰反馈**: 提供明确的状态指示和错误信息
- **简化操作**: 一键安装，自动配置

### 🔒 安全考虑
- **包验证**: 优先显示官方和验证过的包
- **权限检查**: 安装前检查必要的权限
- **沙箱执行**: 在安全的环境中执行安装命令

### 📈 性能优化
- **懒加载**: 按需加载包信息和图片
- **缓存策略**: 合理缓存 API 响应和安装状态
- **批量操作**: 支持批量安装和更新

## 故障排除

### 常见问题

#### 1. Node.js 未安装
**问题**: 安装 NPX 包时提示 Node.js 不可用
**解决**: 安装 Node.js 并确保在 PATH 中

#### 2. 网络连接问题
**问题**: 无法连接到市场 API
**解决**: 检查网络连接，或使用离线模式

#### 3. 权限问题
**问题**: 安装时权限不足
**解决**: 使用管理员权限运行 IDE，或选择用户级安装

#### 4. 配置冲突
**问题**: 安装的包与现有配置冲突
**解决**: 检查 MCP 配置，手动解决冲突

### 调试技巧
- 查看 IDE 日志中的错误信息
- 使用开发者模式查看详细的安装过程
- 检查 MCP 配置文件的格式和内容

## 未来规划

### 🚀 即将推出
- **包评论和评分**: 用户可以对包进行评价
- **依赖管理**: 自动处理包之间的依赖关系
- **版本管理**: 支持包的版本升级和回滚
- **离线安装**: 支持离线包的安装

### 🔮 长期目标
- **私有市场**: 支持企业内部的私有包市场
- **包开发工具**: 提供包开发和发布的工具链
- **智能推荐**: 基于使用习惯推荐相关包
- **集成测试**: 包安装后的自动化测试

---

## 贡献指南

欢迎贡献代码、报告问题或提出建议！请参考项目的贡献指南。

## 许可证

本项目采用与 AutoDev 主项目相同的许可证。
